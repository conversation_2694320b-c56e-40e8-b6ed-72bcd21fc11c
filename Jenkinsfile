pipeline {
  agent none
  options {
    skipDefaultCheckout()
  }

  stages {

    stage("docker-build") {
      agent any
      steps {

      slackSend channel: "builds-alerts-pm", color: "warning", message: "${env.JOB_NAME} - #${env.BUILD_NUMBER} Started\n${env.BUILD_URL}"
      checkout scm
            sh "make docker-build"
            archiveArtifacts artifacts: 'docker-tag.txt', onlyIfSuccessful: true
      }
      }

    } // End stages

    post {
          success {
            slackSend channel: "builds-alerts-pm", color: "good", message: "${env.JOB_NAME} - #${env.BUILD_NUMBER} Success after ${currentBuild.duration/1000} sec"
          }
          failure {
            slackSend channel: "builds-alerts-pm", color: "danger", message: "${env.JOB_NAME} - #${env.BUILD_NUMBER} Failed\n${env.BUILD_URL}"
          }
    }

} // End pipeline
