const commonPaths = require('./common-paths');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const path = require('path');
const port = process.env.PORT || 3002;
// const outputPath = path.join(__dirname, 'build');
const isDesktop = process.argv.includes('desktop');
const buildPath = isDesktop ? 'desktop' : 'mobile';
const isDebug = !process.argv.includes('release');
const NODE_ENV = process.env.NODE_ENV || 'development';

const getHostName = () => {
  switch (NODE_ENV) {
    case 'staging':
      return 'https://pml-widgets-stg.paytmmoney.com/';
    case 'beta':
      return 'https://pml-widgets-beta.paytmmoney.com';
    case 'production':
      return 'https://pml-widgets.paytmmoney.com';
    default:
      return 'https://pml-widgets.paytmmoney.com';
  }
};

const config = () => ({
  mode: 'development',
  module: {
    rules: [],
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: '[name].css',
      chunkFilename: '[id].css',
      ignoreOrder: true,
    }),
    new HtmlWebpackPlugin({
      hostName: getHostName(),
      template: `public/index.html`,
      filename: `${buildPath}-index.html`,
      favicon: `public/favicon.ico`,
    }),
  ],
  devtool: 'inline-source-map',
  devServer: {
    port: port,
    static: {
      directory: commonPaths.outputPath,
    },
    historyApiFallback: {
      index: `/${buildPath}-index.html`,
    },
    webSocketServer: false,
  },
});

module.exports = config;
