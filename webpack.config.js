const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const commonConfig = require('./webpack-utils/webpack.common');
// const { initBuildModules } = require('@paytm-money/build-modules/webpack');

const addons = (/* string | string[] */ addonsArg) => {
  let addons = [...[addonsArg]] // Normalize array of addons (flatten)
    .filter(Boolean); // If addons is undefined, filter it out

  return addons.map((addonName) =>
    require(`./webpack-utils/addons/webpack.${addonName}.js`),
  );
};

// class CdnModules {
//   constructor(options) {
//     this.options = options;
//   }
//
//   apply(compiler) {
//     compiler.hooks.compilation.tap('html-webpack-cdn-plugin', compilation => {
//       if (HtmlWebpackPlugin.getHooks) {
//         HtmlWebpackPlugin.getHooks(compilation).alterAssetTagGroups.tapAsync(
//           { name: 'html-webpack-cdn-plugin', stage: Infinity },
//           this.alterAssetTagGroups.bind(this, compiler, compilation),
//         );
//       }
//     });
//   }
//
//   async alterAssetTagGroups(
//     compiler,
//     compilation,
//     { plugin, bodyTags, headTags, ...rest },
//     cb,
//   ) {
//     this.head = headTags || rest.head || [];
//
//     this.head.unshift(
//       {
//         tagName: 'script',
//         attributes: {
//           type: 'importmap',
//         },
//         innerHTML: JSON.stringify({
//           imports: {
//             React:
//               'https://build-modules.paytmmoney.com/build-modules/react/18.2.0/index.js',
//             ReactDOM:
//               'https://build-modules.paytmmoney.com/build-modules/react-dom/18.2.0/index.js',
//             ReactRouterDOM:
//               'https://build-modules.paytmmoney.com/build-modules/react-router-dom/6.15.0/index.js',
//           },
//         }),
//       },
//       {
//         tagName: 'script',
//         attributes: {
//           crossorigin: 'anonymous',
//           src:
//             'https://static.paytmmoney.com/pml-modules/polyfill/v1/modern-browser.js',
//         },
//         voidTag: false,
//       },
//       {
//         tagName: 'script',
//         attributes: {
//           crossorigin: 'anonymous',
//           src:
//             'https://static.paytmmoney.com/pml-modules/polyfill/v1/legacy-browser.js',
//         },
//         voidTag: false,
//       },
//       {
//         tagName: 'link',
//         attributes: {
//           rel: 'modulepreload',
//           fetchpriority: 'high',
//           href:
//             'https://build-modules.paytmmoney.com/build-modules/react/18.2.0/index.js',
//         },
//         voidTag: false,
//       },
//       {
//         tagName: 'link',
//         attributes: {
//           rel: 'modulepreload',
//           fetchpriority: 'high',
//           href:
//             'https://build-modules.paytmmoney.com/build-modules/react-dom/18.2.0/index.js',
//         },
//         voidTag: false,
//       },
//       {
//         tagName: 'link',
//         attributes: {
//           rel: 'modulepreload',
//           fetchpriority: 'high',
//           href:
//             'https://build-modules.paytmmoney.com/build-modules/react-router-dom/6.15.0/index.js',
//         },
//         voidTag: false,
//       },
//     );
//
//     cb();
//   }
// }

module.exports = async (env) => {
  console.log('🚀 ~ env:', env);
  if (!env) {
    throw new Error(buildValidations.ERR_NO_ENV_FLAG);
  }

  const envConfig = require(`./webpack-utils/webpack.${env.env}.js`);
  const mergedConfig = merge(
    commonConfig(),
    envConfig(),
    ...addons(env.addons),
  );
  if (env.env === 'dev') {
    return [mergedConfig];
  }

  return [
    {
      ...mergedConfig,
      plugins: [...mergedConfig.plugins],
      externals: {
        React: 'React',
        ReactDOM: 'ReactDOM',
        ReactRouterDOM: 'ReactRouterDOM',
      },
    },
  ];
};
