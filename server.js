/* eslint-disable no-unused-vars */
const express = require('express');
const path = require('path');

const app = express();
const expressStaticGzip = require('express-static-gzip');
const router = express.Router();

function serveWithNoCacheHeaders(file, res) {
  const filePath = path.join(__dirname, file);
  res.header('Cache-Control', 'private, no-cache, no-store, must-revalidate');
  res.header('Expires', '-1');
  res.header('Pragma', 'no-cache');
  res.sendFile(filePath);
}

app.set('ETag', 'strong');

app.use(
  '/',
  expressStaticGzip(path.join(__dirname, 'build'), {
    urlContains: 'static/',
    setHeaders: (res) =>
      res.setHeader('Cache-Control', 'private, max-age=31536000'),
  }),
);

app.get('/workers/sharedWorker.js', (_, res) => {
  serveWithNoCacheHeaders(
    path.join(__dirname, 'build', 'workers/sharedWorker.js'),
    res,
  );
});

app.get('/*', (req, res) => {
  res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

app.listen(8080, '0.0.0.0', () => console.log('server started at 8080'));
