// ESLint configuration
// http://eslint.org/docs/user-guide/configuring
module.exports = {
  parser: '@babel/eslint-parser',

  extends: [
    'react-app',
    'airbnb',
    'plugin:jsx-a11y/recommended',
    'plugin:css-modules/recommended',
    'prettier',
  ],

  plugins: ['flowtype', 'css-modules', 'prettier'],

  globals: {
    __DEV__: true,
    __ENV__: true,
    __ENV_NAME__: true,
    Cypress: true,
    cy: true,
  },

  env: {
    browser: true,
    commonjs: true,
    es2021: true,
    jest: true,
  },
  rules: {
    // Forbid the use of extraneous packages
    // https://github.com/benmosher/eslint-plugin-import/blob/master/docs/rules/no-extraneous-dependencies.md
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    'react/no-array-index-key': 0,
    'react/jsx-props-no-spreading': 0,
    'import/no-extraneous-dependencies': ['warn', { packageDir: '.' }],
    'react/prop-types': ['off'],
    'import/prefer-default-export': ['off'],
    camelcase: ['off'],
    'css-modules/no-unused-class': ['off'],
    'consistent-return': ['off'],
    'no-nested-ternary': ['off'],
    'prefer-destructuring': ['off'],
    'no-restricted-syntax': ['off'],
    'guard-for-in': ['off'],
    'react/jsx-no-bind': ['off'],
    'no-inner-declarations': ['off'],
    'css-modules/no-undef-class': 0,
    'react/forbid-prop-types': ['off'],
    'react/react-in-jsx-scope': 'off',
    'react/jsx-uses-react': 'off',
    'react/function-component-definition': 'off',

    // Recommend not to leave any console.log in your code
    // Use console.error, console.warn and console.info instead
    // https://eslint.org/docs/rules/no-console
    'no-console': [
      'warn',
      {
        allow: ['warn', 'error', 'info'],
      },
    ],

    'no-debugger': 'off',

    // Prefer destructuring from arrays and objects
    // http://eslint.org/docs/rules/prefer-destructuring
    'prefer-destructuring': [
      'warn',
      {
        VariableDeclarator: {
          array: false,
          object: true,
        },
        AssignmentExpression: {
          array: false,
          object: false,
        },
      },
      {
        enforceForRenamedProperties: false,
      },
    ],

    'import/no-unresolved': [
      'error',
      {
        ignore: [
          '@paytm-h5-common/paytm_common_ui',
          '@paytm-money/utils-frontend',
        ],
      },
    ],

    // Allow .js files to use JSX syntax
    // https://github.com/yannickcr/eslint-plugin-react/blob/master/docs/rules/jsx-filename-extension.md
    'react/jsx-filename-extension': ['warn', { extensions: ['.js', '.jsx'] }],

    // Functional and class components are equivalent from React’s point of view
    // https://github.com/yannickcr/eslint-plugin-react/blob/master/docs/rules/prefer-stateless-function.md
    'react/prefer-stateless-function': 'off',

    // ESLint plugin for prettier formatting
    // https://github.com/prettier/eslint-plugin-prettier
    'prettier/prettier': 'warn',

    'jsx-a11y/no-static-element-interactions': ['off'],
    'jsx-a11y/click-events-have-key-events': ['off'],
    'jsx-a11y/no-noninteractive-element-interactions': ['off'],

    // Ensure <a> tags are valid
    // https://github.com/evcohen/eslint-plugin-jsx-a11y/blob/master/docs/rules/anchor-is-valid.md
    'jsx-a11y/anchor-is-valid': [
      'warn',
      {
        components: ['Link'],
        specialLink: ['to'],
        aspects: ['noHref', 'invalidHref', 'preferButton'],
      },
    ],

    'jsx-a11y/label-has-for': [
      0,
      {
        components: ['Label'],
        required: {
          some: ['nesting', 'id'],
        },
        allowChildren: false,
      },
    ],
  },

  settings: {
    // Allow absolute paths in imports, e.g. import Button from 'components/Button'
    // https://github.com/benmosher/eslint-plugin-import/tree/master/resolvers
    'import/resolver': {
      node: {
        moduleDirectory: ['node_modules', 'src'],
      },
      alias: {
        map: [
          ["@src", "./src"],
          ["@assets", "./src/assets"],
          ["@components", "./src/components"],
          ["@config", "./src/config"],
          ["@context", "./src/context"],
          ["@HOC", "./src/HOC"],
          ["@layout", "./src/layout"],
          ["@query", "./src/query"],
          ["@services", "./src/services"],
          ["@utils", "./src/utils"],
          [
            '@paytm-h5-common/paytm_common_ui',
            'node_modules/@paytm-h5-common/paytm_common_ui',
          ],
          [
            '@paytm-money/utils-frontend',
            'node_modules/@paytm-money/utils-frontend',
          ],
        ],
        extensions: ['.js', '.jsx'],
      },
    },

    'jsx-a11y': {
      components: {},
    },
  },
};
