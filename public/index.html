<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,user-scalable=no"
    />
    <meta
      httpEquiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Paytm Money</title>
    <link rel="preconnect" href="https://static.paytmmoney.com" crossorigin />
    <link
      rel="preconnect"
      href="<%= htmlWebpackPlugin.options.hostName %>"
      crossorigin
    />
  </head>

  <body>
    <script>
      var urlSearchParams = new URLSearchParams(window.location.search);
      var isDarkModeParam = urlSearchParams.get('darkmode');
      console.log("check isDarkModeParam", isDarkModeParam)
      var root = document.getElementsByTagName('html')[0];
      console.log("check root", root, document.getElementsByTagName('html'))
      console.log("check window.matchMedia", window.matchMedia('(prefers-color-scheme: dark)').matches)
      if (isDarkModeParam === 'true') {
        root.setAttribute('class', 'dark');
      }
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        root.setAttribute('class', 'dark');
      }
    </script>
    <div id="root"></div>
  </body>
</html>
