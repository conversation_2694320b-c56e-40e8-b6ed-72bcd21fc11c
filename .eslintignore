# See https://help.github.com/ignore-files/ for more about ignoring files.

# Dependencies
node_modules/

# Compiled output
build/
public

# Test coverage
coverage
tests
*.config.js

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editors and IDEs
.idea
.vscode/*
# !.vscode/settings.json
# !.vscode/tasks.json
# !.vscode/launch.json
# !.vscode/extensions.json


#npm
package-lock.json
yarn-lock.json
yarn.lock

# Cypress e2e testing
cypress/fixtures
cypress/downloads
# Not mandatory as they are already false in config
cypress/videos
cypress/screenshots
