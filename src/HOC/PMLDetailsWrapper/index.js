import React from 'react';

import queryString from 'query-string';
import { useCompanyDetails } from '../../query/stockQuery';
import Loader from '../../components/atoms/CentralLoader/CentralLoader';

function PMLDetailsWrapper({ children, locationHeader, onError }) {
  const query = queryString.parse(window.location?.search);
  const pmlId = query?.id;
  const { isLoading: pmlDetailsLoading, data, isError } = useCompanyDetails(
    pmlId,
    false,
    false,
    !!pmlId,
  );
  if (isError && onError) {
    onError();
  }

  if (isError) return <div>Error</div>;
  if (pmlId && pmlDetailsLoading) return <Loader />;

  if (!data?.data?.results?.length && onError) onError();
  if (!data?.data?.results?.length) return <div>Error</div>;

  return React.cloneElement(children, {
    data: data?.data?.results?.[0],
    locationHeader,
  });
}

export default PMLDetailsWrapper;
