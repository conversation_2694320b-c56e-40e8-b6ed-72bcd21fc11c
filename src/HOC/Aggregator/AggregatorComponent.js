import { useAggregator, useAggregator<PERSON>ey, useMFAggregatorKey } from '../../query/aggregatorQuery';

import RenderPage from './RenderPage';

const AggregatorComponent =
  ({ queryProps, loader }) =>
  (WrappedComponent) => {
    function BaseComponent(props) {
      const useQueryHook = queryProps?.isMF
        ? useMFAggregatorKey
        : queryProps?.queryParams?.keys
          ? useAggregatorKey
          : useAggregator;

      const {
        isLoading,
        data,
        refetch: refetchAggrData,
      } = useQueryHook(queryProps);

      if (isLoading) {
        return loader;
      }

      return (
        <WrappedComponent
          {...props}
          pageComponent={RenderPage}
          pages={data}
          refetchAggrData={refetchAggrData}
        />
      );
    }

    return BaseComponent;
  };

export default AggregatorComponent;
