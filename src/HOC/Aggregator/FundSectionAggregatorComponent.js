import { useFundSectionAggregatorKey } from '../../query/aggregatorQuery';

import RenderPage from './RenderPage';

const FundSectionAggregatorComponent =
  ({ queryProps, loader }) =>
  (WrappedComponent) => {
    function BaseComponent(props) {
      const useQueryHook = useFundSectionAggregatorKey;

      const {
        isLoading,
        data,
        refetch: refetchAggrData,
      } = useQueryHook(queryProps);

      if (isLoading) {
        return loader;
      }

      return (
        <WrappedComponent
          {...props}
          pageComponent={RenderPage}
          pages={data}
          refetchAggrData={refetchAggrData}
        />
      );
    }

    return BaseComponent;
  };

export default FundSectionAggregatorComponent;
