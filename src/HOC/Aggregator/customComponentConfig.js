import React from 'react';
/**
 * @param {JSON} Component json list
 * @param {String} type it represents custom widget type e.g. custom-banner-2_0
 * @returns react component based on the type provided
 */
export const getViewType = (viewConfig, type) => viewConfig[type] || {};

/**
 *
 * @param {Object} view data object of component
 * @param {Object} receivedProps additional data for component like screenName, verticalName
 * @param {Object} viewConfig map of components
 * @returns a react component with all props populated
 */
export const getComponent = (card, receivedProps, viewConfig) => {
  const viewedComponent = getViewType(viewConfig, card);
  const { Component, props } = viewedComponent;
  if (!Component) return null;
  return <Component key={card} {...receivedProps} {...props} />;
};
