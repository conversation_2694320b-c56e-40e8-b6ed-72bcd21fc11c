/* eslint-disable class-methods-use-this */
import { Component } from 'react';

import { sendErrorToBackend } from '../../actions/runtime';
import {
  getStartupParamsAllCallback,
  notifyNativeApp,
} from '../../utils/bridgeUtils';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    try {
      const { isWidget } = this.props;
      sendErrorToBackend({
        level: 'error',
        key: 'fatal_error',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({
          stack: error.stack,
          error: errorInfo,
          url: window.location.href,
        }),
      });

      if (!isWidget) {
        getStartupParamsAllCallback((result) => {
          if (result?.nativeData) {
            const nativeData = JSON.parse(result.nativeData);
            notifyNativeApp({
              flowType:
                BUSINESS_TYPE_MAPPINGS[
                  nativeData?.businessType || nativeData?.meta?.businessType
                ]?.removeFragment || 'removeH5FragmentCombinedHome',
              widgetId: nativeData.widgetType,
            });
          }
        });
      }
    } catch (err) {
      console.error('Error logging failed', err);
    }
  }

  render() {
    const { hasError } = this.state;
    const { children } = this.props;

    if (hasError) {
      return null;
    }

    return children;
  }
}

const withErrorBoundary = (RouteComponent, isWidget = true) =>
  function withErrorBoundaryFunction(props) {
    return (
      <ErrorBoundary isWidget={isWidget}>
        <RouteComponent {...props} />
      </ErrorBoundary>
    );
  };

export default ErrorBoundary;
export { withErrorBoundary };
