/* eslint-disable no-use-before-define */
import React, { useEffect } from 'react';

// import { navigateHome } from '@utils/navigationUtil';


import { SCREEN_TYPE } from '../../config/locationConfig';
import { useGeoLocation } from '../CaptureLocationHOC';
import Loader from '../../components/atoms/CentralLoader/CentralLoader';

import DeviceLocation from '../CaptureLocationHOC/partials/DeviceLocation';

function VerifyUserLocationHOC({
  children,
  showHeader = true,
  fullPage = false,
  goToDashboard = null,
}) {
  const {
    geoLocationPermissionStatus,
    isLocationRequired,
    locationData,
    requestToEnableLocation,
    triggerFlow,
    setTriggerFlow,
    isLoading,
    isError,
    isPermissionRequired,
  } = useGeoLocation();

  useEffect(() => {
    if (!triggerFlow) {
      setTriggerFlow(true);
    }
  }, [triggerFlow]);

  if (showLoader() && isPermissionRequired) {
    return <Loader />;
  }

  if (isError) {
    return children;
  }

  if (
    (isPermissionRequired &&
      geoLocationPermissionStatus === SCREEN_TYPE.LOCATION_ACCESS_LANDING) ||
    geoLocationPermissionStatus === SCREEN_TYPE.PERMISSION_DENIED
  ) {
    return (
      <DeviceLocation
        showHeader={showHeader}
        enableLocation={requestToEnableLocation}
        goToDashboard={checkFxnExe}
        geoLocationPermissionStatus={geoLocationPermissionStatus}
        fullPage={fullPage}
      />
    );
  }

  return React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        locationHeader: isLocationRequired
          ? JSON.stringify(locationData)
          : null,
      });
    }
  });

  function checkFxnExe() {
    // TDOD: navigate to dashboard
    if (goToDashboard) {
      if (typeof goToDashboard === 'function') goToDashboard();
      // else navigateHome();
    } 
    // else navigateHome();
  }

  function showLoader() {
    if (isLoading) return true;
    if (
      geoLocationPermissionStatus === SCREEN_TYPE.CHECKING_LOCATION_PERMISSION
    )
      return true;
    if (geoLocationPermissionStatus === SCREEN_TYPE.IN_PROGRESS) return true;
  }
}

export default VerifyUserLocationHOC;
