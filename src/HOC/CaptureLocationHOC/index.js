import { useContext, createContext } from 'react';

import { useCaptureLocation } from './hooks/useCaptureLocation';

const LocationContext = createContext(null);
function CaptureLocationHOC({ children, isModuleFlow }) {
  const data = useCaptureLocation(isModuleFlow);

  return (
    <LocationContext.Provider
      value={{
        ...data,
      }}
    >
      <div>{children}</div>
    </LocationContext.Provider>
  );
}

function useGeoLocation() {
  return useContext(LocationContext);
}

export { CaptureLocationHOC as default, useGeoLocation };
