import React from 'react';
import cx from 'classnames';
import {Button} from '@paytm-h5-common/paytm_common_ui';


import Header from '../../../../components/molecules/HeaderWrapper';
import { useImageLoader } from '../../../../utils/react';
import If from '../../../../components/atoms/If';
import Shimmer from '../../../../components/atoms/Shimmer';


import deviceLocation from '../../../../assets/deviceLocation.svg';
import { SCREEN_TYPE } from '../../../../config/locationConfig';

import { CONSTANTS, CTA } from './STATICS';

import styles from './index.scss';

function DeviceLocation({
  enableLocation,
  goToDashboard,
  geoLocationPermissionStatus,
  showHeader = true,
  fullPage = false,
}) {
  const { isImageLoaded, image } = useImageLoader({ imgSrc: deviceLocation });
  return (
    <>
      {showHeader && (
        <Header title={CONSTANTS.TITLE} customTitleStyle={styles.title} />
      )}
      <div
        className={cx(styles.container, {
          [styles.fullPage]: fullPage,
        })}
      >
        <div className={styles.filler} />

        <div className={styles.contentWrapper}>
          <If test={!isImageLoaded}>
            <Shimmer type="circle" width="181px" />
          </If>
          <If test={isImageLoaded}>
            <figure className={styles.image}>
              <img src={image} alt="" />
            </figure>
          </If>

          <div className={styles.content}>
            <h1 className={styles.header}>{CONSTANTS.HEADER}</h1>
            <p className={styles.description}>{CONSTANTS.DESCRIPTION}</p>
          </div>
        </div>

        <div className={styles.ctaContainer}>
          <Button
            isPrimary
            label={CTA.ENABLE_LOCATION}
            onClick={enableLocation}
            loading={geoLocationPermissionStatus === SCREEN_TYPE.IN_PROGRESS}
          />
          <Button
            isTextOnly
            label={CTA.GO_TO_DASHBOARD}
            onClick={goToDashboard}
          />
        </div>
      </div>
    </>
  );
}

export default DeviceLocation;
