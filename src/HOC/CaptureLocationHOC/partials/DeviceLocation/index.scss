@import '../../../../commonStyles/variables.scss';

.fullPage {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: var(--background-neutral-inverse);
}

.title {
  margin-top: 0;
  @include typography(heading5B1, #1d2f54);
}

.container {
  height: calc(100vh - 55px);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.filler {
  flex: 0.4 0 auto;
}

.contentWrapper {
  padding: 0 30px;
  flex: 1 0 auto;
}

.image {
  height: 181px;
  img {
    height: 100%;
    width: 100%;
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 35px;
}

.header {
  margin: 0;
  @include typography(heading3, #1d2f54);
}

.description {
  margin: 10px 0 0;
  @include typography(body2, #506d85);
}

.ctaContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 0 20px 24px;
}

.btnCta {
  margin-top: 10px;
  @include typography(heading14, #3e74dd);
}
