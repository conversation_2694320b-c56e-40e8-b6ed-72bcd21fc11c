/* eslint-disable no-use-before-define */
import { useEffect, useState, useMemo } from 'react';

import { useCombinedIr } from '../../../query/readinessQuery';
import { useOrderVerificationSikkimUser } from '../../../query/stockQuery';
import { IR_STATUS_ENUM } from '../../../utils/constants';
import { useGetLocation } from './useGetLocation';

const SIKKIM = 'sikkim';

function useCaptureLocation(isModuleFlow) {
  const [triggerFlow, setTriggerFlow] = useState(false);
  const { irData } = useCombinedIr(!isModuleFlow);
  const isIR = useMemo(
    () => irData?.eqCashIrStatus === IR_STATUS_ENUM.ACTIVE,
    [irData],
  );

  const callApi = isModuleFlow ? false : isIR;

  const { data, isLoading, isError } = useOrderVerificationSikkimUser(callApi);
  const [isLocationRequired, setIsLocationRequired] = useState(false);

  const {
    isPermissionRequired,
    geoLocationPermissionStatus,
    locationData,
    requestToEnableLocation,
  } = useGetLocation({
    isLocationRequired,
    triggerFlow,
    setIsLocationRequired,
  });

  useEffect(() => {
    if (triggerFlow && isIR) {
      if (!isLoading && !isError) {
        const fieldValue = data?.data?.fields?.STATE?.fieldValue;
        if (!fieldValue) return;
        setIsLocationRequired(fieldValue.toLowerCase() === SIKKIM);
      }
    }
  }, [data, triggerFlow]);

  return {
    data,
    isLoading,
    isPermissionRequired,
    isLocationRequired,
    locationData,
    geoLocationPermissionStatus,
    setTriggerFlow,
    triggerFlow,
    isError,
    requestToEnableLocation,
  };
}

export { useCaptureLocation };
