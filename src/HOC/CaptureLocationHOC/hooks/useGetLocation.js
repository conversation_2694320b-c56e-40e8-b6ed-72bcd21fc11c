/* eslint-disable no-use-before-define */
import { useState, useEffect } from 'react';

// import { useToast } from '@components/provider/AppProvider';
import {
  isBridge,
  checkPermission,
  getDeviceLocation,
  isH5,
  reverseGeocodingBridge,
} from '../../../utils/bridgeUtils';

import { SCREEN_TYPE } from '../../../config/locationConfig';
import { useAppStore } from '../../../contexts/AppContextProvider';
import { APPEARANCE_TYPES } from '../../../utils/constants';

function useGetLocation({
  isLocationRequired,
  triggerFlow,
  setIsLocationRequired,
}) {
  const { addSnackBar } = useAppStore();

  const [isPermissionRequired, setPermissionRequired] =
    useState(isLocationRequired);
  const [geoLocationData, setGeoLocationData] = useState({});
  const [locationData, setLocationData] = useState({});

  const [geoLocationPermissionStatus, setGeoLocationPermissionStatus] =
    useState(SCREEN_TYPE.CHECKING_LOCATION_PERMISSION);

  useEffect(() => {
    if (Object.keys(geoLocationData).length && triggerFlow) {
      const { lat, long } = geoLocationData;
      reverseGeocodingBridge(lat, long).then((result) => {
        if (result.error || !result.data) {
          setLocationData({
            lat,
            long,
          });
        } else {
          const {
            data: { subAdmin, admin },
          } = result;
          setLocationData({
            lat,
            long,
            city: subAdmin,
            state: admin,
          });
        }
      });
    }
  }, [geoLocationData, triggerFlow]);

  useEffect(() => {
    if (isLocationRequired && triggerFlow) {
      if (isBridge()) {
        setGeoLocationPermissionStatus(
          SCREEN_TYPE.CHECKING_LOCATION_PERMISSION,
        );
        checkPermission('location').then((res) => {
          const { data } = res;
          if (data.location === 0) {
            setGeoLocationPermissionStatus(SCREEN_TYPE.LOCATION_ACCESS_LANDING);
            setPermissionRequired(true);
          } else {
            setGeoLocationPermissionStatus(SCREEN_TYPE.LOCATION_ACCESS_LANDING);
            setPermissionRequired(false);
            requestToEnableLocation();
          }
        });
      } else {
        setGeoLocationPermissionStatus(SCREEN_TYPE.LOCATION_ACCESS_LANDING);
        setPermissionRequired(true);
        requestToEnableLocation();
      }
    }
  }, [isLocationRequired, triggerFlow]);

  function requestToEnableLocation() {
    setGeoLocationPermissionStatus(SCREEN_TYPE.IN_PROGRESS);
    getDeviceLocation()
      .then((res) => {
        if (res.error) {
          setGeoLocationPermissionStatus(SCREEN_TYPE.PERMISSION_DENIED);
        } else if (res.data === null || res.data.lat === null) {
          addSnackBar({
            message: 'Something went wrong while fetching location!',
            type: APPEARANCE_TYPES.FAIL,
          });
          setIsLocationRequired(false);
          setGeoLocationPermissionStatus(SCREEN_TYPE.SKIP);
        } else if (res.data.long === null) {
          addSnackBar({
            message: 'Something went wrong while fetching location!',
            type: APPEARANCE_TYPES.FAIL,
          });
          setIsLocationRequired(false);
          setGeoLocationPermissionStatus(SCREEN_TYPE.SKIP);
        } else {
          setGeoLocationData(res.data);
          setGeoLocationPermissionStatus(SCREEN_TYPE.ALLOWED);
        }
      })
      .catch((err) => {
        if (!isH5() && (err.state === 'denied' || err.code === 2)) {
          addSnackBar({
            message: 'Check location permission',
            type: APPEARANCE_TYPES.FAIL,
          });
          setGeoLocationPermissionStatus(SCREEN_TYPE.LOCATION_ACCESS_LANDING);
          setPermissionRequired(true);
        } else {
          setGeoLocationPermissionStatus(SCREEN_TYPE.SKIP);
          setIsLocationRequired(false);
        }
      });
  }

  return {
    isPermissionRequired,
    geoLocationData,
    geoLocationPermissionStatus,
    locationData,
    requestToEnableLocation,
  };
}

export { useGetLocation };
