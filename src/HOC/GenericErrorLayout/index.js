import { useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import ErrorScreen from '../../components/molecules/ErrorScreen';
import NoInternetScreen from '../../components/molecules/NoInternetScreen';
import { useBackPress } from '../../hooks/useNativeBackPress';
import { useAppStore } from '../../contexts/AppContextProvider';
import { emptyObj } from '../../utils/commonUtil';
import { CONNECTION_MODE } from '../../utils/constants';
import styles from './index.scss';

const GenericErrorLayout = ({
  header,
  footer,
  offlineHeader,
  children,
  customBackPress,
}) => {
  const {
    connectionStatus,
    updateConnectionStatus,
    showErrorScreenData,
    updateRootErrorScreenView,
  } = useAppStore() || emptyObj;
  const queryClient = useQueryClient();

  const { pushStack } = useBackPress();

  const [headerComponent, setHeaderComponent] = useState(header);

  const handleNoInternetRefresh = () => {
    if (window.navigator.onLine) {
      updateConnectionStatus(CONNECTION_MODE.ONLINE);
    } else {
      updateConnectionStatus(CONNECTION_MODE.OFFLINE);
    }
  };

  const handleErrorRefresh = () => {
    const { retryCallback } = showErrorScreenData;
    if (retryCallback && typeof retryCallback === 'function') {
      retryCallback();
    } else if (
      retryCallback &&
      retryCallback.type === 'query' &&
      retryCallback.queryKeys
    ) {
      retryCallback.queryKeys.forEach((key) => {
        queryClient.refetchQueries(key, {
          exact: true,
        });
      });
    }
    updateRootErrorScreenView({});
  };

  const errorBackPress = () => {
    if (customBackPress) {
      customBackPress();
    } else {
      updateRootErrorScreenView({});
    }
  };

  useEffect(() => {
    if (header) {
      if (showErrorScreenData?.showErrorScreen) {
        setHeaderComponent(
          React.cloneElement(header, {
            onBackClick: errorBackPress,
          }),
        );
        pushStack(errorBackPress);
      } else {
        setHeaderComponent(header);
      }
    } else if (headerComponent) {
      setHeaderComponent(false);
    }
  }, [showErrorScreenData?.showErrorScreen, header]);

  return (
    <>
      {headerComponent || <> </>}
      {connectionStatus === CONNECTION_MODE.OFFLINE ||
      showErrorScreenData?.showErrorScreen ? (
        <>
          {offlineHeader && (
            <div className={styles.offlineHeader}>{offlineHeader}</div>
          )}
          {connectionStatus === CONNECTION_MODE.OFFLINE ? (
            <NoInternetScreen onRefreshClick={handleNoInternetRefresh} />
          ) : (
            <ErrorScreen
              onRefreshClick={handleErrorRefresh}
              message={showErrorScreenData.message}
              errorCode={showErrorScreenData.errorCode}
              retryCallback={!!showErrorScreenData?.retryCallback}
              buttonText={showErrorScreenData?.buttonText}
            />
          )}
        </>
      ) : (
        <> {children} </>
      )}
      {footer || <> </>}
    </>
  );
};

export default GenericErrorLayout;
