
import cx from 'classnames';
import { Outlet } from 'react-router-dom';
import { emptyObj } from '../../../utils/commonUtil';
import { useAppStore } from '../../../contexts/AppContextProvider';
import NativeBackPressContext from '../../../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../../../hooks/useNativeBackPress';
import ErrorPopup from '../../molecules/ErrorPopup';
import SnackbarController from '../../organisms/SnackbarController';
import CentralLoader from '../../atoms/CentralLoader/CentralLoader';
import styles from './index.scss';

const HomeLayout = () => {
  const {
    snackBarList = {},
    removeSnackBar,
    error = {},
    loader,
  } = useAppStore() || emptyObj;

  const BackPress = useNativeBackPress();

  return (
    <NativeBackPressContext.Provider value={BackPress}>
      <main>
        <Outlet />
        <div
          className={cx(styles.toastContainer, {
            [snackBarList?.toastStyle]: snackBarList?.toastStyle,
          })}
        >
          {Object.keys(snackBarList).length ? (
            // eslint-disable-next-line react/jsx-props-no-spreading
            <SnackbarController dismiss={removeSnackBar} {...snackBarList} />
          ) : null}
          {Object.keys(error).length ? (
            // eslint-disable-next-line react/jsx-props-no-spreading
            <ErrorPopup {...error} />
          ) : null}
        </div>

        {loader ? <CentralLoader /> : null}
      </main>
    </NativeBackPressContext.Provider>
  );
};
export default HomeLayout;
