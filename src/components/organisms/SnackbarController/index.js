import { useEffect } from 'react';

import { Snackbar } from '@paytm-h5-common/paytm_common_ui';

import { SNACKBAR_DISMISS_TIME } from '../../../utils/constants';

const SnackbarController = ({
  dismiss,
  message,
  type = 'positive',
  customClass = '',
  TrailingIcon,
  autoHide = true,
  reserveSpaceForStatusBar = false,
  dismissTime = SNACKBAR_DISMISS_TIME,
  dismissCallback = () => {},
}) => {
  useEffect(() => {
    if (autoHide) {
      const timer = setTimeout(() => {
        dismiss();
        dismissCallback();
      }, dismissTime);
      return () => clearTimeout(timer);
    }
  }, []);

  return (
    <Snackbar
      autoHide={autoHide}
      autoHideAfter={dismissTime}
      text={message}
      theme={type}
      customClass={customClass}
      TrailingIcon={TrailingIcon}
      reserveSpaceForStatusBar={reserveSpaceForStatusBar}
    />
  );
};

export default SnackbarController;
