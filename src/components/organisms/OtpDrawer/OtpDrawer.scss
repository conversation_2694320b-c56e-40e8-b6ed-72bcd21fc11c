@import '/src/commonStyles/variables.scss';

.otpContainer {
  max-height: 100vh !important;
}

.otpInputWrapper {
  width: 100%;
}

.otpInputContainer {
  //position: relative;
}

.hiddenInputContainer {
  opacity: 0;
  pointer-events: none;
  width: 1px;
  height: 1px;
  caret-color: transparent;
  user-select: none;
}

.otpBoxContainer {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 12px;
  cursor: pointer;
}

.otpBox {
  width: 48px;
  height: 48px;
  border: 1px solid var(--border-neutral-weak);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  @include typography(heading3B3, var(--text-neutral-strong));
}

.otpBoxSelected {
  border: 1px solid var(--border-primary-strong);
}

.resendContainer {
  display: flex;
  align-items: start;
  justify-content: space-between;
  margin-top: 12px;
  margin-bottom: 16px;
}

.error {
  @include typography(body2B3, var(--text-negative-strong));
}

.resendTimer {
  margin-left: 20px;
  white-space: nowrap;

  @include typography(body2B3, var(--text-neutral-strong));
}

.resendCta {
  margin-left: 20px;
  white-space: nowrap;

  @include typography(body2B3, var(--text-primary-strong));
}
