import {
  getDeviceName,
  getDeviceID,
  getAppVersion,
  getUserId,
  getOsVersion,
  getNormalSSOToken,
} from '../../../utils/coreUtil';
import { getPlatformValue } from '../../../utils/apiUtil';

export const getFormatedData = (otpTransactionDetails, otps) => {
  const formattedOtpTransactionDetails = {
    purchaseSchemeDetails: [
      {
        amount: otpTransactionDetails?.purchaseSchemeDetails[0]?.amount || 0,
        investmentType:
          otpTransactionDetails?.purchaseSchemeDetails[0]?.investmentType || '',
        isin: otpTransactionDetails?.purchaseSchemeDetails[0]?.isin || '',
        sipFrequency: 'DAILY',
        sipStartDate: Date.now().toString(),
        pmAmcCode:
          otpTransactionDetails?.purchaseSchemeDetails[0]?.pmAmcCode || '',
        rtaCode: otpTransactionDetails?.purchaseSchemeDetails[0]?.rtaCode || '',
        twofaAuthDetails: {
          emailId: otpTransactionDetails?.twofaAuthDetails[0]?.emaiL || '',
          contactNo: otpTransactionDetails?.twofaAuthDetails[0]?.mobile || '',
        },
      },
    ],
    userType: 'PML_ON_BSE',
    featureType: 'FRESH_PURCHASE',
    otpDetails: {
      otp: otps,
      uuid: otpTransactionDetails?.otpDetails?.uuid,
    },
    totalAmount: otpTransactionDetails?.totalAmount || 0,
  };
  return formattedOtpTransactionDetails;
};

export const getMaskedEmail = (email) => {
  if (!email) return '';
  const atIndex = email.indexOf('@');
  const username = email.slice(0, atIndex);
  const domain = email.slice(atIndex);
  return `${username.slice(0, 2)}${'*'.repeat(username.length - 4)}${username.slice(-2)}${domain}`;
};

export const maskMobileNumber = (mobileNumber) =>
  `${mobileNumber.slice(0, 2)}****${mobileNumber.slice(-4)}`;

export const otpBody = (otpTransactionDetails) => ({
  purchaseSchemeDetails: [
    {
      amount: otpTransactionDetails?.purchaseSchemeDetails[0]?.amount,
      investmentType:
        otpTransactionDetails?.purchaseSchemeDetails[0]?.investmentType,
      isin: otpTransactionDetails?.purchaseSchemeDetails[0]?.isin,
      pmAmcCode: otpTransactionDetails?.purchaseSchemeDetails[0]?.pmAmcCode,
      rtaCode: otpTransactionDetails?.purchaseSchemeDetails[0]?.rtaCode,
    },
  ],
  userType: 'PML_ON_BSE',
  featureType: 'FRESH_PURCHASE',
  totalAmount: otpTransactionDetails?.purchaseSchemeDetails[0]?.amount,
  otpDetails: {
    uuid: otpTransactionDetails.otpDetails.uuid,
    otp: null,
  },
});
