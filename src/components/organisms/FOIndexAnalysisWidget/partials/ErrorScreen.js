import { isDarkMode } from '../../../../utils/commonUtil';
import Icon, { ICONS_NAME } from '../../../molecules/Icon';
import ERROR_SCREEN_DARK from '../../../../assets/images/empty_state_dark.svg';
import ERROR_SCREEN_LIGHTS from '../../../../assets/images/empty_state.svg';
import styles from './index.scss';

export default function ErrorScreen({ selectedTab, refetchApi }) {
  const errorText = `Latest ${selectedTab} data could not be fetched`;

  return (
    <div className={styles.errorScreen}>
      <img
        src={isDarkMode() ? ERROR_SCREEN_DARK : ERROR_SCREEN_LIGHTS}
        alt="Error"
      />
      <div className={styles.errorText}>{errorText}</div>
      <div className={styles.refreshButton} onClick={refetchApi}>
        <Icon name={ICONS_NAME.REFRESH} width={12} />
        Refresh
      </div>
    </div>
  );
}
