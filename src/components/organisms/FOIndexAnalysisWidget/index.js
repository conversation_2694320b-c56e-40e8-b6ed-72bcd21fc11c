import dayjs from 'dayjs';
import cx from 'classnames';
import { Loader } from '@paytm-h5-common/paytm_common_ui';
import { useState, useMemo } from 'react';
import { isDarkMode } from '../../../utils/commonUtil';
import { THEMES } from '../../../utils/enums';
import ChartJSOIChart from '../CustomBarChart';
import { withErrorBoundary } from '../../../HOC/WidgetErrorBoundary';
import styles from './index.scss';
import {
  formatFOIndexAnalysis,
  formatFNOOIData,
  formatFII_DIIData,
  formatPRFIndicatorsData,
  formatMarketMoversData,
} from './utils';
import Legend from './partials/Legend';
import Button from '../../atoms/Button/Button';
import Icon, { ICONS_NAME } from '../../molecules/Icon';
import Dropdown from '../../molecules/Dropdown';
import StockChange from '../StockChange/StockChange';
import LightViewCandle from '../LightViewCandle';
import LightChartContainer from '../LightChartContainer';
import {
  BAR_CHART_OPTIONS,
  CHART_CONFIG,
  CHART_TYPES,
  TAB_TYPES,
} from './enums';
import {
  useFNOOI,
  useFNOOptionsConfig,
  useFII_DII,
  useFNOPRFIndicators,
  useMarketMovers,
} from '../../../query/fnoQuery';
import MultiSeriesLineChart from '../MultiSeriesLineChart';
import ErrorScreen from './partials/ErrorScreen';
import MultiLinePriceChart from '../MultiLinePriceChart';
import { isPaytmMoney } from '../../../utils/coreUtil';
import {
  openDeepLinkPaytm,
  openDeepLinkPaytmMoney,
} from '../../../utils/bridgeUtils';
import ROUTES from '../../../routes';

const FOIndexAnalysisWidget = (props) => {
  const { data: widgetData, companyPageNavigation: companyNavigationWidget } =
    props;
  const theme = isDarkMode() ? THEMES.DARK : THEMES.LIGHT;
  const formattedData = useMemo(
    () => formatFOIndexAnalysis(widgetData),
    [widgetData],
  );

  const { tabs, indices, buttons, periodTypes } = formattedData?.data || {};

  const [selectedIndex, setSelectedIndex] = useState(
    indices.find((ele) => ele.isDefault),
  );
  const [selectedTab, setSelectedTab] = useState(tabs[0].key);
  const [chartType, setChartType] = useState(CHART_TYPES.CANDLE.key);

  const [selectedPeriod, setSelectedPeriod] = useState(
    periodTypes.find((ele) => ele.isDefault)?.key,
  );

  // Fetch expiry dates from API
  const {
    data: optionsConfig,
    error: optionsConfigError,
    refetch: refetchOptionsConfig,
  } = useFNOOptionsConfig({
    symbol: selectedIndex?.name,
    enabled: true,
  });

  const expiryDates = useMemo(
    () =>
      optionsConfig?.data?.expires.slice(0, 1).map((date) => ({
        label: dayjs(date).format('DD MMM'),
        key: dayjs(date).format('DD-MM-YYYY'),
      })) || [],
    [optionsConfig],
  );
  const [selectedExpiry, setSelectedExpiry] = useState(expiryDates[0]?.key);

  // Fetch FNO OI data
  const {
    data: foData,
    isLoading: isFODataLoading,
    error: foDataError,
    refetch: refetchFOData,
  } = useFNOOI({
    symbol: selectedIndex?.name,
    expiry: selectedExpiry || expiryDates[0]?.key,
    enabled:
      (selectedTab === TAB_TYPES.CHANGE_IN_OI ||
        selectedTab === TAB_TYPES.OPEN_INTEREST) &&
      !!expiryDates.length,
  });

  // Fetch FII/DII data
  const {
    data: fiiData,
    isLoading: isFiiLoading,
    error: fiiDataError,
    refetch: refetchFiiData,
  } = useFII_DII({
    count: -1,
    investmentCategory: 'FII',
    period: selectedPeriod,
    tradeCategory: 'CASH',
    enabled: selectedTab === TAB_TYPES.FII_DII,
  });

  const {
    data: diiData,
    isLoading: isDiiLoading,
    error: diiDataError,
    refetch: refetchDiiData,
  } = useFII_DII({
    count: -1,
    investmentCategory: 'DII',
    period: selectedPeriod,
    tradeCategory: 'CASH',
    enabled: selectedTab === TAB_TYPES.FII_DII,
  });

  const formattedOIData = useMemo(() => formatFNOOIData(foData), [foData]);
  const formattedFIIDIIData = useMemo(() => {
    if (fiiData?.data?.results?.length && diiData?.data?.results?.length) {
      return formatFII_DIIData(fiiData, diiData);
    }
    return {
      labels: [],
      fiiData: [],
      diiData: [],
    };
  }, [fiiData, diiData]);

  // Add this after other useQuery hooks
  const {
    data: prfData,
    isLoading: isPRFLoading,
    error: prfDataError,
    refetch: refetchPRFData,
  } = useFNOPRFIndicators({
    symbol: selectedIndex?.name,
    expiry: dayjs(selectedExpiry || expiryDates[0]?.key, 'DD-MM-YYYY').format(
      'YYYY-MM-DD',
    ),
    interval: '15m',
    exchange: 'NSE',
    enabled:
      (selectedTab === TAB_TYPES.ATM_STRADDLE ||
        selectedTab === TAB_TYPES.PCR) &&
      !!expiryDates.length,
  });

  const formattedPRFData = useMemo(
    () => formatPRFIndicatorsData(prfData, selectedTab),
    [prfData, selectedTab],
  );

  const {
    data: marketMoversData,
    isLoading: isMarketMoversLoading,
    error: marketMoversError,
    refetch: refetchMarketMovers,
  } = useMarketMovers({
    index: selectedIndex?.id,
    enabled: selectedTab === TAB_TYPES.ADVANCE_DECLINE,
  });

  const formattedMarketMoversData = useMemo(
    () => formatMarketMoversData(marketMoversData),
    [marketMoversData],
  );

  const handleIndexChange = (index) => {
    setSelectedIndex(indices[index]);
  };

  const handleExpiryChange = (expiry) => {
    setSelectedExpiry(expiry);
  };

  const handleTabChange = (tab) => {
    setSelectedTab(tab);
    if (tab === TAB_TYPES.PRICE_CHART) {
      setChartType(CHART_TYPES.CANDLE.key);
    }
  };

  const handleChartTypeChange = (type) => {
    setChartType(type);
  };

  const handlePeriodChange = (period) => {
    setSelectedPeriod(period);
  };

  // Update getTabError function
  const getTabError = () => {
    if (
      foDataError &&
      (selectedTab === TAB_TYPES.CHANGE_IN_OI ||
        selectedTab === TAB_TYPES.OPEN_INTEREST)
    )
      return refetchFOData;
    if (
      optionsConfigError &&
      (selectedTab === TAB_TYPES.CHANGE_IN_OI ||
        selectedTab === TAB_TYPES.OPEN_INTEREST)
    )
      return refetchOptionsConfig;
    if (fiiDataError && selectedTab === TAB_TYPES.FII_DII)
      return refetchFiiData;
    if (diiDataError && selectedTab === TAB_TYPES.FII_DII)
      return refetchDiiData;
    if (
      prfDataError &&
      (selectedTab === TAB_TYPES.ATM_STRADDLE || selectedTab === TAB_TYPES.PCR)
    )
      return refetchPRFData;
    if (marketMoversError && selectedTab === TAB_TYPES.ADVANCE_DECLINE)
      return refetchMarketMovers;
    return false;
  };

  const renderContent = () => {
    const refetchApi = getTabError();
    if (refetchApi) {
      return (
        <ErrorScreen
          selectedTab={tabs.find((tab) => tab.key === selectedTab)?.cta}
          refetchApi={refetchApi}
        />
      );
    }
    if (
      selectedTab === TAB_TYPES.CHANGE_IN_OI ||
      selectedTab === TAB_TYPES.OPEN_INTEREST
    ) {
      if (isFODataLoading) {
        return <Loader size="medium" />;
      }
      return (
        <ChartJSOIChart
          data={formattedOIData?.[selectedTab] || {}}
          showLegend={false}
          height={140}
          config={{
            datasets: [
              {
                label: 'Calls OI',
                darkColor: '#B74040',
                lightColor: '#EB4B4B',
                key: 'callsOI',
              },
              {
                label: 'Puts OI',
                darkColor: '#02A85D',
                lightColor: '#2CB079',
                key: 'putsOI',
              },
            ],
          }}
          chartOptions={BAR_CHART_OPTIONS(
            (value) => `${(value / 100000).toLocaleString()} Lacs`,
          )}
        />
      );
    }
    if (selectedTab === TAB_TYPES.PRICE_CHART) {
      if (chartType === CHART_TYPES.CANDLE.key) {
        return (
          <LightViewCandle
            symbol={selectedIndex}
            range="1d"
            isTransparent
            show24hrChart
          />
        );
      }
      return (
        <LightChartContainer
          symbol={selectedIndex}
          range="1d"
          isTransparent
          showHighLow={false}
          chartConfig={CHART_CONFIG}
          height={140}
          show24hrChart
          widgetRoute={ROUTES.FO_INDEX_ANALYSIS}
        />
      );
    }
    if (selectedTab === TAB_TYPES.FII_DII) {
      if (isFiiLoading || isDiiLoading) {
        return <Loader size="medium" />;
      }
      return (
        <ChartJSOIChart
          config={{
            datasets: [
              {
                label: 'FII',
                darkColor: '#B74040',
                lightColor: '#EB4B4B',
                key: 'fiiData',
              },
              {
                label: 'DII',
                darkColor: '#02A85D',
                lightColor: '#2CB079',
                key: 'diiData',
              },
            ],
          }}
          data={formattedFIIDIIData}
          height={140}
          chartOptions={BAR_CHART_OPTIONS((value) => `${value} Cr`)}
        />
      );
    }
    if (
      selectedTab === TAB_TYPES.ATM_STRADDLE ||
      selectedTab === TAB_TYPES.PCR
    ) {
      if (isPRFLoading) {
        return <Loader size="medium" />;
      }
      return (
        <MultiLinePriceChart
          useDefaultColors
          symbol={selectedIndex}
          range="1d"
          isTransparent
          showHighLow={false}
          chartConfig={{
            ...CHART_CONFIG,
            rightPriceScale: {
              ...CHART_CONFIG.rightPriceScale,
              visible: true,
              scaleMargins: {
                top: 0.5,
                bottom: 0.2,
              },
              borderVisible: true,
              borderColor: isDarkMode() ? '#EEEEEE3D' : '#10101021',
              autoScale: true,
              mode: 0,
              alignLabels: true,
              textColor: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
              fontSize: 11,
              priceFormat: {
                type: 'price',
                precision: 2,
                minMove: 0.01,
              },
            },
            leftPriceScale: {
              visible: true,
              scaleMargins: {
                top: 0.1,
                bottom: 0.1,
              },
              borderVisible: true,
              borderColor: isDarkMode() ? '#EEEEEE3D' : '#10101021',
              autoScale: true,
              mode: 0,
              alignLabels: true,
              textColor: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
              fontSize: 11,
              priceFormat: {
                type: 'price',
                precision: 2,
                minMove: 0.01,
              },
            },
          }}
          additionalData={formattedPRFData}
          additionalSeriesColor={isDarkMode() ? '#0A86BF' : '#013DA6'}
          additionalDataLoading={isPRFLoading}
        />
      );
    }
    if (selectedTab === TAB_TYPES.ADVANCE_DECLINE) {
      if (isMarketMoversLoading || !formattedMarketMoversData?.length) {
        return <Loader size="medium" />;
      }
      return (
        <MultiSeriesLineChart
          height={140}
          seriesConfig={formattedMarketMoversData}
          show24hrChart
        />
      );
    }
    return null;
  };

  const getCtaClassName = (button) =>
    cx(styles.cta, {
      [styles.primaryFill]:
        button?.cta?.toLowerCase?.()?.includes('scalper') ||
        button?.cta?.toLowerCase?.()?.includes('charts'),
    });

  const getCtaTextClassName = (button) =>
    cx(styles.buttonTextClassName, {
      [styles.optionScalperText]:
        button?.cta?.toLowerCase?.()?.includes('scalper') ||
        button?.cta?.toLowerCase?.()?.includes('charts'),
    });

  const companyPageNavigation = (stock) => {
    if (companyNavigationWidget) {
      companyNavigationWidget(stock.id);
    } else {
      const instrumentType =
        stock.instrument_type === 'ES'
          ? 'company'
          : stock.instrument_type === 'I'
            ? 'index'
            : stock.instrument_type.toLowerCase();
      const { id } = stock;
      const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}`;
      openDeepLinkPaytmMoney(url);
    }
  };

  const advanceChartPageNavigation = (stock) => {
    const { id, instrument_type } = stock;
    const instrumentType =
      instrument_type === 'ES'
        ? 'company'
        : stock.instrument_type === 'I'
          ? 'index'
          : instrument_type.toLowerCase();

    const url = `https://www.paytmmoney.com/stocks/${instrumentType}/${id}/charts/tradingView`;
    openDeepLinkPaytmMoney(url);
  };

  const onClickHandler = (button, stock) => {
    if (isPaytmMoney()) {
      const scalperUrl = `https://www.paytmmoney.com/stocks/scalper?pmlId=${stock.id}`;
      const optionChainUrl = `https://www.paytmmoney.com/stocks/fno/option-chain/${stock.id}/NIFTY?shouldOpenOnCurrentScreen=true`;
      switch (button.action) {
        case 'option-chain':
          openDeepLinkPaytmMoney(optionChainUrl);
          break;
        case 'charts':
          advanceChartPageNavigation(stock);
          break;
        case 'scalper':
          openDeepLinkPaytmMoney(scalperUrl);
          break;
        default:
          openDeepLinkPaytmMoney(button.deeplink);
          break;
      }
      if (button.action === 'charts') {
        return;
      }
      openDeepLinkPaytmMoney(button.deeplink);
    } else {
      const optionChainUrl = `paytmmp://paytmmoney/stocks/fno/option-chain-v2/${stock.id}/${stock.name}/${stock.exchange}`;
      switch (button.action) {
        case 'option-chain':
          openDeepLinkPaytm(optionChainUrl);
          break;
        case 'charts':
        case 'scalper':
          companyPageNavigation(stock);
          break;
        default:
          openDeepLinkPaytm(button.deeplinkMini);
          break;
      }
    }
  };

  return (
    <div
      className={`${styles.foIndexAnalysisWidget} ${theme === THEMES.DARK ? styles.dark : styles.light}`}
    >
      {/* Header with Index Selector and Price */}
      <div
        className={cx(styles.header, {
          [styles.headerDark]: theme === THEMES.DARK,
        })}
      >
        <div className={styles.indexSelector}>
          <Dropdown
            title={selectedIndex.name}
            options={indices}
            handleSelection={(name, code, type, index) => {
              handleIndexChange(index);
            }}
            customDropdownHeader={styles.dropdownHeader}
            customDropdownList={styles.dropdownList}
            downArrow={
              <Icon
                name={ICONS_NAME.DOWN_ARROW_BLUE}
                width={15}
                className={styles.downArrowStyle}
              />
            }
            activeClassName={styles.active}
          />
        </div>
        <StockChange
          exchange={selectedIndex.exchange}
          segment={selectedIndex.segment}
          securityId={selectedIndex.security_id}
          instrumentType={selectedIndex.instrument_type}
          id={selectedIndex.id}
        />
      </div>

      <div className={styles.tabNavigation}>
        {tabs?.map((tab) => (
          <button
            type="button"
            key={tab.key}
            className={`${styles.tab} ${selectedTab === tab.key ? styles.activeTab : ''}`}
            onClick={() => handleTabChange(tab.key)}
          >
            {tab.cta}
          </button>
        ))}
      </div>

      {/* Only render Legend if there is no error */}
      {getTabError() === false && (
        <Legend
          expiryDates={expiryDates}
          selectedExpiry={selectedExpiry || expiryDates[0]?.key}
          handleExpiryChange={handleExpiryChange}
          selectedTab={selectedTab}
          chartType={chartType}
          onChartTypeChange={handleChartTypeChange}
          selectedPeriod={selectedPeriod}
          onPeriodChange={handlePeriodChange}
          periodTypes={periodTypes}
          label={
            formattedPRFData?.[formattedPRFData.length - 1]?.value?.toFixed(
              2,
            ) || '0.00'
          }
        />
      )}
      <div
        className={styles.chartContainer}
        onClick={() => {
          companyPageNavigation(selectedIndex);
        }}
      >
        {renderContent()}
      </div>

      <div className={styles.actionButtons}>
        {buttons?.map((button, index) => (
          <Button
            key={button?.cta || `button-${index}`}
            buttonText={button?.cta}
            className={getCtaClassName(button)}
            onClickHandler={() => onClickHandler(button, selectedIndex)}
            buttonTextClassName={getCtaTextClassName(button)}
          />
        ))}
      </div>
    </div>
  );
};

export default withErrorBoundary(FOIndexAnalysisWidget);
