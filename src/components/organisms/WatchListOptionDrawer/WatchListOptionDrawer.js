import { FAV_DATA } from '@config/favConfig';
import { useGetAllWatchList } from '@src/query/watchListQuery';
import Icon, { ICONS_NAME } from '@src/components/molecules/Icon';

import Drawer from '@src/components/molecules/Drawer/Drawer';
import styles from './WatchListOptionDrawer.scss';
import Shimmer from '../../atoms/Shimmer';

const WatchListOptionDrawer = ({
  isOpen,
  onClose,
  favStatus,
  handleFavClick,
  handleRemoveFavClick,
  isWidget,
}) => {
  const { data: allWatchListData, isLoading } = useGetAllWatchList();
  const watchlists = allWatchListData?.watchlists || [];

  return (
    <Drawer active={isOpen} showCloseIcon={false} triggerClose={onClose}>
      <div
        onClick={(event) => {
          event.stopPropagation();
        }}
        className={styles.root}
      >
        <div className={styles.drawerHeader}>
          <div className={styles.title}>{FAV_DATA.SELECT_WATCHLIST}</div>
          {!isWidget && (
            <Icon name={ICONS_NAME.CLOSE_ICON} onClick={onClose} width={15} />
          )}
        </div>
        {isLoading ? (
          <>
            {[...Array(4)].map((_, index) => (
              <Shimmer key={index} width="100%" height="20px" />
            ))}
          </>
        ) : (
          watchlists.map((item, index) => (
            <div
              key={item.id}
              onClick={(event) => {
                event.stopPropagation();
                if (favStatus?.ids?.includes(item.id)) {
                  handleRemoveFavClick(item);
                } else {
                  handleFavClick({ ...item, index });
                }
              }}
              className={styles.watchlist}
            >
              <div className={styles.watchlistname}>
                <Icon
                  name={
                    favStatus?.ids?.includes(item.id)
                      ? ICONS_NAME.ADD_TO_WATCHLIST
                      : ICONS_NAME.BOOKMARK_ICON
                  }
                  size={3}
                  byPass
                />
                <span className={styles.name}>{item.name}</span>
              </div>
              <span className={styles.name}>{item.security_count}</span>
            </div>
          ))
        )}
      </div>
    </Drawer>
  );
};

export default WatchListOptionDrawer;
