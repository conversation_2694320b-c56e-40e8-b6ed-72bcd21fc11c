import React from 'react';

import s from './index.scss';

const FirstTradeProgressCardLoader = () => (
  <div className={s.root}>
    <div className={s.progressContainer}>
      <div className={s.nameInfoContainer}>
        <span className={s.name} />
        <span className={s.des} />
      </div>
      <div className={s.pieContainer} />
    </div>
    <div className={s.progressOptions}>
      {[0, 1, 2, 3].map((option) => (
        <div key={option} className={s.progressDetail}>
          <div className={s.info}>
            <span className={s.icon} />
            <div className={s.details}>
              <span className={s.title} />
              <span className={s.subTitle} />
            </div>
          </div>
          <span className={s.cta} />
        </div>
      ))}
    </div>
  </div>
);

export default FirstTradeProgressCardLoader;
