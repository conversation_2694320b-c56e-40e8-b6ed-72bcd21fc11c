@keyframes shimmer {
  0% {
    background-position: -200% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

@mixin shimmer-effect {
  background: linear-gradient(
    90deg,
    var(--background-neutral-medium) 0%,
    var(--background-neutral-weak) 50%,
    var(--background-neutral-medium) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite ease-in-out;
}

.root {
  border-radius: 16px;
  border: 1px solid var(--border-neutral-weak);
  background-color: var(--background-neutral-inverse);

  .progressContainer {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 16px;
    border-bottom: 1px solid var(--border-neutral-weak);

    .nameInfoContainer {
      display: flex;
      flex-direction: column;

      & > * + * {
        margin-top: 8px;
      }

      .name {
        height: 24px;
        width: 160px;
        border-radius: 8px;
        @include shimmer-effect;
      }

      .des {
        height: 16px;
        width: 160px;
        border-radius: 8px;
        @include shimmer-effect;
      }
    }

    .pieContainer {
      height: 48px !important;
      width: 48px !important;
      border-radius: 50%;
      @include shimmer-effect;
    }
  }

  .progressOptions {
    padding: 16px 16px 0;

    .progressDetail {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 16px;

      .info {
        display: flex;
        align-items: center;

        .icon {
          height: 32px;
          width: 32px;
          border-radius: 50%;
          @include shimmer-effect;
        }

        .details {
          display: flex;
          flex-direction: column;
          margin-left: 8px;

          & > * + * {
            margin-top: 4px;
          }

          .title {
            height: 16px;
            width: 100px;
            border-radius: 8px;
            @include shimmer-effect;
          }

          .subTitle {
            height: 16px;
            width: 100px;
            border-radius: 8px;
            @include shimmer-effect;
          }
        }
      }

      .cta {
        height: 32px;
        width: 100px;
        border-radius: 8px;
        @include shimmer-effect;
      }
    }
  }
}
