/* eslint-disable no-underscore-dangle */
import React, { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';

import { useBackPress } from '@src/hooks/useNativeBackPress';

import { removeFromWatchList, useGetAllWatchList } from '@query/watchListQuery';

import { FAV_DATA } from '@config/favConfig';
import Icon, { ICONS_NAME } from '@src/components/molecules/Icon';
import { APPEARANCE_TYPES } from '@utils/constants';
import { getGenericAppHeaders, makeApiPostCall } from '@utils/apiUtil';
import { queryClient } from '@src/provider/ReactQueryProvider';
import { FAV_API_URLS } from '@config/urlConfig';
import WatchListOptionDrawer from '../WatchListOptionDrawer/WatchListOptionDrawer';
import { openDeepLinkPaytmMoney } from '../../../utils/bridgeUtils';
import { fav_message } from './enum';

const FavIcon = ({
  companyDetails,
  commonAnalyticsData = {},
  addRemoveSriptAnalyticsEvent,
  sendPulseEvent = () => {},
  isWidget,
  deeplink,
  useToast,
}) => {
  const [favStatus, setFavStatus] = useState({
    ids: [],
    status: false,
  });
  const [openWatchListDrawer, setOpenWatchListDrawer] = useState(false);
  const { popStack } = useBackPress();
  const { data: allWatchListData } = useGetAllWatchList();
  const toastHook = useToast?.();
  const addToast = toastHook?.addToast;

  const customizeWatchListName = (watchListName) => {
    const firstPart = watchListName.slice(0, 35);
    const remainingPart = watchListName.slice(36);
    if (remainingPart.length) return `${firstPart}...`;
    return watchListName;
  };

  const handleOnSuccessCallback = (companyName, watchlistName) => {
    if (addRemoveSriptAnalyticsEvent) {
      addRemoveSriptAnalyticsEvent({
        ...commonAnalyticsData,
        pmlId: companyDetails?.id,
        remove: 'remove',
        watchlistName,
      });
    }
    addToast({
      message: FAV_DATA.REMOVED_FROM_FAV(companyName, watchlistName),
      type: APPEARANCE_TYPES.SUCCESS,
      customIcon: ICONS_NAME.WHITE_TICKET_WITHOUT_BORDER,
    });
  };

  const { mutate: removeFromWatchListMutate } = removeFromWatchList(
    handleOnSuccessCallback,
  );

  const addToWatchList = useMutation(
    (data) =>
      makeApiPostCall({
        url: FAV_API_URLS.ADD_SECURITY_TO_WATCHLIST(data.watchlist_id),
        body: {
          security_id: data.companyDetails.id,
        },
        headers: getGenericAppHeaders(),
      }),
    {
      onMutate: async (data) => {
        setFavStatus(true);
        const queryKey = ['allWatchlist'];
        await queryClient.cancelQueries(queryKey);
        const previousValue = queryClient.getQueryData(queryKey);
        queryClient.setQueryData(queryKey, (old) => ({
          ...old,
          watchlists: old.watchlists.map((watchlist) => {
            if (watchlist.id === data.watchlist_id) {
              return {
                ...watchlist,
                security_count: watchlist.security_count + 1,
                securities: watchlist.security_count
                  ? [
                      {
                        ...data.companyDetails,
                        created_at: new Date().getTime(),
                      },
                      ...watchlist.securities,
                    ]
                  : [
                      {
                        ...data.companyDetails,
                        created_at: new Date().getTime(),
                      },
                    ],
              };
            }
            return watchlist;
          }),
        }));

        popStack();
        addToast({
          message: fav_message.ADDED_TO_FAV(
            companyDetails.name,
            customizeWatchListName(data.watchlistName),
          ),
          type: APPEARANCE_TYPES.SUCCESS,
          customIcon: ICONS_NAME.WHITE_TICKET_WITHOUT_BORDER,
        });

        if (addRemoveSriptAnalyticsEvent) {
          addRemoveSriptAnalyticsEvent({
            ...commonAnalyticsData,
            pmlId: companyDetails?.id,
            add: 'add',
            watchlistName: data.watchlistName,
          });
        }
        return previousValue;
      },

      onError: (err, variables, previousValue) => {
        setFavStatus(false);
        queryClient.setQueryData(['allWatchlist'], previousValue);
      },

      onSettled: () => {
        queryClient.invalidateQueries(['allWatchlist']);
      },
    },
  );

  useEffect(() => {
    if (companyDetails?.id && allWatchListData?.watchlist_count) {
      const { watchlists = [] } = allWatchListData;
      let checkFav = {
        ids: [],
        status: false,
      };
      watchlists.forEach((watchlist) => {
        if (watchlist?.security_count) {
          const isSecurityExist = watchlist?.securities?.find(
            (security) => security.id === companyDetails.id.toString(),
          );
          if (isSecurityExist) {
            checkFav = {
              ...checkFav,
              status: true,
              ids: [...checkFav.ids, watchlist.id],
            };
          }
        }
      });
      setFavStatus(checkFav);
    }
  }, [companyDetails, allWatchListData]);

  const handleStorageChange = (e) => {
    if (e.key === 'watchlist_updated' && e.newValue === 'true') {
      // Force refetch the watchlist data
      queryClient.refetchQueries(['allWatchlist'], { active: true });
      // Reset the flag
      localStorage.setItem('watchlist_updated', 'false');
      // Remove listener since update is complete
      window.removeEventListener('storage', handleStorageChange);
    }
  };

  const handleFavClick = async (watchlist) => {
    if (watchlist?.security_count >= 50) {
      addToast({
        message: fav_message.STOCKS_FULL_MSG(watchlist.name),
        hideIcon: true,
      });
    } else {
      addToWatchList.mutate({
        watchlist_id: watchlist.id,
        watchlistName: watchlist.name,
        index: watchlist.index,
        companyDetails,
      });
    }
    setOpenWatchListDrawer(false);
    sendPulseEvent('add');
  };

  const handleRemoveFavClick = (watchlist) => {
    removeFromWatchListMutate({
      watchlist_id: watchlist.id,
      watchlistName: watchlist.name,
      companyDetails,
    });
    setOpenWatchListDrawer(false);
    sendPulseEvent('remove');
  };

  const handleWatchlistDrawer = (event) => {
    if (isWidget) {
      event.stopPropagation();
      setOpenWatchListDrawer(true);
    } else {
      // Add storage listener before opening deeplink
      window.addEventListener('storage', handleStorageChange);
      openDeepLinkPaytmMoney(
        encodeURI(
          `paytmmoney:///mini-app?aId=7398c9dbed384124b1a45a9e9bd1d0dc&phoenixPopup=true&pageName=select-watchlist&stockName=${companyDetails.name}&stockId=${companyDetails.id}`,
        ),
      );
    }
  };

  return (
    <>
      <Icon
        name={
          favStatus.status
            ? ICONS_NAME.WATCHLIST_ADD
            : ICONS_NAME.WATCHLIST_REMOVE
        }
        onClick={handleWatchlistDrawer}
        width={20}
        byPass
      />

      <WatchListOptionDrawer
        isOpen={openWatchListDrawer}
        companyDetails={companyDetails}
        favStatus={favStatus}
        handleFavClick={handleFavClick}
        handleRemoveFavClick={handleRemoveFavClick}
        onClose={() => {
          setOpenWatchListDrawer(false);
        }}
        isWidget
      />
    </>
  );
};

export default FavIcon;
