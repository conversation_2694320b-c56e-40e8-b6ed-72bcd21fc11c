import { useState, useEffect, useCallback, useRef } from 'react';
import { LineSeries, createChart } from 'lightweight-charts';
import dayjs from 'dayjs';

import { SERIES_OPTIONS } from './enums';
import { isDarkMode } from '../../../utils/commonUtil';
import { THEMES } from '../../../utils/enums';
import { isPostSession } from '../LightViewCandle/utils';

const useMultiLineSeries = ({
  chartContainerRef,
  showHighLow = false,
  isTransparent,
  seriesConfig = [],
}) => {
  const chartRef = useRef(null);
  const [chartSeries, setChartSeries] = useState([]);
  const theme = isDarkMode() ? THEMES.DARK : THEMES.LIGHT;

  const chartResizeObserver = useCallback(() => {
    if (!chartContainerRef.current) return null;

    const resizeObserver = new ResizeObserver((entries) => {
      if (
        entries.length === 0 ||
        entries[0].target !== chartContainerRef.current
      ) {
        return;
      }
      const newRect = entries[0].contentRect;
      if (chartRef.current) {
        chartRef.current.applyOptions({
          height: newRect.height || 100,
          width: newRect.width || 272,
        });
        chartRef.current.timeScale().fitContent();
      }
    });

    resizeObserver.observe(chartContainerRef.current);
    return resizeObserver;
  }, [chartContainerRef]);

  useEffect(() => {
    if (chartContainerRef.current && seriesConfig?.length) {
      chartRef.current = createChart(chartContainerRef.current, {
        layout: {
          attributionLogo: false,
          background: { color: 'transparent' },
          textColor: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
          fontSize: 11,
        },
        rightPriceScale: {
          visible: true,
          borderVisible: true,
          borderColor: isDarkMode() ? '#EEEEEE3D' : '#10101021',
          autoScale: true,
          mode: 0,
          alignLabels: true,
          textColor: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
          fontSize: 11,
          priceFormat: {
            type: 'price',
            precision: 2,
            minMove: 0.01,
          },
        },
        timeScale: {
          visible: true,
          borderVisible: true,
          timeVisible: true,
          secondsVisible: false,
          allowBoldLabels: false,
          borderColor: isDarkMode() ? '#EEEEEE3D' : '#10101021',
          tickMarkFormatter: (time) => {
            const date = dayjs(time * 1000);
            const now = dayjs();
            if (date.isSame(now, 'day')) {
              return date.format('HH:mm');
            }
            return date.format('DD MMM');
          },
          leftOffset: 12,
          rightOffset: 12,
          fixLeftEdge: true,
          fixRightEdge: isPostSession(dayjs()),
        },
        grid: {
          vertLines: {
            visible: false,
          },
          horzLines: {
            visible: true,
            color: isDarkMode() ? '#EEEEEE3D' : '#10101021',
          },
        },
        crosshair: {
          mode: 2,
          vertLine: {
            visible: false,
            labelVisible: false,
          },
          horzLine: {
            visible: false,
            labelVisible: false,
          },
        },
        handleScroll: {
          mouseWheel: false,
          pressedMouseMove: false,
          horzTouchDrag: false,
          vertTouchDrag: false,
        },
      });
      const resizeObserver = chartResizeObserver();

      // Create series for each configuration
      const newSeries = seriesConfig.map((config) => {
        const series = chartRef.current.addSeries(LineSeries, {
          ...SERIES_OPTIONS,
          color: config.color,
          lastValueVisible: true,
          priceFormat: {
            type: 'price',
            precision: 2,
            minMove: 0.01,
          },
        });
        series.setData(config.data);
        return series;
      });

      setChartSeries(newSeries);
      chartRef.current.timeScale().fitContent();

      return () => {
        resizeObserver?.disconnect();
        if (chartRef.current) {
          chartRef.current.remove();
        }
      };
    }
  }, [
    chartContainerRef,
    theme,
    chartResizeObserver,
    showHighLow,
    isTransparent,
    seriesConfig,
  ]);

  return {
    chartRef,
    chartSeries,
  };
};

export default useMultiLineSeries;
