const lightTheme = {
  layout: {
    textColor: 'black',
    background: {
      type: 'solid',
      color: 'white',
    },
  },
  timeScale: {
    borderVisible: false,
    timeVisible: true,
    shiftVisibleRangeOnNewBar: true,
  },
  rightPriceScale: {
    borderVisible: false,
    visible: false,
  },
  grid: {
    horzLines: {
      visible: false,
    },
    vertLines: {
      visible: false,
    },
  },
  crosshair: {
    horzLine: {
      visible: false,
      labelVisible: false,
    },
    vertLine: {
      visible: true,
      style: 0,
      labelVisible: false,
    },
  },
  //   handleScale: {
  //     mouseWheel: false,
  //     pinch: false,
  //     axisPressedMouseMove: false,
  //     axisDoubleClickReset: false,
  //   },
  //   handleScroll: {
  //     mouseWheel: false,
  //     pressedMouseMove: false,
  //     horzTouchDrag: false,
  //     vertTouchDrag: false,
  //   },
  watermark: {
    visible: false,
  },
};

const darkTheme = {
  layout: {
    background: { color: '#1C1C1E' },
    lineColor: '#2B2B43',
    textColor: '#D9D9D9',
  },
  timeScale: {
    borderVisible: false,
    timeVisible: true,
    barSpacing: 3,
    shiftVisibleRangeOnNewBar: true,
  },
  rightPriceScale: {
    borderVisible: false,
    visible: false,
  },
  grid: {
    horzLines: {
      visible: false,
    },
    vertLines: {
      visible: false,
    },
  },
  crosshair: {
    horzLine: {
      visible: false,
      labelVisible: false,
    },
    vertLine: {
      visible: true,
      style: 0,
      labelVisible: false,
      color: '#758696',
    },
  },
  handleScale: {
    mouseWheel: false,
    pinch: false,
    axisPressedMouseMove: false,
    axisDoubleClickReset: false,
  },
  handleScroll: {
    mouseWheel: false,
    pressedMouseMove: false,
    horzTouchDrag: false,
    vertTouchDrag: false,
  },
  watermark: {
    color: 'rgba(0, 0, 0, 0)',
  },
};

const CHART_PROPERTIES = (theme) => {
  if (theme === 'dark') return darkTheme;
  return lightTheme;
};

const SERIES_OPTIONS = {
  priceLineVisible: false,
  lineWidth: 2,
  lastPriceAnimation: 2,
};

export { CHART_PROPERTIES, SERIES_OPTIONS };
