import { useRef } from 'react';
import useMultiLineSeries from './useMultiLineSeries';

const MultiSeriesLineChart = ({ height = 400, seriesConfig }) => {
  const chartContainerRef = useRef(null);

  useMultiLineSeries({
    chartContainerRef,
    seriesConfig,
  });

  return (
    <div style={{ width: '100%', height }}>
      <div ref={chartContainerRef} style={{ width: '100%', height }} />
    </div>
  );
};

export default MultiSeriesLineChart;
