import { useRef } from 'react';
import useMultiLineSeries from './useMultiLineSeries';

const MultiSeriesLineChart = ({
  height = 400,
  seriesConfig,
  show24hrChart = false,
}) => {
  const chartContainerRef = useRef(null);

  useMultiLineSeries({
    chartContainerRef,
    seriesConfig,
    show24hrChart,
  });

  return (
    <div style={{ width: '100%', height }}>
      <div ref={chartContainerRef} style={{ width: '100%', height }} />
    </div>
  );
};

export default MultiSeriesLineChart;
