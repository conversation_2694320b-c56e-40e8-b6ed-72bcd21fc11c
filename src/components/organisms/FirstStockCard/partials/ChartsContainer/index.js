import React, { useRef } from 'react';
import styles from '../../index.scss';
import Dropdown from '../../../../molecules/Dropdown';
import StockPriceChange from '../../../../molecules/StockPriceChange';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import { WIDGET_TYPE_DATA } from '../../enums';
import LightViewCharts from '../../../LightViewCharts';
import useLightViewCharts from '../../../LightViewCharts/useLightViewCharts';

const ChartsContainer = ({
  stock,
  stockRanges,
  widgetType,
  index,
  timeRanges,
  setStockRanges,
  companyPageNavigation,
  chartClickedEvent,
  rangeClickedEvent,
  ranges,
}) => {
  const chartContainerRef = useRef();
  const { error, loading, chartData, rangeValue } = useLightViewCharts({
    symbol: stock,
    range: stockRanges?.[index]?.code,
    chartContainerRef,
    show24hrChart: true,
  });
  const getQuantity = (quantity) => {
    if (quantity) {
      return (
        <div className={styles.quantityContainer}>
          <Icon
            name={ICONS_NAME.BRIEFCASE}
            width={13}
            className={styles.qtyIcon}
          />
          <span className={styles.qtyText}>{quantity}</span>
        </div>
      );
    }
  };
  return (
    <div className={styles.chartContainer}>
      <div className={styles.stockPriceWrapper}>
        <div className={styles.stockPriceRange}>
          <StockPriceChange
            securityId={stock.sec_id}
            exchange={stock.exchange}
            segment={stock.segment}
            change={rangeValue}
          />
          {WIDGET_TYPE_DATA[widgetType] && (
            <div className={styles.defaultRange}>
              ({ranges?.[0]?.title || '1D'})
            </div>
          )}
        </div>
        {WIDGET_TYPE_DATA[widgetType] ? (
          getQuantity(stock?.metadata?.net_qty || stock?.metadata?.quantity)
        ) : (
          <Dropdown
            title={stockRanges?.[index]?.name}
            options={timeRanges}
            handleSelection={(_, code) => {
              rangeClickedEvent(code);
              const newRange = timeRanges?.find((range) => range.code === code);
              const newStockRanges = [...stockRanges];
              newStockRanges[index] = { ...newRange };
              setStockRanges(newStockRanges);
            }}
            customDropdownHeader={styles.dropdownHeader}
            customDropdownList={styles.dropdownList}
            downArrow={
              <Icon
                name={ICONS_NAME.DOWN_ARROW_ICON}
                size={2}
                className={styles.downArrowStyle}
              />
            }
          />
        )}
      </div>
      <LightViewCharts
        symbol={stock}
        range={stockRanges?.[index]?.code}
        companyPageNavigation={companyPageNavigation}
        error={error}
        loading={loading}
        chartData={chartData}
        chartContainerRef={chartContainerRef}
        chartClickedEvent={chartClickedEvent}
      />
    </div>
  );
};

export default ChartsContainer;
