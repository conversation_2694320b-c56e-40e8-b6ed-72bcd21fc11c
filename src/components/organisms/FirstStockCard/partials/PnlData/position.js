import React from 'react';
import styles from './index.scss';
import { ChangeWithPercentIndianNumberingSystem } from '../../../../molecules/Prices';
import { useStockAndInstrumentFeed } from '../../../../../utils/Equities/hooks';
import { calculatePositionData, getReturns } from './utils';

const PositionPnl = ({
  exchange,
  segment,
  instrumentType,
  securityId,
  id,
  metaData,
}) => {
  const {
    tot_buy_qty_cf,
    tot_sell_qty_cf,
    buy_avg,
    sell_avg,
    product,
    realised_profit,
    net_qty,
    avg_price,
  } = metaData;
  const { ltp, pClose } = useStockAndInstrumentFeed({
    exchange,
    segment,
    securityId,
    instrumentType,
    id,
  });
  const { profit, change } = calculatePositionData({
    ltp,
    netQty: net_qty,
    realisedProfit: realised_profit,
    instrumentType,
    tot_buy_qty_cf,
    tot_sell_qty_cf,
    buy_avg,
    sell_avg,
    pClose,
    product,
    avg_price,
  });

  const getPreChild = () => {
    if (profit > 0) return '+';
    if (profit < 0) return '-';
  };

  return (
    <div className={styles.clickableWrapper}>
      <ChangeWithPercentIndianNumberingSystem
        preChild={getPreChild()}
        className={styles.returnValue}
        changeWithSign
        withRupee
        value={profit}
        percent={change}
        signClassName={styles.returnsPercentage}
      />
    </div>
  );
};

export default PositionPnl;
