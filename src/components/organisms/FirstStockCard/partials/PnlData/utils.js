import { roundValue } from '../../../../../utils/commonUtil';
import {
  INSTRUMENTS,
  PRODUCT_TYPES,
  SEGMENT_TYPES,
} from '../../../../../utils/Equities/enum';

const getPercentageChange = (oldValue, newValue) => {
  if (!oldValue) return NaN;
  const change = newValue - oldValue;
  return (change * 100) / oldValue;
};

export function getReturns({ ltp, pClose, quantity, cost_price }) {
  const yday_value = quantity * pClose;
  const current_value = quantity * ltp;
  const today_returns = current_value - yday_value;
  const today_returns_percent = yday_value
    ? getPercentageChange(yday_value, current_value)
    : 0;

  const cost_value = cost_price ? cost_price * quantity : 0;
  const overall_returns_value = cost_value ? current_value - cost_value : 0;
  const overall_returns_per = cost_value
    ? (overall_returns_value * 100) / cost_value
    : 0;
  return {
    today_returns,
    today_returns_percent,
    overall_returns_value,
    overall_returns_per,
  };
}

const calculateProfitAndLoss = ({
  ltp,
  netQty,
  realisedProfit,
  instrument,
  tot_buy_qty_cf,
  tot_sell_qty_cf,
  buy_avg,
  sell_avg,
  pClose,
  product,
}) => {
  let unrealizedProfit = 0;
  if (
    (tot_buy_qty_cf || tot_sell_qty_cf) &&
    [INSTRUMENTS.OPTIDX, INSTRUMENTS.OPTSTK].indexOf(instrument) > -1 &&
    product === PRODUCT_TYPES.MARGIN
  ) {
    unrealizedProfit = roundValue(ltp - pClose) * netQty;
  } else {
    unrealizedProfit =
      roundValue(ltp - (netQty > 0 ? buy_avg : sell_avg)) * netQty;
  }
  return realisedProfit + unrealizedProfit;
};

const getPosPercentageChange = ({ netQty, ltp, avg_price }) => {
  if (netQty === 0) {
    return null;
  }
  const change = ((ltp - avg_price) / avg_price) * 100;
  return netQty > 0 ? change : -1 * change;
};

export const calculatePositionData = ({
  ltp,
  netQty,
  realisedProfit,
  instrument,
  tot_buy_qty_cf,
  tot_sell_qty_cf,
  buy_avg,
  sell_avg,
  pClose,
  product,
  avg_price,
}) => ({
  profit: calculateProfitAndLoss({
    ltp,
    netQty,
    realisedProfit,
    instrument,
    tot_buy_qty_cf,
    tot_sell_qty_cf,
    buy_avg,
    sell_avg,
    pClose,
    product,
  }),
  change: getPosPercentageChange({
    netQty,
    ltp,
    avg_price,
  }),
});

export const getAvgPrice = ({
  net_qty,
  segment,
  instrument,
  product,
  tot_sell_val_day,
  tot_buy_val_day,
  tot_buy_qty_cf,
  cost_price,
  tot_sell_qty_cf,
  buy_avg,
  sell_avg,
}) => {
  let avg_price;
  if (!net_qty) {
    avg_price = 0;
  } else if (
    net_qty !== 0 &&
    (tot_buy_qty_cf || tot_sell_qty_cf) &&
    segment === SEGMENT_TYPES.DERIVATIVES &&
    [INSTRUMENTS.FUTIDX, INSTRUMENTS.FUTSTK].indexOf(instrument) > -1 &&
    product === PRODUCT_TYPES.MARGIN
  ) {
    const num =
      tot_sell_val_day +
      tot_buy_qty_cf * cost_price -
      (tot_buy_val_day + tot_buy_qty_cf * cost_price);
    avg_price = Math.abs(num) / Math.abs(net_qty);
  } else if (net_qty > 0) {
    avg_price = buy_avg;
  } else {
    avg_price = sell_avg;
  }
  return avg_price;
};
