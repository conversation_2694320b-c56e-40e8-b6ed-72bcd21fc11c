import React from 'react';
import styles from './index.scss';
import { ChangeWithPercentIndianNumberingSystem } from '../../../../molecules/Prices';
import { useStockAndInstrumentFeed } from '../../../../../utils/Equities/hooks';
import { getReturns } from './utils';

const PortfolioPnl = ({
  exchange,
  segment,
  instrumentType,
  securityId,
  id,
  metaData,
}) => {
  const { quantity, cost_price } = metaData;
  const { ltp, pClose } = useStockAndInstrumentFeed({
    exchange,
    segment,
    securityId,
    instrumentType,
    id,
  });
  const { overall_returns_value, overall_returns_per } = getReturns({
    ltp,
    pClose,
    quantity,
    cost_price,
  });
  const getPreChild = () => {
    if (overall_returns_value > 0) return '+';
    if (overall_returns_value < 0) return '-';
  };

  return (
    <div className={styles.clickableWrapper}>
      <ChangeWithPercentIndianNumberingSystem
        preChild={getPreChild()}
        className={styles.returnValue}
        changeWithSign
        withRupee
        value={overall_returns_value}
        percent={overall_returns_per}
        signClassName={styles.returnsPercentage}
      />
    </div>
  );
};

export default PortfolioPnl;
