import cx from 'classnames';
import Button from '../../../../atoms/Button/Button';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import styles from './index.scss';
import { BUTTON_TYPES } from '../../enums';
import FavIcon from '../../../FavIcon/FavIcon';

const CTAContainer = ({
  buttons,
  stock,
  onClickHandler,
  isWidget,
  useToast,
  onCtaClickEvent,
}) => {
  const getCtaText = (button, scrip) =>
    button?.subCta ? (
      <div>
        <div className={styles.buttonCta}>
          {button.cta}{' '}
          {button.type === BUTTON_TYPES.LARGE ? scrip.name.toUpperCase() : ''}
        </div>
        <div className={styles.subText}>{button.subCta}</div>
      </div>
    ) : (
      button?.cta ||
      (button?.imageUrl ? (
        <img src={button.imageUrl} alt={button.imageUrl} />
      ) : (
        <Icon name={ICONS_NAME.BOOKMARK} width="17px" />
      ))
    );

  const getCtaClassName = (button) =>
    cx(styles.cta, {
      [styles.smallButton]: button?.type === BUTTON_TYPES.SMALL,
      [styles.mediumButton]: button?.type === BUTTON_TYPES.MEDIUM,
      [styles.largeButton]:
        button?.type === BUTTON_TYPES.LARGE ||
        button?.type === BUTTON_TYPES.EXTRA_LARGE,
      [styles.greenButton]:
        button?.cta?.toLowerCase?.()?.includes('buy') ||
        button?.cta?.toLowerCase?.()?.includes('activate'), // to do: check before pushing to prod
    });

  return (
    <div className={styles.ctaContainer}>
      {buttons?.map((button, index) => {
        if (button?.action === 'watchlist' && FavIcon) {
          return (
            <div
              key={`watchlist-${index}`}
              className={cx(styles.watchListCta, styles.cta)}
              onClick={() => onCtaClickEvent(index)}
            >
              <FavIcon
                companyDetails={stock}
                iconSize={4.8}
                isWidget={isWidget}
                deeplink={button.deeplink}
                useToast={useToast}
              />
            </div>
          );
        }
        return (
          <Button
            key={button?.cta || `button-${index}`}
            buttonText={getCtaText(button, stock)}
            className={getCtaClassName(button)}
            onClickHandler={() => onClickHandler(button, stock, index)}
            buttonTextClassName={styles.buttonTextClassName}
          />
        );
      })}
    </div>
  );
};

export default CTAContainer;
