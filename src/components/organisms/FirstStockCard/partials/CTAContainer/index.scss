@import '~@paytm-h5-common/paytm_common_ui/styles/colors';
@import '~@paytm-money/utils-frontend/styles/pml-typography';
@import '~@paytm-money/utils-frontend/styles/pml-mixins';

.ctaContainer {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
    gap: 8px;
    align-items: center;

    .buttonCta {
        font-size: 12px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        white-space: pre-wrap;
    }

    .subText{
        font-size: 10px;
        font-weight: 400;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        white-space: pre-wrap;
    }

    .buttonTextClassName {
        font-size: 14px !important;
        color: var(--text-primary-strong);
        font-weight: 500;
    }

    .cta {
        border: 1px solid var(--border-neutral-variant);
        padding: 10px;
        border-radius: 12px;
        background-color: var(--surface-level-1);
        text-align: center;

        font-size: 14px !important;
        color: var(--text-primary-strong);
        font-weight: 500;
    }

    .mediumButton {
        width: 50%;
    }

    .smallButton {
        padding: 10px;
        width: unset !important;

        > span {
        width: 24px;
        height: 24px;
        align-content: center;
        }
    }

    .greenButton {
        background: var(--background-positive-strong);
        border: none;
        > span,
        > div {
            color: var(--text-universal-strong) !important;
        }
    }
}

.watchListCta {
  flex-shrink: 0;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}