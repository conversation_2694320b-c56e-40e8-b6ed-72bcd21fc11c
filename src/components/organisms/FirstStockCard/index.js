/* eslint-disable react-hooks/rules-of-hooks */
import React, {
  useEffect,
  useState,
  useCallback,
  useRef,
  useMemo,
} from 'react';
import { useSwipeable } from 'react-swipeable';
import cx from 'classnames';
import {
  openDeepLinkPaytm,
  openDeepLinkPaytmMoney,
  notifyNativeApp,
} from '@src/utils/bridgeUtils';
import {
  ORDER_PAD,
  BUTTON_TYPES,
  WIDGET_TYPE_DATA,
  MOST_BOUGHT_WIDGET,
} from './enums';
import styles from './index.scss';
import CompanyIcon from '../../atoms/CompanyIcon/CompanyIcon';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../../atoms/CompanyIcon/IconsList';
import Icon, { ICONS_NAME } from '../../molecules/Icon';
import { generateQueryParamsString } from '../../../utils/commonUtil';
import { TRANSACTION_TYPES } from '../OrderPadLite/enum';
import CTAContainer from './partials/CTAContainer';
import PositionPnl from './partials/PnlData/position';
import PortfolioPnl from './partials/PnlData/portfolio';
import ChartsContainer from './partials/ChartsContainer';
import { useFirstStockEvents } from './useFirstStockEvents';
import { getAvgPrice } from './partials/PnlData/utils';
import { chartsFallbackConfig } from '../../../config/chartsFallbackConfig';
import { CHARTS_CONFIG } from '../../../config/urlConfig';
import { getGenericAppHeaders, makeApiGetCall } from '../../../utils/apiUtil';
import { setGlobalMaps } from '../LightViewCharts/utils';
import { BUSINESS_TYPE_MAPPINGS } from '../../../utils/constants';
import { formatMostBoughtMtfWidgetData } from './utils';

const FirstStockCard = (props) => {
  const {
    data: widgetData,
    companyPageNavigation: companyNavigationWidget,
    isWidget = false,
    navigateTo,
    history,
    onOpen,
    useToast,
  } = props;
  const [hideWidget, setHideWidget] = useState(
    sessionStorage.getItem('hideFirstStockCard') || false,
  );
  const [timeRanges, setTimeRanges] = useState([]);

  const formattedData = useMemo(() => {
    if (widgetData?.widgetId === MOST_BOUGHT_WIDGET) {
      return formatMostBoughtMtfWidgetData(widgetData);
    }
    return widgetData;
  }, [widgetData]);

  const {
    stocks,
    title,
    subtitle,
    buttons: rawButtons,
    ranges = [],
    closeButton,
    tickerTexts = [],
    widgetType,
    viewAll,
    businessId,
    businessType,
    subCohortId,
    sectionTitle,
  } = formattedData?.data || {};
  const { isVisible } = closeButton || {};
  const [stockRanges, setStockRanges] = useState();
  const [buttons, setButtons] = useState([]);

  const cardsWrapperRef = useRef(null);
  const {
    onNewTradeWidget,
    onDismissNewTradeWidget,
    onStockClick,
    onViewAllClick,
    onChartClick,
    onTimePeriodChange,
    onCTA1Click,
    onCTA2Click,
    onWidgetScroll,
  } = useFirstStockEvents({
    widgetId: widgetType || widgetData?.widgetId,
    widgetType: widgetType || widgetData?.widgetId,
    businessId,
    businessType,
    subCohortId,
    isMostBought: widgetData?.widgetId === MOST_BOUGHT_WIDGET,
  });

  useEffect(() => {
    const fetchHolidayData = async () => {
      try {
        const { data } = await makeApiGetCall({
          url: CHARTS_CONFIG,
          headers: getGenericAppHeaders(),
        });
        setGlobalMaps(data?.holidaysMap, data?.muhuratMap);
      } catch (err) {
        setGlobalMaps(
          chartsFallbackConfig?.holidaysMap,
          chartsFallbackConfig?.muhuratMap,
        );
      }
    };
    fetchHolidayData();
  }, []);

  const handleSwiping = useCallback((e) => {
    if (!cardsWrapperRef.current) return;
    const delta = e.deltaX;
    cardsWrapperRef.current.scrollLeft -= delta;
  }, []);

  const swipeHandlers = useSwipeable({
    onSwiping: handleSwiping,
    preventScrollOnSwipe: true,
    trackMouse: true,
    trackTouch: true,
    delta: 5,
    swipeDuration: 500,
    touchEventOptions: { passive: false },
  });

  function navigateToBuyScreen(isMainOrderpad, stock) {
    const queryString = generateQueryParamsString({
      transactionType: TRANSACTION_TYPES.BUY,
      id: stock.id,
      securityId: stock.sec_id,
      exchange: stock.exchange,
      name: stock.name,
      segment: stock.segment,
      quantity: 1,
      instrumentType: stock.inst_type,
      isin: stock.isin,
      ...(widgetData?.widgetId === MOST_BOUGHT_WIDGET && {
        productType: stock?.metadata?.product,
      }),
    });

    const route = isMainOrderpad ? ORDER_PAD : '/';

    navigateTo(history, `${route}${queryString}`, {}, 'push');
  }

  function navigateToSIP(stock) {
    const queryString = generateQueryParamsString({
      txnType: TRANSACTION_TYPES.BUY,
      sipOrderType: true,
      id: stock.id,
      securityId: stock.sec_id,
      exchange: stock.exchange,
      name: stock.name,
      segment: stock.segment,
      quantity: 1,
      instrumentType: stock.inst_type,
      isin: stock.isin,
      isSubsequentTrade: false,
    });

    navigateTo(history, `${ORDER_PAD}${queryString}`, {}, 'push');
  }

  useEffect(() => {
    const filteredBtns = rawButtons.filter((ele) => ele);
    const smallButton = filteredBtns.find(
      (button) => button.type === BUTTON_TYPES.SMALL,
    );
    const largeButton = filteredBtns.find(
      (button) => button.type === BUTTON_TYPES.LARGE,
    );
    if (smallButton && largeButton) {
      setButtons([smallButton, largeButton]);
    } else {
      const mediumButtons = filteredBtns.filter(
        (button) => button.type === BUTTON_TYPES.MEDIUM,
      );
      const extraLargeButton = filteredBtns.find(
        (button) => button.type === BUTTON_TYPES.EXTRA_LARGE,
      );
      if (mediumButtons.length === 2) {
        setButtons(mediumButtons);
      } else if (extraLargeButton) {
        setButtons([extraLargeButton]);
      }
    }
  }, [rawButtons]);

  useEffect(() => {
    if (hideWidget) return;
    if (WIDGET_TYPE_DATA[widgetType]) {
      setStockRanges(
        stocks?.map(() => ({
          code: ranges[0].range,
          name: ranges[0].title,
        })) || [],
      );
    } else {
      const defaultRange =
        ranges?.find((range) => range.default) || ranges?.[0];
      setStockRanges(
        stocks?.map(() => ({
          code: defaultRange.range,
          name: defaultRange.title,
        })) || [],
      );
    }
  }, [hideWidget, ranges, stocks, widgetType]);

  const onCtaClickEvent = (index, button) => {
    if (index === 0) onCTA1Click(button.action);
    else onCTA2Click(button.action);
  };

  const onClickHandler = (button, stock, index) => {
    onCtaClickEvent(index, button);
    if (!isWidget) {
      if (button.action === 'orderPad' || button.action === 'mainOrderPad') {
        const instrumentType =
          stock.inst_type === 'ES' ? 'company' : stock.inst_type.toLowerCase();
        const {
          id,
          metadata: { product },
        } = stock;
        const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}?action=place-order&txn_type=B&price=0&product=${product}&order_type=MKT`;
        openDeepLinkPaytmMoney(url);
        return;
      }
      openDeepLinkPaytmMoney(button.deeplink);
    } else {
      switch (button.action) {
        case 'equitySIP':
          navigateToSIP(stock);
          break;
        case 'orderPad':
          navigateToBuyScreen(false, stock);
          onOpen();
          break;
        case 'mainOrderPad':
          navigateToBuyScreen(true, stock);
          break;
        case 'custom':
          openDeepLinkPaytm(button.deeplinkMini);
          break;
        default:
          openDeepLinkPaytm(button.deeplinkMini);
          break;
      }
    }
  };

  const viewAllClicked = () => {
    onViewAllClick();
    if (isWidget) {
      openDeepLinkPaytm(viewAll.deeplinkMini);
    } else {
      openDeepLinkPaytmMoney(viewAll.deeplink);
    }
  };

  useEffect(() => {
    if (hideWidget) return;
    if (ranges) {
      setTimeRanges(() =>
        ranges?.map((range) => ({
          code: range.range,
          name: range.title,
        })),
      );
    }
  }, [hideWidget, ranges]);
  const companyPageNavigation = (stock) => {
    if (companyNavigationWidget) {
      companyNavigationWidget(stock.id);
    } else {
      const instrumentType =
        stock.inst_type === 'ES' ? 'company' : stock.inst_type.toLowerCase();
      const { id } = stock;
      const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}`;
      openDeepLinkPaytmMoney(url);
    }
  };
  const getMetaData = (stock) => {
    const { segment, inst_type, metadata } = stock;
    const {
      tot_buy_qty_cf,
      tot_sell_qty_cf,
      product,
      net_qty,
      tot_sell_val_day,
      tot_buy_val_day,
      cost_price,
      buy_avg,
      sell_avg,
    } = metadata;
    return {
      ...metadata,
      avg_price: getAvgPrice({
        net_qty,
        segment,
        instrument: inst_type,
        product,
        tot_sell_val_day,
        tot_buy_val_day,
        tot_buy_qty_cf,
        cost_price,
        tot_sell_qty_cf,
        buy_avg,
        sell_avg,
      }),
    };
  };

  const onReturnsClicked = (route) => {
    if (isWidget) {
      navigateTo(history, `/${route}`, {}, 'push');
    } else {
      const url = `https://paytmmoney.com/stocks/${route}`;
      openDeepLinkPaytmMoney(url);
    }
  };

  const getReturns = (stock) => {
    if (WIDGET_TYPE_DATA[widgetType]) {
      return (
        <div
          className={styles.statsContainer}
          onClick={() => onReturnsClicked(WIDGET_TYPE_DATA[widgetType].route)}
        >
          <span className={styles.leftText}>{sectionTitle?.text}</span>
          <span className={styles.rightText}>
            {WIDGET_TYPE_DATA[widgetType].id === 'holdings-widget' ? (
              <PortfolioPnl
                exchange={stock.exchange}
                id={stock.id}
                segment={stock.segment}
                instrumentType={stock.inst_type}
                securityId={stock.sec_id}
                metaData={stock.metadata}
              />
            ) : (
              <PositionPnl
                exchange={stock.exchange}
                id={stock.id}
                segment={stock.segment}
                instrumentType={stock.inst_type}
                securityId={stock.sec_id}
                metaData={getMetaData(stock)}
              />
            )}
          </span>
        </div>
      );
    }
  };
  useEffect(() => {
    if (!hideWidget) {
      onNewTradeWidget();
    }
  }, [hideWidget]);

  useEffect(() => {
    const element = cardsWrapperRef.current;
    let scrollTimeout;

    const handleScroll = () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        onWidgetScroll();
      }, 150);
    };

    if (element) {
      element.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (element) {
        element.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeout);
      }
    };
  }, [onWidgetScroll]);

  if (hideWidget) return null;

  return (
    <div
      className={cx(styles.mainContainer, { [styles.isFullPage]: !isWidget })}
    >
      <div className={styles.header}>
        <div className={styles.text}>
          <div className={styles.title}>{title}</div>
          <div className={styles.subtitle}>{subtitle}</div>
        </div>
        {isVisible ? (
          <Icon
            name={ICONS_NAME.CLOSE_ICON}
            className={styles.crossIcon}
            size={4}
            onClick={() => {
              onDismissNewTradeWidget();
              setHideWidget(true);
              sessionStorage.setItem('hideFirstStockCard', true);
              if (!isWidget) {
                notifyNativeApp({
                  flowType:
                    BUSINESS_TYPE_MAPPINGS[businessType]?.removeFragment ||
                    'removeH5FragmentCombinedHome',
                  widgetId: widgetData?.widgetId,
                });
              }
            }}
          />
        ) : null}
      </div>
      <div
        {...swipeHandlers}
        ref={cardsWrapperRef}
        className={styles.cardsWrapper}
      >
        {stocks?.map((stock, index) => (
          <div
            className={cx(styles.cardContainer, {
              [styles.singleCard]: stocks.length === 1,
            })}
            key={index}
          >
            <div className={styles.card}>
              <div className={styles.headerWrapper}>
                <div
                  className={styles.companyDetails}
                  onClick={() => {
                    onStockClick();
                    companyPageNavigation(stock);
                  }}
                >
                  <CompanyIcon
                    name={stock.id}
                    type={COMPANY_ICONS_NAME.STOCKS}
                    className={styles.companyIcon}
                  />
                  <div className={styles.companyName}>{stock.name}</div>
                  <div className={styles.rightIconWrapper}>
                    <Icon
                      name={ICONS_NAME.RIGHT_ICON}
                      size={1.7}
                      iconStyles={styles.rightIconStyles}
                    />
                  </div>
                </div>
              </div>
              <ChartsContainer
                stock={stock}
                stockRanges={stockRanges}
                widgetType={widgetType}
                index={index}
                timeRanges={timeRanges}
                setStockRanges={setStockRanges}
                companyPageNavigation={companyPageNavigation}
                chartClickedEvent={onChartClick}
                rangeClickedEvent={onTimePeriodChange}
                ranges={ranges}
              />
              {getReturns(stock)}
              <CTAContainer
                buttons={buttons}
                stock={stock}
                onClickHandler={onClickHandler}
                isWidget={isWidget}
                useToast={useToast}
                onCtaClickEvent={onCtaClickEvent}
              />
            </div>
            {tickerTexts.length ? (
              <div className={styles.bottomBar}>{tickerTexts[0]}</div>
            ) : null}
          </div>
        ))}
        {viewAll?.cta && (
          <div className={styles.viewAllCard} onClick={viewAllClicked}>
            <div>{viewAll?.cta}</div>
            <div className={styles.iconWrapper}>
              <Icon
                name={ICONS_NAME.RIGHT_ARROW_BLUE}
                size={1.7}
                iconStyles={styles.rightIconStyles}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(FirstStockCard);
