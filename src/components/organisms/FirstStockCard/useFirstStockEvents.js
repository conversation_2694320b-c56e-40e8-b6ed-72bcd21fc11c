import { useRef } from 'react';
import { useAnalyticsEventForWidget } from '../../../hooks/analyticsHooks';
import { EVENT, EVENT_CATGEORY, USER_ACTIONS } from './enums';

const useFirstStockEvents = ({
  widgetId,
  widgetType,
  businessId,
  businessType,
  subCohortId,
  isMostBought = false,
}) => {
  const { sendAnalyticsEventWidget } = useAnalyticsEventForWidget();

  const handleEvents = (params) => {
    sendAnalyticsEventWidget({
      ...params,
      event: EVENT,
      category: EVENT_CATGEORY,
      label2: `${widgetId}, ${widgetType}`,
      label3: `${businessId}, ${businessType}`,
      label4: `${subCohortId}`,
    });
  };

  const events = useRef({
    onNewTradeWidget: () => {
      handleEvents({
        action: isMostBought
          ? USER_ACTIONS.MOSTBOUGHTNEW_WIDGET
          : USER_ACTIONS.NEWTRADE_WIDGET,
      });
    },
    onDismissNewTradeWidget: () => {
      handleEvents({
        action: USER_ACTIONS.DISMISS_NEWTRADE_WIDGET,
      });
    },
    onStockClick: () => {
      handleEvents({
        action: isMostBought
          ? USER_ACTIONS.STOCKCLICK_MOSTBOUGHTNEW
          : USER_ACTIONS.STOCKCLICK_NEWTRADE_WIDGET,
      });
    },
    onTimePeriodChange: (label) => {
      handleEvents({
        action: isMostBought
          ? USER_ACTIONS.TIMEPERIOD_MOSTBOUGHTNEW
          : USER_ACTIONS.TIMEPERIOD_NEWTRADE_WIDGET,
        label,
      });
    },
    onCTA1Click: (label) => {
      handleEvents({
        action: isMostBought
          ? USER_ACTIONS.CTA1CLICK_MOSTBOUGHTNEW
          : USER_ACTIONS.CTA1CLICK_NEWTRADE_WIDGET,
        label,
      });
    },
    onCTA2Click: (label) => {
      handleEvents({
        action: isMostBought
          ? USER_ACTIONS.CTA2CLICK_MOSTBOUGHTNEW
          : USER_ACTIONS.CTA2CLICK_NEWTRADE_WIDGET,
        label,
      });
    },
    onWidgetScroll: () => {
      handleEvents({
        action: isMostBought
          ? USER_ACTIONS.NEWTRADE_MOSTBOUGHTNEW
          : USER_ACTIONS.NEWTRADE_WIDGET_SCROLL,
      });
    },
    onViewAllClick: () => {
      handleEvents({
        action: isMostBought
          ? USER_ACTIONS.VIEWALLCLICK_MOSTBOUGHTNEW
          : USER_ACTIONS.VIEWALLCLICK_NEWTRADE_WIDGET,
      });
    },
    onChartClick: () => {
      handleEvents({
        action: isMostBought
          ? USER_ACTIONS.CHARTCLICK_MOSTBOUGHTNEW
          : USER_ACTIONS.CHARTCLICK_NEWTRADE_WIDGET,
      });
    },
  });

  return events.current;
};

export { useFirstStockEvents };
