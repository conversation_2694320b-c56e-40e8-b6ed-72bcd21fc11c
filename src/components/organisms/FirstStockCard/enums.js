import { BASE_URL } from '../../../config/envConfig';

export const PULSE_STATICS = {
  OPEN_SCREEN_EVENT: 'openScreen',
  CUSTOM_EVENT: 'custom_event',
  ACTION: {
    first_trade: {
      firsttrade_widget: 'firsttrade_widget',
      firsttrade_changestock: 'firsttrade_changestock',
      firsttrade_watchlistclick: 'firsttrade_watchlistclick',
      firsttrade_buyclick: 'firsttrade_buyclick',
      firsttrade_orderpad: 'firsttrade_orderpad',
      firsttrade_investcare: 'firsttrade_investCare_bottom_sheet_displayed',
      firsttrade_InvestCare_error_received:
        'firsttrade_InvestCare_error_received',
      firsttrade_investcare_yes_clicked: 'firsttrade_investcare_yes_clicked',
      firsttrade_investcare_no_clicked: 'firsttrade_investcare_no_clicked',
      firsttrade_order_status: 'firsttrade_order_status',
      firsttrade_land_order_screen: 'firsttrade_land_order_screen',
      firsttrade_select_exchange: 'firsttrade_select_exchange',
      firsttrade_input_qty: 'firsttrade_input_qty',
      firsttrade_charges_link_clicked: 'firsttrade_charges_link_clicked',
      firsttrade_back_click: 'firsttrade_back_click',
      firsttrade_charges_bottomsheet_displayed:
        'firsttrade_charges_bottomsheet_displayed',
      firsttrade_charges_bottomsheet_closed:
        'firsttrade_charges_bottomsheet_closed',
      firsttrade_buy_order_clicked: 'firsttrade_buy_order_clicked',
      firsttrade_insufficient_funds_error_recieved:
        'firsttrade_insufficient_funds_error_recieved',
      firsttrade_add_funds_clicked:
        'firsttrade_insufficient_funds_add_funds_clicked',
      first_trade_order_lotsize_error_received:
        'firsttrade_order_lotsize_error_received',
      first_trade_order_pad_error_received:
        'firsttrade_order_pad_error_received',
      first_trade_Insufficient_fund_bottomsheet_landed:
        'firsttrade_Insufficient_fund_bottomsheet_landed',
      first_trade_Add_fund_clicked: 'firsttrade_Add_fund_clicked',
      first_trade_modify_order_Insufficient_fund_bottomsheet_clicked:
        'firsttrade_modify_order_Insufficient_fund_bottomsheet_clicked',
    },
    subsequent_trade: {
      subsequent_widget: 'subsequent_trade_widget_rendered',
      subsequent_changestock: 'subsequent_changestock',
      subsequent_buyclick: 'subsequent_trade_buy_cta_clicked',
      subsequent_stock_sip_click: 'subsequent_trade_stock_sip_cta_clicked',
      subsequent_orderpad:
        'subsequent_trade_order_pad_entry_from_subsequent_widget',
      subsequent_investcare: 'subsequent_investcare',
      subsequent_order_status: 'subsequent_order_status',
    },
  },
};

export const returns = {
  '1d': 'one_day_return',
  '1w': 'seven_days_return',
  '1m': 'one_month_return',
  '3m': 'three_month_return',
  '6m': 'six_month_return',
  '1y': 'one_year_return',
  '3y': 'three_year_return',
};

export const ORDER_PAD = '/order-pad';
const { EQUITY_PML } = BASE_URL;

export const getChartsImageUrl = (theme = 'light', range = '1w') =>
  `${EQUITY_PML}ssr-charts/v4/price?format=png&mode=${theme}&range=${range}`;

export const GET_CHARTS_IMAGE_NEW = `${EQUITY_PML}ssr-charts/v5/price?mode=light`;

export const BUTTON_TYPES = {
  SMALL: 'small-button',
  MEDIUM: 'medium-button',
  LARGE: 'large-button',
  EXTRA_LARGE: 'extra-large-button',
};

export const CHART_RANGE_MAPPING = {
  '1m': '1mo',
  '1w': '1w',
  '1d': '1d',
};

export const WIDGET_TYPE_DATA = {
  'holdings-widget': {
    id: 'holdings-widget',
    label: 'Your Returns:',
    qty: 'quantity',
    route: 'portfolio',
  },
  'positions-widget': {
    id: 'positions-widget',
    label: 'Position P&L:',
    qty: 'net_qty',
    route: 'positions',
  },
};

export const USER_ACTIONS = {
  NEWTRADE_WIDGET: 'newtrade_widget',
  DISMISS_NEWTRADE_WIDGET: 'dismiss_newtrade_widget',
  STOCKCLICK_NEWTRADE_WIDGET: 'stockclick_newtrade_widget',
  TIMEPERIOD_NEWTRADE_WIDGET: 'timeperiod_newtrade_widget',
  CTA1CLICK_NEWTRADE_WIDGET: 'cta1click_newtrade_widget',
  CTA2CLICK_NEWTRADE_WIDGET: 'cta2click_newtrade_widget',
  NEWTRADE_WIDGET_SCROLL: 'newtrade_widget_scroll',
  VIEWALLCLICK_NEWTRADE_WIDGET: 'viewallclick_newtrade_widget',
  CHARTCLICK_NEWTRADE_WIDGET: 'chartclick_newtrade_widget',
  MOSTBOUGHTNEW_WIDGET: 'mostboughtnew_widget',
  DISMISS_MOSTBOUGHTNEW: 'dismiss_mostboughtnew',
  STOCKCLICK_MOSTBOUGHTNEW: 'stockclick_mostboughtnew',
  TIMEPERIOD_MOSTBOUGHTNEW: 'timeperiod_mostboughtnew',
  CHARTCLICK_MOSTBOUGHTNEW: 'chartclick_mostboughtnew',
  CTA1CLICK_MOSTBOUGHTNEW: 'cta1click_mostboughtnew',
  CTA2CLICK_MOSTBOUGHTNEW: 'cta2click_mostboughtnew',
  NEWTRADE_MOSTBOUGHTNEW: 'newtrade_mostboughtnew',
  VIEWALLCLICK_MOSTBOUGHTNEW: 'viewallclick_mostboughtnew',
  SOCIALPROOFINGCLICK_MOSTBOUGHTNEW: 'socialproofingclick_mostboughtnew',
};

export const EVENT_CATGEORY = 'homescreen';
export const EVENT = 'custom_event';
export const MOST_BOUGHT_WIDGET = 'most-bought-widget';
