@import '~@paytm-h5-common/paytm_common_ui/styles/colors';
@import '~@paytm-money/utils-frontend/styles/pml-typography';
@import '~@paytm-money/utils-frontend/styles/pml-mixins';

.mainContainer {
  display: flex;
  flex-direction: column;
  margin: 16px;
  
  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    
    .text {
      .title {
        font-size: 14px;
        color: var(--text-neutral-strong);
        font-weight: 500;
      }
      .subtitle {
        font-size: 12px;
        color: var(--text-neutral-strong);
        font-weight: 400;
      }
    }
  }
  .viewAllCard {
    min-width: 100px;
    padding: 22px;
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: var(--background-neutral-inverse);
    border-radius: 16px;
    flex-direction: column;
    margin-right: 15px;

    font-size: 14px;
    color: var(--text-primary-strong);
    font-weight: 400;
    line-height: 20px;

    .iconWrapper {
      width: 24px;
      height: 24px;
      justify-content: center;
      align-items: center;
      border-radius: 19px;
      border: 1px solid var(--text-neutral-light);
      align-content: center;
      display: flex;
    }
  }

  .cardsWrapper {
    display: flex;
    width: 100%;
    overflow-x: scroll;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
    user-select: none;
    
    &::-webkit-scrollbar {
      display: none;
    }

    padding-right: 40px;
    .singleCard {
      width: 100%;
    }

    .cardContainer {
      scroll-snap-align: center;
      scroll-snap-stop: normal;
      display: flex;
      flex-direction: column;
      min-width: 272px;
      margin-right: 16px;
      justify-content: space-between;
      position: relative;

      .card {
        border-radius: 16px;
        background-color: var(--surface-level-1);
        z-index: 4;
        // margin-bottom: 20px;
      }
      .bottomBar {
        position: absolute;
        bottom: 0;
        right: 0;
        left: 0;
        padding: 20px 14px 5px;
        background-color: var(--background-offset-strong);
        border-radius: 0px 0px 12px 12px;
        font-size: 10px;
        color: var(--text-neutral-strong);
        font-weight: 400;
      }

      .headerWrapper {
        display: flex;
        flex-direction: column;
        margin: 16px 16px 0;

      .companyDetails {
        display: flex;
        padding: 4px;
        border-radius: 20px;
        background: var(--background-neutral-weak);
        align-items: center;
        margin-bottom: 12px;

        font-size: 14px;
        color: var(--text-neutral-strong);
        font-weight: 400;

        .companyIcon {
          border-radius: 15px;
          background: #FFF;
          display: flex;
          align-items: center;
          margin-right: 8px;
          justify-content: center;

          >img {
            width: 20px;
            height: 20px;
          }
        }

        .companyName {
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          white-space: pre-wrap;
          width: 70%;
        }
        
        .rightIconWrapper {
          margin-left: auto;
        .rightIconStyles {
          margin: 5px 8px;
        }
      }
      }
    }

    .chartContainer {
      display: flex;
      flex-direction: column;

      .stockPriceWrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 16px;

        .dropdownHeader {
          min-width: 70px;
          padding: 6px;
          border-radius: 40px;
          border: 1px solid var(--background-neutral-weak);
          background: var(--surface-level-1);

          >div{
            font-size: 12px !important;
            color: var(--text-neutral-strong) !important;
            font-weight: 400;
          }
        }

        .dropdownList {
          width: 100%;
          padding: 0;
          text-align: center;
          background: var(--surface-level-1);
          border: 1px solid var(--background-neutral-weak);
          box-shadow: 0px 0px 12px 0px var(--background-neutral-weak);
          margin-top: 3px;

          > div{
            color: var(--text-neutral-strong);
            font-size: 12px;
            margin: 0;
            padding: 8px;
          }
        }
      }
      .downArrowStyle {
        margin-left: 4px;
        padding: 5px 3px;
      }
    }
  }
  }
}

.statsContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-top: 1px solid var(--border-neutral-weak);
  border-bottom: 1px solid var(--border-neutral-weak);
  
  .leftText {
    color: var(--text-neutral-medium);
    font-size: 12px;
  }
  
  .rightText {
    color: var(--text-neutral-strong);
    font-size: 12px;
    font-weight: 500;
  }
}

.returnValue {
  font-size: 14px;
  font-weight: 600;
  align-items: center;
}

.isFullPage {
  margin: 0 0 0 16px;
}

.quantityContainer {
  display: flex;
  align-items: baseline;

  .qtyIcon {
    margin-right: 5px;
  }

  .qtyText {
    font-size: 14px;
    color: var(--text-neutral-strong);
    font-weight: 500;
  }
}

.crossIcon {
  margin-right: 16px;
}

.stockPriceRange {
  display: flex;
  align-items: center;

  .defaultRange {
    color: var(--text-neutral-medium);
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
  }
}