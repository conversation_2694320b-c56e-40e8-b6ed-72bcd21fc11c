import { PRODUCT_TYPES } from '../../../utils/Equities/enum';

export const formatMostBoughtMtfWidgetData = (data) => {
  const { data: { meta, widget } = {} } = data;
  const attributes = widget?.attributes || [];

  const attributesMap = attributes.reduce((acc, attr) => {
    acc[attr.name] = attr.value;
    return acc;
  }, {});

  const buttonCtas = [attributesMap?.BUTTON_CTA1, attributesMap?.BUTTON_CTA2];

  const popularStocksList = (attributesMap.POPULAR_STOCKS_LIST || []).map(
    (stock) => ({
      ...stock,
      id: stock.id,
      name: stock.name,
      exchange: stock.exchange,
      segment: stock.segment,
      metadata: {
        product: PRODUCT_TYPES.MTF,
      },
      inst_type: stock.instrument_type,
      sec_id: stock.security_id,
    }),
  );

  return {
    data: {
      stocks: popularStocksList,
      title: attributesMap.TITLE_FIRST_TRADE,
      subtitle: attributesMap.SUB_TITLE_FIRST_TRADE,
      ranges: attributesMap.MOST_BOUGHT_STOCKS_PERF_RANGE,
      closeButton: attributesMap.CROSS_BUTTON,
      viewAll: attributesMap.BUTTON_VIEW_ALL,
      buttons: buttonCtas,
      ...meta,
    },
  };
};
