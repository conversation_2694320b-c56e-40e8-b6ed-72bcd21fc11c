import React, { useState } from 'react';
import cx from 'classnames';

import Icon, { ICONS_NAME } from '../../../../../../molecules/Icon';
import { formatPrice } from '../../../../../../molecules/Prices';

import Summary from './partials/Summary';
import { STATICS } from '../../statics';

import styles from './index.scss';

function OrderCharges({ data }) {
  const [isOpen, setIsOpen] = useState(false);

  const { items, title, total } = data;
  if (!items) {
    return (
      <div className={cx([styles.container, styles.border])}>
        <p className={styles.header}>{title}</p>
        <span className={styles.amount}>₹{formatPrice(total)}</span>
      </div>
    );
  }
  return (
    <div className={styles.border}>
      <div className={cx([styles.container, styles.accContainer])}>
        <div
          className={styles.accHeader}
          onClick={() => setIsOpen((prevState) => !prevState)}
        >
          <p className={styles.header}>{title}</p>
          <Icon
            name={ICONS_NAME.NEXT_BLUE}
            size={3}
            className={cx({
              [styles.opened]: isOpen,
              [styles.closed]: !isOpen,
            })}
          />
        </div>
        <span className={styles.amount}>₹{formatPrice(total)}</span>
      </div>
      {isOpen && <Summary items={items} />}
      {!isOpen && <p className={styles.breakUp}>{STATICS.BREAK_UP}</p>}
    </div>
  );
}

export default OrderCharges;
