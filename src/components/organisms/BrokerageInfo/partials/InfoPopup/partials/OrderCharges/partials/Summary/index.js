import { formatPrice } from '../../../../../../../../molecules/Prices';

import styles from './index.scss';

function Summary({ items }) {
  return (
    <div className={styles.container}>
      {items.map(data => {
        const { header, formula, value } = data;
        if (value === null) return null;
        return (
          <div key={header} className={styles.summaryContainer}>
            <div className={styles.headerContainer}>
              <p className={styles.header}>{header}</p>
              <p className={styles.formula}>({formula})</p>
            </div>
            <p className={styles.value}>₹{formatPrice(value)}</p>
          </div>
        );
      })}
    </div>
  );
}

export default Summary;
