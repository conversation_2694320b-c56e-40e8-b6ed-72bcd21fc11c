.container {
  display: flex;
  justify-content: space-between;
  padding-bottom: 9px;
}

.border {
  border-bottom: 1px dashed #e8edf3;
}

.header {
  margin: 0 5px 0 0;
  font-size: 14px;
  font-weight: 600;
  color: #1d2f54;
}

.amount {
  margin: 0;
  font-size: 14px;
    line-height: 1.43;
  color: #1d2f54;
}

.accHeader {
  display: flex;
  align-content: center;
}

.opened {
  transform: rotate(270deg);
  transition: transform .2s;
}

.closed {
  transform: rotate(90deg);
  transition: transform .2s;
}

.accContainer {
  padding: 10px 0;
}

.breakUp {
  margin: 0 0 10px;
  font-size: 12px;
  line-height: 1.42;
  color: #8ba6c1;
}
