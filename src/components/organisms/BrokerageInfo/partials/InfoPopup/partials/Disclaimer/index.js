/* eslint-disable no-use-before-define */
import React from 'react';
import { STATICS } from '../../../../../OrderPadLite/enum';
import { PRODUCT_TYPES, TRANSACTION_TYPES } from '../../../../../../../utils/Equities/enum';
import { formatPrice } from '../../../../../../molecules/Prices';

import styles from './index.scss';

function Disclaimer({
  auto_sq_off,
  isSikkimUser,
  postData,
  isSl,
  isLimitOrder,
  ...rest
}) {
  const { product_type, transaction_type } = postData;
  const isTypeSell = transaction_type === TRANSACTION_TYPES.SELL;
  const isIntraday = product_type === PRODUCT_TYPES.INTRADAY;
  const isMKTOrder = !isLimitOrder;

  return (
    <p className={styles.disclaimer}>
      {isSikkimUser ? STATICS.SIKKIM_USER : null}
      <br />
      {getDescription()}
    </p>
  );

  function getDescription() {
    // description logic for convertOrder
    // need to show note only if D -> I
    const convertOrder = rest?.convertOrder;
    const productFrom = rest?.productFrom;
    if (convertOrder) {
      if (productFrom === PRODUCT_TYPES.DELIVERY)
        return STATICS.AUTO_SQ_OFF(formatPrice(auto_sq_off));
      if (productFrom === PRODUCT_TYPES.INTRADAY) return null;
      return null;
    }

    if (!isTypeSell) {
      // buy
      if (isIntraday) {
        switch (true) {
          case isLimitOrder && isSl:
          case isMKTOrder && isSl:
          case isLimitOrder:
            return STATICS.AUTO_SQ_OFF(formatPrice(auto_sq_off));
          case isMKTOrder:
          default:
            return STATICS.INTRADAY_MKT(formatPrice(auto_sq_off));
        }
      } else {
        switch (true) {
          case isLimitOrder && isSl:
          case isLimitOrder:
            return null;
          case isMKTOrder:
          case isMKTOrder && isSl:
          default:
            return STATICS.MARKET;
        }
      }
    } else if (isIntraday) {
      switch (true) {
        case isLimitOrder && isSl:
        case isMKTOrder && isSl:
          return STATICS.SELL_INTRADAY_SL(formatPrice(auto_sq_off));
        case isLimitOrder && !isSl:
          return STATICS.AUTO_SQ_OFF(formatPrice(auto_sq_off));
        case isMKTOrder:
        default:
          return STATICS.INTRADAY_MKT(formatPrice(auto_sq_off));
      }
    } else {
      // sell
      switch (true) {
        case isLimitOrder && isSl:
        case isMKTOrder && isSl:
          return STATICS.SELL_SL;
        case isLimitOrder && !isSl:
          return null;
        case isMKTOrder:
        default:
          return STATICS.MARKET;
      }
    }
  }
}

export default Disclaimer;
