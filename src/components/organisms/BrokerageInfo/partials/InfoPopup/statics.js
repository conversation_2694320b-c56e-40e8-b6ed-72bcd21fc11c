const STATICS = {
  TOTAL: ' Total',
  DISCLAIMER:
    'Note: In case of both delivery buy / sell of same stock on same day, intraday charges will be applicable for the minimum of both quantities',
  BREAK_UP: 'View Break-up',
  AUTO_SQ_OFF: price =>
    `Auto square off charge of Rs ${price} + GST charges of will be applicable if position is not squared off upto square off time`,
  SIKKIM_USER:
    'Customers residing in Sikkim are exempt from Stamp Duty charges.',
  MARKET:
    'Charges shown are computed based on LTP + 3% to account for market volatility',
  SELL_SL: 'Charges shown are computed considering trigger price',
  INTRADAY_MKT: price =>
    `Charges shown are computed based on LTP + 3% to account for market volatility.Auto square off charge of Rs ${price} + GST charges of will be applicable if position is not squared off upto square off time.`,
  SELL_INTRADAY_SL: price => `Charges shown are computed considering trigger price.
Auto square off charge of Rs ${price} + GST charges of will be applicable if position is not squared off upto square off time`,
};

export { STATICS };
