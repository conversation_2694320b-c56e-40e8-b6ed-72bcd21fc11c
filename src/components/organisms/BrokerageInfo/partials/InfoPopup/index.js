import React, { useEffect, lazy, Suspense } from 'react';

import { formatPrice } from '../../../../molecules/Prices';

import OrderCharges from './partials/OrderCharges';
import Loader from '../Loader';
import Disclaimer from './partials/Disclaimer';

import { STATICS } from './statics';

import styles from './index.scss';

const ErrorView = lazy(() =>
  import(
    /* webpackChunkName: 'BrokerageInfo-popup-retryScreen' */ '../ErrorView'
  ),
);

function InfoPopup({ mutate, apiData, postData, desc }) {
  const { data, isLoading, isError } = apiData;

  useEffect(() => {
    mutate(postData);
  }, []);

  if (!data || isLoading) return <Loader />;

  if (isError) {
    return (
      <Suspense fallback={<></>}>
        <ErrorView mutate={mutate} postData={postData} />
      </Suspense>
    );
  }

  return (
    <div className={styles.container}>
      {data.sections.map(section => (
        <OrderCharges data={section} key={section.title} />
      ))}
      <div className={styles.totalContainer}>
        <p className={styles.totalHeader}>{STATICS.TOTAL}</p>
        <p className={styles.totalValue}>₹{formatPrice(data.total)}</p>
      </div>
      <Disclaimer
        auto_sq_off={data?.auto_sq_off}
        postData={postData}
        {...desc}
      />
    </div>
  );
}

export default InfoPopup;
