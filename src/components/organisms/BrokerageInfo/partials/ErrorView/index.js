import { Button } from '@paytm-h5-common/paytm_common_ui';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';

import { STATICS } from '../../Statics';

import styles from './index.scss';

function ErrorView({ mutate, postData }) {
  return (
    <div className={styles.container}>
      <Icon name={ICONS_NAME.ERROR} size={20} className={styles.icon} />
      <Button
        label={STATICS.TRY_AGAIN}
        onClick={() => mutate(postData)}
        emphasis="high"
      />
    </div>
  );
}

export default ErrorView;
