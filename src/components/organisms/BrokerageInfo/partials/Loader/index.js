import React from 'react';

import Shimmer from '../../../../atoms/Shimmer';

import styles from './index.scss';

function Loader() {
  return (
    <div className={styles.container}>
      {new Array(2).fill().map((_, index) => (
        <div key={index}>
          <Shimmer />
          <Col />
          <Col />
        </div>
      ))}
    </div>
  );
}

function Col() {
  return (
    <div className={styles.col}>
      <Shimmer width="168px" height="25px" />
      <Shimmer width="37px" height="25px" />
    </div>
  );
}

export default Loader;
