/* eslint-disable no-use-before-define */
import React, { lazy, Suspense } from 'react';

import Drawer, { ALIGNMENTS } from '../../molecules/DrawerV2';
import Spinner from './partials/Spinner';

import { useBrokerageInfo } from './useBrokerageInfo';
// import { useToast } from '@components/provider/AppProvider';

import { TRANSACTION_TYPES } from '../../../utils/Equities/enum';
import { STATICS } from './Statics';

import styles from './index.scss';
import { useAppStore } from '../../../contexts/AppContextProvider';
import { emptyObj } from '../../../utils/commonUtil';
import { APPEARANCE_TYPES } from '../../../utils/constants';

const InfoPopup = lazy(
  () => import(/* brokerageInfo-InfoPopup */ './partials/InfoPopup'),
);

function BrokerageInfo(props) {
  const { isOpen, onClose: closeDrawer, onOpen, apiData } = useBrokerageInfo();
  const { addSnackBar } = useAppStore() || emptyObj;
  const {
    desc,
    render,
    chargesBottomSheet,
    chargesBottomSheetClosed,
    ...postData
  } = props;

  const onClose = () => {
    closeDrawer();
    if (chargesBottomSheetClosed) {
      chargesBottomSheetClosed();
    }
  };

  const { mutate } = apiData;
  return (
    <>
      {render(validateBody)}
      <Drawer
        isOpen={isOpen}
        onClose={onClose}
        title={STATICS.HEADER}
        align={ALIGNMENTS.LEFT}
        customTitle={styles.title}
      >
        {isOpen && (
          <Suspense fallback={<Spinner />}>
            <InfoPopup
              mutate={mutate}
              apiData={apiData}
              postData={postData}
              desc={desc}
            />
          </Suspense>
        )}
      </Drawer>
    </>
  );

  function validateBody() {
    const isSl = desc?.isSl;
    const isSell = postData.transaction_type === TRANSACTION_TYPES.SELL;
    if (isSl && isSell && !props.price) {
      addSnackBar({
        message: STATICS.ENTER_TRIGGER_PRICE,
        type: APPEARANCE_TYPES.FAIL,
      });
    } else if (!props.price)
      addSnackBar({
        message: STATICS.FETCHING_LTP,
        type: APPEARANCE_TYPES.FAIL,
      });
    else {
      if (chargesBottomSheet) chargesBottomSheet();
      onOpen();
    }
  }
}

export default BrokerageInfo;
