import { useMutation } from '@tanstack/react-query';

import { useDrawer } from '../../molecules/DrawerV2';
import { useCurrentPlan } from '../../../query/generalQuery';
import { postChargesInfo } from '../../../actions/orderPadActions';

function useBrokerageInfo() {
  const { isOpen, onClose, onOpen } = useDrawer();

  const {
    data: currentPlan,
    isLoading: isCurrentPlanLoading,
  } = useCurrentPlan();

  const brokerageProfileCode =
    currentPlan?.data?.[0]?.classProfileCodes?.[0]?.PLATFORM_CHARGES;

  const getBrokerageCharges = async postData => {
    const { data: response } = await postChargesInfo({
      ...postData,
      brokerage_profile_code: brokerageProfileCode,
    });
    return response.data;
  };

  const { mutate, isLoading, isError, data } = useMutation(
    getBrokerageCharges,
    {},
  );

  return {
    isOpen,
    onClose,
    onOpen,
    apiData: {
      data,
      mutate,
      isLoading: isLoading || isCurrentPlanLoading,
      isError,
    },
  };
}

export { useBrokerageInfo };
