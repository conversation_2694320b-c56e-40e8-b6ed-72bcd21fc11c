import { makeApiPostCall, getGenericAppHeaders } from '@utils/apiUtil';
import { EQUITIES } from '@config/urlConfig';

function postChargesInfo(postData) {
  /*
   * postData -> exchange, price, product_type, qty, transaction_type, brokerage_profile_code, instrument_type,
   */
  return makeApiPostCall({
    url: EQUITIES.CHARGES_INFO.POST,
    body: {
      ...postData,
    },
    headers: getGenericAppHeaders(),
  });
}

export { postChargesInfo };
