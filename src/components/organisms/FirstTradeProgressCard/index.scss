.root {
  border-radius: 16px;
  border: 1px solid var(--border-neutral-weak);
  background-color: var(--background-neutral-inverse);
}

.addPadding {
  margin: 0 16px;
}

.progressContainer {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 16px;
  border-bottom: 1px solid var(--border-neutral-weak);

  .nameInfoContainer {
    display: flex;
    flex-direction: column;

    .name {
      font-size: 18px;
      font-weight: 500;
      line-height: 1.5;
      color: var(--text-positive-strong);
    }

    .des {
      font-size: 12px;
      font-weight: 400;
      color: var(--text-neutral-medium);
    }
  }
}

.progressOptions {
  padding: 16px 16px 0;

  .progressDetail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;

    .info {
      display: flex;

      .details {
        display: flex;
        flex-direction: column;
        margin-left: 8px;

        .title {
          font-size: 12px;
          font-weight: 500;
          line-height: 1.5;
          color: var(--text-neutral-strong);
        }

        .value {
          font-size: 12px;
          font-weight: 400;
          color: var(--text-neutral-medium);
        }

        .valueIR {
          font-size: 12px;
          font-weight: 400;
          color: var(--text-positive-strong);
        }

        .fund {
          display: flex;
          align-items: center;
          font-size: 12px;
          font-weight: 400;
          color: var(--text-neutral-medium);
        }

        .accountOpen {
          font-size: 12px;
          font-weight: 400;
          color: var(--text-primary-strong);
        }
      }
    }
  }
}

.ctaPrimary {
  border-radius: 96px;
  width: 100px;
  padding: 8px 12px;
  margin-left: 8px;
  background: var(--text-primary-strong);
  border: none;

  .text {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
    color: var(--text-neutral-inverse);
  }
}

.ctaSecondary {
  border-radius: 96px;
  width: 100px;
  padding: 8px 12px;
  margin-left: 8px;
  background: var(--background-neutral-inverse);
  border: 1px solid var(--background-primary-strong);

  .text {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
    color: var(--text-primary-strong);
  }
}

.pieContainer {
  height: 48px !important;
  width: 48px !important;
}
