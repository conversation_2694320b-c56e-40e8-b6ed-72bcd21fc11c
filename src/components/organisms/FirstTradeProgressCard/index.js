import React, { useEffect } from 'react';
import cx from 'classnames';

import {
  openDeepLinkPaytmMoney,
  openDeepLinkPaytm,
  notifyNativeApp,
} from '../../../utils/bridgeUtils';
import tickIcon from '../../../assets/FirstTradeProgressCard/tick.svg';
import colors from '../../../commonStyles/colors.scss';
import {
  EVENTS,
  CARD_IDS,
  LABELS,
} from '../../../config/FirstTradeProgressCard';
import Pie from '../../atoms/Pie';
import Icon from '../../molecules/Icon';
import useFirstTradeProgressCard from '../../../hooks/useFirstTradeProgressCard';
import { useBootApiCall } from '../../../hooks/useBootApiCall';
import { isIosBuild, log } from '../../../utils/commonUtil';
import { useAnalyticsEventForFirstTradeProgressCard } from '../../../hooks/analyticsHooks';
import { isPaytmMoney } from '../../../utils/coreUtil';

import s from './index.scss';

const FirstTradeProgressCard = ({
  pages,
  refetchAggrData,
  isWidget,
  navigateTo,
  history,
}) => {
  const { personalDetails } = useBootApiCall();
  const {
    percentage,
    isMfIR,
    isStockIR,
    list,
    isFundsUserEligible,
    title,
    subTitle,
    mainContainerRef,
    timeoutRef,
    widgetId,
    subCohortId,
    irStatus,
    hasInvested,
    hasTraded,
  } = useFirstTradeProgressCard({ pages });
  const { sendAnalyticsEvent } = useAnalyticsEventForFirstTradeProgressCard();
  const { displayName, firstName } = personalDetails;

  const handleEvents = (action, event = EVENTS.EVENT.CUSTOM_EVENT) => {
    sendAnalyticsEvent(
      EVENTS.VERTICAL_NAME,
      EVENTS.SCREEN_NAME,
      EVENTS.CATEGORY,
      action,
      event,
      'NA',
      `${widgetId} | ${widgetId}`,
      'NA',
      subCohortId,
      'NA',
      `irstatus: ${irStatus} | hasinvestedstatus: ${hasInvested} | hastradedstatus: ${hasTraded}`,
    );
  };

  const getWidgetHeight = () => {
    const divHeight = mainContainerRef.current.getBoundingClientRect().height;
    if (isIosBuild()) {
      return divHeight;
    }

    return (divHeight + 15).toString();
  };

  const setFragmentHeight = () => {
    if (isPaytmMoney()) {
      notifyNativeApp({
        flowType: 'combinedHomeH5FragmentHeight',
        height: getWidgetHeight(),
        widgetId: 'pre-ir-invest-widget',
      });
    }
  };

  const handleVisibilityChange = () => {
    if (!document.hidden) {
      refetchAggrData();
    }
  };

  useEffect(() => {
    handleEvents(EVENTS.ACTIONS.NEW_IR_WIDGET, EVENTS.EVENT.OPEN_SCREEN);
    timeoutRef.current = setTimeout(() => {
      setFragmentHeight();
    }, 200);
    document.addEventListener(
      'visibilitychange',
      handleVisibilityChange,
      false,
    );

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      document.removeEventListener(
        'visibilitychange',
        handleVisibilityChange,
        false,
      );
    };
  }, []);

  const handleMFViewOrderStatus = (option) => {
    log(
      'handleMFViewOrderStatus link -> ',
      option.deeplink,
      option.deeplinkMini,
    );
    handleEvents(EVENTS.ACTIONS.INVEST_NOW_MF_ORDER_STATUS_CLICK);
    if (isWidget) {
      openDeepLinkPaytm(option.deeplinkMini);
    } else {
      openDeepLinkPaytmMoney(option.deeplink);
    }
  };

  const handleViewOrderStatus = (option) => {
    log('handleViewOrderStatus link -> ', option.deeplink, option.deeplinkMini);
    handleEvents(EVENTS.ACTIONS.INVEST_NOW_STOCKS_ORDER_STATUS_CLICK);
    if (isWidget) {
      navigateTo(history, option.deeplinkMini);
    } else {
      openDeepLinkPaytmMoney(option.deeplink);
    }
  };

  const handleAccountStatusClick = (option) => {
    log(
      'handleAccountStatusClick link -> ',
      option.deeplink,
      option.deeplinkMini,
    );
    handleEvents(EVENTS.ACTIONS.VIEW_ACCOUNT_STATUS_CLICK);
    if (isWidget) {
      navigateTo(history, option.deeplinkMini);
    } else {
      openDeepLinkPaytmMoney(option.deeplink);
    }
  };

  const getOptionCTA = (option) => {
    let text = null;
    let ctaClickFunc = () => {};
    let isPrimary = false;

    const setButtonProps = (
      textFromParams,
      ctaClickFuncFromParams = () => {},
      isPrimaryFromParams = false,
    ) => {
      text = textFromParams;
      ctaClickFunc = ctaClickFuncFromParams;
      isPrimary = isPrimaryFromParams;
    };

    switch (option.name) {
      case CARD_IDS.FUND_ADDITION_CARD:
        if (option.isComplete) {
          setButtonProps(null);
        } else {
          setButtonProps(
            option.cta,
            () => {
              log('option.LINK -> ', option.deeplink, option.deeplinkMini);
              handleEvents(EVENTS.ACTIONS.ADD_FUND_CTA_CLICK);
              if (isWidget) {
                navigateTo(history, option.deeplinkMini);
              } else {
                openDeepLinkPaytmMoney(option.deeplink);
              }
            },
            true,
          );
        }
        break;
      case CARD_IDS.MF_PURCHASE_CARD:
        if (option.isComplete) {
          setButtonProps(null);
        } else if (isMfIR) {
          setButtonProps(
            option.cta,
            () => {
              log('IR invest now mf -> ', option.deeplink, option.deeplinkMini);
              handleEvents(EVENTS.ACTIONS.INVEST_NOW_MF_CTA_CLICK);
              if (isWidget) {
                openDeepLinkPaytm(option.deeplinkMini);
              } else {
                openDeepLinkPaytmMoney(option.deeplink);
              }
            },
            true,
          );
        } else {
          setButtonProps(option.cta, () => {
            log(
              'NON_IR invest now mf -> ',
              option.deeplink,
              option.deeplinkMini,
            );
            handleEvents(EVENTS.ACTIONS.INVEST_NOW_MF_CTA_CLICK);
            if (isWidget) {
              openDeepLinkPaytm(option.deeplinkMini);
            } else {
              openDeepLinkPaytmMoney(option.deeplink);
            }
          });
        }
        break;
      case CARD_IDS.STOCK_PURCHASE_CARD:
        if (option.isComplete) {
          setButtonProps(null);
        } else if (isStockIR) {
          setButtonProps(
            option.cta,
            () => {
              log(
                'IR invest now stocks -> ',
                option.deeplink,
                option.deeplinkMini,
              );
              handleEvents(EVENTS.ACTIONS.INVEST_NOW_STOCKS_CTA_CLICK);
              if (isWidget) {
                navigateTo(history, option.deeplinkMini);
              } else {
                openDeepLinkPaytmMoney(option.deeplink);
              }
            },
            true,
          );
        } else {
          setButtonProps(
            option.cta,
            () => {
              log(
                'NON_IR invest now stocks -> ',
                option.deeplink,
                option.deeplinkMini,
              );
              handleEvents(EVENTS.ACTIONS.INVEST_NOW_STOCKS_CTA_CLICK);
              if (isWidget) {
                navigateTo(history, option.deeplinkMini, {
                  pageOrigin: LABELS.HOME_SCREEN,
                });
              } else {
                openDeepLinkPaytmMoney(option.deeplink);
              }
            },
            true,
          );
        }
        break;
      default:
        break;
    }

    if (!text) return <Icon url={tickIcon} width={24} byPass />;

    return (
      <button
        className={isPrimary ? cx(s.ctaPrimary) : cx(s.ctaSecondary)}
        onClick={ctaClickFunc}
        type="button"
      >
        <span className={s.text}>{text}</span>
      </button>
    );
  };

  const renderSubtitle = (option) => {
    if (isStockIR && option.name === CARD_IDS.ACCOUNT_DETAILS_CARD) {
      return <span className={s.valueIR}>{option.subtitle}</span>;
    }

    let onClickFunc = () => {};
    let className = '';
    let optionSubTitle = '';

    const setSubTitleContent = (
      onClickFromParams,
      classNameFromParams,
      subtitleFromParams,
    ) => {
      if (option.isComplete) {
        onClickFunc = onClickFromParams;
        className = classNameFromParams;
        optionSubTitle = subtitleFromParams;
      } else {
        onClickFunc = () => {};
        className = s.value;
        optionSubTitle = option.subtitle;
      }
    };

    switch (option.name) {
      case CARD_IDS.ACCOUNT_DETAILS_CARD:
        setSubTitleContent(
          handleAccountStatusClick,
          s.accountOpen,
          option.subtitle,
        );
        break;
      case CARD_IDS.STOCK_PURCHASE_CARD:
        setSubTitleContent(
          handleViewOrderStatus,
          s.accountOpen,
          option.subtitle,
        );
        break;
      case CARD_IDS.MF_PURCHASE_CARD:
        setSubTitleContent(
          handleMFViewOrderStatus,
          s.accountOpen,
          option.subtitle,
        );
        break;
      case CARD_IDS.FUND_ADDITION_CARD:
        setSubTitleContent(() => {}, s.fund, option.subtitle);
        break;
      default:
        setSubTitleContent(() => {}, s.value, option.subtitle);
        break;
    }

    return (
      <span onClick={() => onClickFunc(option)} className={className}>
        {optionSubTitle}
      </span>
    );
  };

  if (!list?.length || !isFundsUserEligible) {
    return null;
  }

  const getTitle = () => {
    const modifiedDN = displayName
      ? displayName.split(' ')[0][0].toUpperCase() +
        displayName.split(' ')[0].slice(1).toLowerCase()
      : '';

    const modifiedFN = firstName
      ? firstName.split(' ')[0][0].toUpperCase() +
        firstName.split(' ')[0].slice(1).toLowerCase()
      : '';

    const name = modifiedDN || modifiedFN || '';
    return `${title} ${name}!`;
  };

  return (
    <div
      className={cx(s.root, {
        [s.addPadding]: isWidget,
      })}
      ref={mainContainerRef}
    >
      <div className={s.progressContainer}>
        <div className={s.nameInfoContainer}>
          <span className={s.name}>{getTitle()}</span>
          <span className={s.des}>{subTitle}</span>
        </div>
        <div className={s.pieContainer}>
          <Pie percentage={percentage} colour={colors.Green5} />
        </div>
      </div>
      <div className={s.progressOptions}>
        {list?.map((option) => (
          <div key={option.id} className={s.progressDetail}>
            <div className={s.info}>
              <Icon url={option.iconUrl} width={32} byPass />
              <div className={s.details}>
                <span className={s.title}>{option.title}</span>
                {renderSubtitle(option)}
              </div>
            </div>
            {getOptionCTA(option)}
          </div>
        ))}
      </div>
    </div>
  );
};

export default FirstTradeProgressCard;
