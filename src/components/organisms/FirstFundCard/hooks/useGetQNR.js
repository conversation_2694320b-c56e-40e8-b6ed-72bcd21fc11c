import { useMemo, useCallback } from 'react';
import queryString from 'query-string';
import { PAYMENT_DATA, QUICK_PAYMENT_DATA } from '../../../../config/paymentConfig';


function useGetQNR(paymentMethods) {
  const searchParams = queryString.parse(window.location.search);

  const getLogo = useCallback(
    id =>
      paymentMethods.find(method => method.paymentMethodId === id)
        ?.paymentMethodImgUrl,
    [paymentMethods],
  );

  return useMemo(() => {
    const showAllOptions = searchParams?.showAllOptions;
    if (showAllOptions && showAllOptions === 'true') return null;

    const qnr = paymentMethods.find(
      method => method.paymentMethodId === PAYMENT_DATA.PAYMENT_MODES.PPBL,
    );

    if (!qnr) return null;

    const isNetBanking =
      qnr?.paymentMethodOptionList?.[0]?.paymentMethodId ===
      PAYMENT_DATA.PAYMENT_MODES.NET_BANKING;

    if (isNetBanking) return null;

    const isUPICollect =
      qnr?.paymentMethodOptionList?.[0]?.upiFlow ===
      QUICK_PAYMENT_DATA.UPI_COLLECT;

    const upiData = paymentMethods.find(
      method => method.paymentMethodId === PAYMENT_DATA.PAYMENT_MODES.UPI,
    );
    if (isNetBanking || isUPICollect) {
      const appUrl = getLogo(
        isUPICollect
          ? PAYMENT_DATA.PAYMENT_MODES.UPI
          : PAYMENT_DATA.PAYMENT_MODES.NET_BANKING,
      );
      return {
        ...qnr,
        appUrl,
        upiData: {
          ...upiData,
        },
      };
    }
    return qnr;
  }, [paymentMethods, getLogo]);
}

export { useGetQNR };
