import { PAYMENT_DATA, QUICK_PAYMENT_DATA } from '../../../../config/paymentConfig';

function useGetQNRConfig({
  paymentMethodOption,
  qnrData,
  installedQnrIntentApp,
}) {
  if (!paymentMethodOption) {
    return {};
  }

  switch (true) {
    case paymentMethodOption?.paymentMethodId ===
      PAYMENT_DATA.PAYMENT_MODES.NET_BANKING: {
      return {
        title: 'Net Banking',
        subtitle: `${paymentMethodOption?.displayName} - ${paymentMethodOption?.bankAccountNumber}`,
        logo: paymentMethodOption?.bankImgSrc,
        appLogo: qnrData?.appUrl,
      };
    }
    case paymentMethodOption?.upiFlow === QUICK_PAYMENT_DATA.UPI_COLLECT: {
      return {
        title: paymentMethodOption?.upiDetails?.collect?.lastUsedVpa,
        subtitle: `${paymentMethodOption?.displayName} - ${paymentMethodOption?.bankAccountNumber}`,
        logo: paymentMethodOption?.bankImgSrc,
        appLogo: qnrData?.appUrl,
      };
    }
    case paymentMethodOption?.upiFlow === QUICK_PAYMENT_DATA.UPI_INTENT: {
      return {
        title: installedQnrIntentApp?.appName,
        subtitle: `${paymentMethodOption?.displayName} - ${paymentMethodOption?.bankAccountNumber}`,
        appLogo: installedQnrIntentApp?.logoUrl,
        logo: paymentMethodOption?.bankImgSrc,
      };
    }
    default: {
      return {
        title: '',
        subtitle: `${paymentMethodOption?.displayName} - ${paymentMethodOption?.bankAccountNumber}`,
        logo: paymentMethodOption?.bankImgSrc,
        appLogo: paymentMethodOption?.bankImgSrc,
      };
    }
  }
}

export { useGetQNRConfig };
