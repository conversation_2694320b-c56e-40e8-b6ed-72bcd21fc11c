import { useQuery } from '@tanstack/react-query';

import { PAYMENT_API_URLS } from '../../../../config/urlConfig';
import { errorLog } from '../../../../utils/commonUtil';
import {
  getGenericAppHeaders,
  makeApiGetCall,
  makeApiPostCall,
} from '../../../../utils/apiUtil';
import { PAYMENTS_CONFIG_URL } from '../../../../config/envConfig';

const useGetPaymentOptionsFms = () =>
  useQuery(
    ['payment-options-fms'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: PAYMENT_API_URLS.PAYMENT_OPTIONS_FMS,
          headers: {
            ...getGenericAppHeaders(),
            channel: 'Paytm',
          },
        });
        return data?.data;
      } catch (error) {
        errorLog(error);
      }
    },
    { refetchOnMount: true },
  );

export const usePaymentsConfig = () =>
  useQuery(
    ['payments-config'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: PAYMENTS_CONFIG_URL.GET_CONFIG_V6,
          headers: getGenericAppHeaders(),
        });
        return data;
      } catch (error) {
        errorLog(error);
      }
    },
    { refetchOnMount: true },
  );

export const initiatePayment = async (initiatePaymentBody) => {
  const url = PAYMENT_API_URLS.INITIATE_FUND_FMS;

  try {
    const response = await makeApiPostCall({
      url,
      headers: getGenericAppHeaders(),
      body: initiatePaymentBody,
    });
    return response.data.data;
  } catch (error) {
    errorLog(error);
    return {};
  }
};
export { useGetPaymentOptionsFms };
