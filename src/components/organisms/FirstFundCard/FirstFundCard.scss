@import '../../../commonStyles//variables.scss'; 

.card {
  padding: 0 16px 8px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  height: 250px;
}

.content {
  background: linear-gradient(180deg, #d4ddfd 0%, #c9dffd 50%, #ffffff 100%);
  border-radius: 8px;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.headerContent {
  background-image: url(../../../assets/firstFundCard.png);
  padding: 16px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.title {
  font-size: 16px;
    font-weight: 500;
  color:  var(--text-neutral-strong);
}

.subtitle {
  font-size: 12px;
    font-weight: 400;
    line-height: 18px;
  color:  var(--text-neutral-strong);
}

.inputContainer {
  padding: 0 16px 10px;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.inputStyle {
  border: 1px solid var(--border-neutral-variant);
  background: var(--surface-level-1);
  padding: 10px;
  border-radius: 8px;
  text-align: center;

  &:focus {
    border: 0;
    outline: none;
  }

  &::placeholder {
    text-align: center;
    font-size: 14px;
    line-height: 2.14;
    color:  var(--text-neutral-weak);
  }
}

.errorInput {
  border: 1px solid var(--border-negative-strong);
}

.errorMsg {
  text-align: center;
  padding-top: 5px;
  font-size: 12px;
    font-weight: 400;
    line-height: 18px;
  color:  var(--text-negative-strong);
}

.chipContainer {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 0 16px 16px;
}

.btnContainer {
  display: flex;
  justify-content: center;
  padding: 0 16px 16px;
}

.showBtn {
  background-color: var(--background-primary-strong);
  border-radius: 8px;

  @include typography(text, var(--text-universal-strong));
}
