/* eslint-disable react-hooks/rules-of-hooks */
import React, { useCallback, useEffect, useState } from 'react';
import {Button} from '@paytm-h5-common/paytm_common_ui';
import { cancelPayment } from '../../../actions/myOrderActions';
import { useDocumentHide } from '../../../contexts/NativeDocumentHideContext';

import { REGEX } from '../../../config/config';
import { QUICK_PAYMENT_DATA } from '../../../config/paymentConfig';
import { withErrorBoundary } from '../../../HOC/WidgetErrorBoundary';
import useMakePaytmTpvPayment from '../OrderPadLite/hooks/useMakePaytmTpvPayment';
import useUpiIntentFlow from '../OrderPadLite/hooks/useUpiIntentFlow';
import { isEquityMiniApp } from '../../../utils/apiUtil';
import { log } from '../../../utils/commonUtil';
import { useAnalyticsEventForFirstCards } from '../../../hooks/analyticsHooks';


import {
  callPaymentFlow,
  callPaymentStatusFlow,
} from '../../../utils/navigationUtil';
import { PULSE_STATICS } from './enums';
import styles from './FirstFundCard.scss';
import { useGetQNR } from './hooks/useGetQNR';
import {
  initiatePayment,
  useGetPaymentOptionsFms,
} from './hooks/usePaymentQuery';
import Chip from './partials/Chips';
import QnrComponent from './partials/QnrComponent';
import { sanitizeAppName } from './utils';

const FirstFundCard = props => {
  if (!props.data) return <></>;

  const { items } = props.data;
  const { title, subtitle, 'funds-card': fundsCard } = items[0] || {};
  const [amount, setAmount] = useState(fundsCard.prefilledAmount);
  const [isInitiateApiLoading, setIsInitiateApiLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [installedQnrIntentApp, setInstalledQnrIntentApp] = useState(null);
  const { sendAnalyticsEventFirstCard } = useAnalyticsEventForFirstCards();
  const { upiIntentOptions } = useUpiIntentFlow();
  const { txnStatusdata, makePaytmTpvPayment } = useMakePaytmTpvPayment();
  const {
    data: paymentOptions,
    // isLoading,
    // isFetching,
    refetch: refetchPaymentOptions,
  } = useGetPaymentOptionsFms();
  const paymentMethods = paymentOptions?.paymentMethods || [];
  const qnrData = useGetQNR(paymentMethods);

  useDocumentHide({
    onResume: () => {
      const isPrevTransactionDone = sessionStorage.getItem(
        'isPrevTransactionDone',
      );
      log('onResume', isPrevTransactionDone);
      if (sessionStorage.getItem('hasMovedToPaymentFLow')) {
        props.refetchAggrData();
      }
      if (isPrevTransactionDone) {
        sessionStorage.removeItem('isPrevTransactionDone');
        refetchPaymentOptions();
      }
    },
  });
  const sendPulseEvents = (action, event, label = '') => {
    sendAnalyticsEventFirstCard({
      event,
      action,
      label,
    });
  };

  const getNumericAmount = () =>
    amount && typeof amount === 'string'
      ? parseFloat(amount?.split(',')?.join(''))
      : typeof amount === 'number'
      ? amount
      : 0;

  const onChangeClicked = () => {
    sendPulseEvents(
      PULSE_STATICS.ACTION.Firstfund_changeclick,
      PULSE_STATICS.CUSTOM_EVENT,
    );
    const amountValue = getNumericAmount();
    sessionStorage.setItem('hasMovedToPaymentFLow', true);
    callPaymentFlow({ amount: amountValue });
  };

  useEffect(() => {
    // to do : add intersection observer for event
    sendPulseEvents(
      PULSE_STATICS.ACTION.Firstfund_widget,
      PULSE_STATICS.OPEN_SCREEN_EVENT,
    );
  }, []);

  const setValidAmount = value => {
    const data = value.replace(REGEX.ADD_COMMA_AMOUNT, ',');
    setAmount(data);
  };

  const resetError = () => {
    if (errorMsg) setErrorMsg('');
  };

  const amountChangeHandler = e => {
    const { value } = e.target;
    const inputRegex = new RegExp(`^\\d{0,${12}}(\\.\\d{0,${2}})?$`);
    const inputValue = value.split(',').join('');
    const isValid = inputRegex.test(inputValue);
    if (isValid) {
      sendPulseEvents(
        PULSE_STATICS.ACTION.Firstfund_amountclick,
        PULSE_STATICS.CUSTOM_EVENT,
        inputValue,
      );
      setValidAmount(inputValue);
    }
    resetError();
  };

  const onChipClick = val => {
    let newAmount;
    if (amount) {
      const inputValue = getNumericAmount();
      newAmount = `${inputValue + parseFloat(val)}`;
    } else {
      newAmount = `${parseFloat(val)}`;
    }
    sendPulseEvents(
      PULSE_STATICS.ACTION.Firstfund_suggestionclick,
      PULSE_STATICS.CUSTOM_EVENT,
      val,
    );
    setValidAmount(newAmount);
    resetError();
  };

  const callMakePaytmTpvPayment = useCallback(
    data => makePaytmTpvPayment(data),
    [makePaytmTpvPayment],
  );

  const getFindInstalledApp = (upiIntentOptionsT, appName) =>
    upiIntentOptionsT?.find(
      apps => apps?.appName?.toLowerCase() === appName?.toLowerCase(),
    );

  useEffect(() => {
    if (qnrData) {
      setInstalledQnrIntentApp(
        getFindInstalledApp(
          upiIntentOptions,
          sanitizeAppName(
            qnrData?.paymentMethodOptionList[0]?.upiDetails?.intent?.name,
          ),
        ),
      );
    }
  }, [qnrData, upiIntentOptions]);

  const onAddFundsClicked = async () => {
    sendPulseEvents(
      PULSE_STATICS.ACTION.Firstfund_fundaddcta,
      PULSE_STATICS.CUSTOM_EVENT,
      amount,
    );
    const amountValue = getNumericAmount();
    if (
      amountValue < fundsCard.minAmount ||
      amountValue > fundsCard.maxAmount
    ) {
      setErrorMsg(fundsCard.errorMessage);
      return;
    }

    if (
      !qnrData ||
      (qnrData?.paymentMethodOptionList?.[0]?.upiFlow ===
        QUICK_PAYMENT_DATA.UPI_INTENT &&
        !installedQnrIntentApp)
    ) {
      sessionStorage.setItem('hasMovedToPaymentFLow', true);
      callPaymentFlow({ amount: amountValue });
      return;
    }

    const selectedBank = qnrData?.paymentMethodOptionList?.[0];
    const makePaymentRequestBody = {
      amount: amountValue,
      id: selectedBank?.id,
      payments_txn_id: paymentOptions?.paymentsTxnId,
      payment_method: selectedBank?.paymentMethodId,
      vpa: selectedBank?.upiDetails?.collect?.lastUsedVpa,
      psp_app: installedQnrIntentApp?.processName
        ? installedQnrIntentApp?.appName
        : null,
      fms_txn_id: paymentOptions?.fmsTxnId,
    };

    sessionStorage.setItem(
      'firstFundTxnId',
      JSON.stringify({
        fmsTxnId: paymentOptions?.fmsTxnId,
        paymentsTxnId: paymentOptions?.paymentsTxnId,
      }),
    );
    setIsInitiateApiLoading(true);
    const makePaymentRes = await initiatePayment(makePaymentRequestBody);
    log('onAddFundsClicked makePaymentRes', makePaymentRes);
    setIsInitiateApiLoading(false);
    const paymentBridgeParams = makePaymentRes;
    if (paymentBridgeParams?.responseType) {
      callMakePaytmTpvPayment({
        responseType: paymentBridgeParams.responseType,
        redirectionUrl: paymentBridgeParams.redirectionUrl,
        listenUrl: paymentBridgeParams.listenUrl,
        paymentsTxnId: paymentBridgeParams.paymentsTxnId,
        vpa: selectedBank?.upiDetails?.collect?.lastUsedVpa || null,
        processTxnRequestParams: paymentBridgeParams.processTxnRequestParams,
        amount: amountValue,
        upiDeepLink: paymentBridgeParams.upiDeepLink,
        processName: installedQnrIntentApp?.processName || null,
        source_payment: !isEquityMiniApp() ? 'pml' : '',
        mid: paymentBridgeParams.processTxnRequestParams?.mid,
        txnToken: paymentBridgeParams.processTxnRequestParams?.txnToken,
        fmsTxnId: paymentOptions?.data?.fmsTxnId,
        // exitIntentConfig: supportedVersion
        //   ? {
        //       exitIntentEnabled,
        //       title: header,
        //       sub_title: description,
        //       image_url: pgImage,
        //       primary_cta: primaryCta,
        //       sec_cta: secondaryCta,
        //     }
        //   : {},
      });
    }
  };

  useEffect(() => {
    if (txnStatusdata) {
      if (txnStatusdata?.isTransactionDone) {
        callPaymentStatusFlow({
          transactionId: JSON.parse(
            sessionStorage.getItem('firstFundTxnId'),
            '{}',
          )?.fmsTxnId,
        });
        sessionStorage.removeItem('firstFundTxnId');
        sessionStorage.setItem('isPrevTransactionDone', true);
      } else {
        log('cancelling the payment');
        cancelPayment(
          JSON.parse(sessionStorage.getItem('firstFundTxnId'), '{}')
            ?.paymentsTxnId,
        );
        sessionStorage.removeItem('firstFundTxnId');
        refetchPaymentOptions();
      }
      props.refetchAggrData();
    }
  }, [txnStatusdata]);

  return (
    <div className={styles.card}>
      <div className={styles.content}>
        <div className={styles.headerContent}>
          <div className={styles.title}>{title}</div>
          <div className={styles.subtitle}>{subtitle}</div>
        </div>
        <div className={styles.inputContainer}>
          <input
            value={amount || ''}
            placeholder={fundsCard.inputPlaceholder}
            id="amount"
            data-testid="amount"
            inputMode="decimal"
            onChange={amountChangeHandler}
            type="text"
            className={`${styles.inputStyle} ${
              errorMsg ? styles.errorInput : ''
            }`}
          />
          <div className={styles.errorMsg}>{errorMsg}</div>
        </div>
        {fundsCard?.suggestions && fundsCard?.suggestions?.length > 0 && (
          <div className={styles.chipContainer}>
            {fundsCard.suggestions.map((suggestion, index) => (
              <Chip key={index} label={suggestion} onChipClick={onChipClick} />
            ))}
          </div>
        )}
        <div className={styles.btnContainer}>
          <Button
            customClass={styles.customClassButton}
            label={amount ? `Add ₹${amount}` : fundsCard.addFundsCta}
            emphasis="high"
            onClick={isInitiateApiLoading? ()=>{}:onAddFundsClicked}
            loading={isInitiateApiLoading}
          />
        </div>
        {qnrData && (
          <QnrComponent
            qnrData={qnrData}
            installedQnrIntentApp={installedQnrIntentApp}
            onChangeClicked={onChangeClicked}
          />
        )}
      </div>
    </div>
  );
};

export default React.memo(withErrorBoundary(FirstFundCard));
