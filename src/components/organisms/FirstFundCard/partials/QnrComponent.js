/* eslint-disable react-hooks/rules-of-hooks */
import Icon, { ICONS_NAME } from '../../../molecules/Icon';
import { QUICK_PAYMENT_DATA } from '../../../../config/paymentConfig';
import { useGetQNRConfig } from '../hooks/useGetQNRConfig';
import styles from './index.scss';

const QnrComponent = ({ qnrData, installedQnrIntentApp, onChangeClicked }) => {
  if (
    qnrData?.paymentMethodOptionList?.[0]?.upiFlow ===
      QUICK_PAYMENT_DATA.UPI_INTENT &&
    !installedQnrIntentApp
  )
    return null;

  const paymentMethodOption = qnrData?.paymentMethodOptionList[0];
  const { title, subtitle, logo, appLogo } = useGetQNRConfig({
    paymentMethodOption,
    qnrData,
    installedQnrIntentApp,
  });

  return (
    <div className={styles.container}>
      <div className={styles.detailsContainer}>
        <Icon url={appLogo} width="28px" />
        <div className={styles.textContainer}>
          <div className={styles.title}>{title}</div>
          <div className={styles.subtitleContainer}>
            <Icon url={logo} width="14px" />
            <div className={styles.subtitle}>{subtitle}</div>
          </div>
        </div>
      </div>
      <div className={styles.cta} onClick={onChangeClicked}>
        Change
        <Icon name={ICONS_NAME.LEFT_ARROW_BLUE} width={16} />
      </div>
    </div>
  );
};

export default QnrComponent;
