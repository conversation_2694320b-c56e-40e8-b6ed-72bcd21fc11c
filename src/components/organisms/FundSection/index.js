import { useMemo } from 'react';
import cx from 'classnames';
import GenericWidget from '../../molecules/GenericWidget/GenericWidget';
import blackDownArrow from '../../../assets/icons/black_down_arrow.svg';
import blackDownArrowWhite from '../../../assets/icons/black_down_arrow_white.svg';
import groupIcon from '../../../assets/icons/Group.png';
import { isDarkMode } from '../../../utils/commonUtil';
import styles from './FundSection.module.scss';
import { useGenericWidgetAnalyticsEvents } from '../../../hooks/useGenericWidgetAnalyticsEvents';
import {
  EVENT_ACTION_GENERIC_WIDGET,
  VIEW_MORE,
} from '../../../utils/constants';

const FundSection = ({
  data,
  onViewAllClick,
  selectedDuration,
  setSelectedDuration,
  onFundClick,
}) => {
  const perfRanges =
    data?.widget?.attributes?.find((attr) => attr.name === 'GENERIC_PERF_RANGE')
      ?.value || [];
  const contextData = data?.widget?.attributes?.find(
    (attr) => attr.name === 'GENERIC_DATA_FETCH',
  )?.value;

  const initialFunds = useMemo(() => {
    const allFunds =
      data?.widget?.attributes?.find(
        (attr) => attr.name === 'GENERIC_MF_SECTOR_WISE_SCHEMES',
      )?.value || [];
    return allFunds.slice(0, 3);
  }, [data]);

  const getReturnValue = (fund, duration) => {
    const returnData = fund.fundReturns.returns.find(
      (r) => r.name === duration,
    );
    return returnData ? returnData.percentage : -Infinity;
  };

  const getFormattedReturns = (fund, duration) => {
    const returnData = fund.fundReturns.returns.find(
      (r) => r.name === duration,
    );
    return returnData
      ? `${returnData.percentage > 0 ? '+' : ''}${returnData.percentage.toFixed(2)}%`
      : 'NA';
  };

  const sortedFunds = useMemo(
    () =>
      [...initialFunds].sort(
        (a, b) =>
          getReturnValue(b, selectedDuration) -
          getReturnValue(a, selectedDuration),
      ),
    [initialFunds, selectedDuration],
  );
  const { sendAnalyticsEventGenWidget } = useGenericWidgetAnalyticsEvents();

  const handleDurationIncrease = () => {
    const currentIndex = perfRanges.findIndex(
      (range) => range.range === selectedDuration,
    );
    const nextIndex = (currentIndex + 1) % perfRanges.length;
    setSelectedDuration(perfRanges[nextIndex].range);
  };

  const handleDurationDecrease = () => {
    const currentIndex = perfRanges.findIndex(
      (range) => range.range === selectedDuration,
    );

    const prevIndex =
      (currentIndex - 1 + perfRanges.length) % perfRanges.length;
    setSelectedDuration(perfRanges[prevIndex].range);
  };

  return (
    <div className={styles.fundSection}>
      <div className={styles.header}>
        <div className={styles.titleContainer}>
          <h2 className={cx(styles.title, { [styles.darkMode]: isDarkMode() })}>
            {contextData?.title}
          </h2>
          <p
            className={cx(styles.subtitle, { [styles.darkMode]: isDarkMode() })}
          >
            {contextData?.subtitle}
          </p>
        </div>
        {contextData?.logo && (
          <div className={styles.illustration}>
            <img src={contextData?.logo} alt="Section Logo" />
          </div>
        )}
      </div>

      <div className={styles.filters}>
        <span className={cx(styles.tag, { [styles.darkMode]: isDarkMode() })}>
          {contextData?.text}
        </span>
        <div
          className={cx(styles.durationSelector, {
            [styles.darkMode]: isDarkMode(),
          })}
        >
          <span className={cx({ [styles.darkMode]: isDarkMode() })}>
            {perfRanges.find((r) => r.range === selectedDuration)?.title ||
              contextData?.default}
          </span>
          <div className={styles.chevronButtons}>
            <button
              type="button"
              className={styles.chevronUp}
              onClick={handleDurationIncrease}
            >
              <img
                src={isDarkMode() ? blackDownArrowWhite : blackDownArrow}
                alt="up arrow"
              />
            </button>
            <button
              type="button"
              className={styles.chevronDown}
              onClick={handleDurationDecrease}
            >
              <img
                src={isDarkMode() ? blackDownArrowWhite : blackDownArrow}
                alt="down arrow"
              />
            </button>
          </div>
        </div>
      </div>

      <div className={styles.funds}>
        {sortedFunds.map((fund) => (
          <GenericWidget
            key={fund.isin}
            logo={fund.amcLogo}
            name={fund.schemeName}
            category={fund.category}
            type={fund.subCategory}
            duration={selectedDuration.toUpperCase()}
            returns={getFormattedReturns(fund, selectedDuration)}
            tickerTexts={fund.tickerTexts}
            onClick={() => {
              sendAnalyticsEventGenWidget({
                action:
                  EVENT_ACTION_GENERIC_WIDGET.GENERIC_WIDGET_FUND_CARD_CLICKED,
              });
              onFundClick(fund);
            }}
          />
        ))}
      </div>

      <div className={styles.footer}>
        <button
          type="button"
          className={cx(styles.viewMore, { [styles.darkMode]: isDarkMode() })}
          onClick={onViewAllClick}
        >
          {VIEW_MORE}
          <img
            src={groupIcon}
            alt="Group Icon"
            style={{
              width: '16px',
              height: '16px',
            }}
          />
        </button>
      </div>
    </div>
  );
};

export default FundSection;
