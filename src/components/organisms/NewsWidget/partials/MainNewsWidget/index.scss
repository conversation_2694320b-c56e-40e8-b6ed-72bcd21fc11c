.newsFeedWidget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .newsFeedHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0;
    padding: 16px;
    padding-top: 0;
    padding-bottom: 12px;

    .headerLeft {
      display: flex;
      align-items: center;
      gap: 8px;

      .newsIcon {
        width: 20px;
        height: 20px;
        color: var(--icon-neutral-strong);
        flex-shrink: 0;
      }

      .headerText {
        position: relative;
        top: -20px;
        left: 0;

        .headlineCarousel {
          position: relative;
          height: 100%;

          .headline {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            color: var(--icon-neutral-strong);
            font-size: 14px;
            font-weight: 600;
            opacity: 0;
            transform: translateY(100%);
            transition:
              transform 0.5s ease-in-out,
              opacity 0.5s ease-in-out;
            white-space: nowrap;

            &.active {
              opacity: 1;
              transform: translateY(0);
            }
          }
        }
      }
    }

    .headerRight {
      .viewAll {
        color: #013da6;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        white-space: nowrap;
      }
      .viewAllDarkMode {
        color: #0a86bf;
      }
    }
  }

  .overScrollContainer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 12px;
    overflow-x: scroll;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
    user-select: none;
    padding: 0px 16px 0px 16px;

    &::-webkit-scrollbar {
      display: none;
    }

    .newsGroup {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }

  .paginationDots {
    display: inline-flex;
    align-self: center;
    justify-content: center;
    align-items: center;
    gap: 4px;
    margin: 0;
    margin-top: 8px;
    padding: 0;
    height: 6px;

    .dot {
      width: 6px;
      height: 6px;
      border-radius: 9999px;
      background-color: var(--text-neutral-light);
      padding: 0;
      margin: 0;
      border: none;
      cursor: pointer;
      transition: background-color 0.3s ease;
      flex-shrink: 0;

      &.active {
        background-color: var(--background-primary-strong);
      }

      &:hover:not(.active) {
        background-color: var(--text-neutral-light);
      }
    }
  }
}
