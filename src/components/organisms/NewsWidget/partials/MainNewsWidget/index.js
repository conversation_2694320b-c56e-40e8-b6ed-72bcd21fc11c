import cx from 'classnames';
import { useCallback, useEffect, useRef, useState } from 'react';
import NewsIcon from '@assets/icons/news-Icon.svg';
import HeadlineCarousel from '../../../../molecules/HeadlineCarousel';

import styles from './index.scss';
import { isDarkMode } from '../../../../../utils/commonUtil';
import NewsCard, { NewsCardLoading } from '../../../../molecules/NewsCard';
import { useNewsWidgetEvents } from '../../useNewsWidgetEvents';

const MainNewsWidget = ({
  HEADLINES,
  viewAllCta,
  chunkedNewsItems,
  handleViewAllClick,
  handleCardClick,
}) => {
  const [visibleGroupIndex, setVisibleGroupIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const groupRefs = useRef([]);
  const didMountRef = useRef(false);

  const { onWidgetScroll, onWidgetClick } = useNewsWidgetEvents();

  const ITEMS_PER_PAGE = 2;
  const totalPages = chunkedNewsItems?.length || 1;

  const ANIMATION_DURATION = 300;
  const handleSlideChange = useCallback(() => {
    if (isAnimating) return;
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), ANIMATION_DURATION);
  }, [isAnimating]);

  useEffect(() => {
    groupRefs.current = groupRefs.current.slice(0, chunkedNewsItems.length);
  }, [chunkedNewsItems.length]);

  useEffect(() => {
    const observer = new window.IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = Number(entry.target.getAttribute('data-group-index'));
            setVisibleGroupIndex(index);
            if (didMountRef.current) {
              onWidgetScroll();
            }
          }
        });
        if (!didMountRef.current) {
          didMountRef.current = true;
        }
      },
      {
        root: null,
        threshold: 0.5,
      },
    );

    groupRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => {
      groupRefs.current.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, [chunkedNewsItems.length, onWidgetScroll]);

  return (
    <div className={styles.newsFeedWidget}>
      <div className={styles.newsFeedHeader}>
        <HeadlineCarousel
          headlines={HEADLINES ?? ['Latest News']}
          icon={NewsIcon}
          direction="horizontal"
        />
        <div className={styles.headerRight}>
          <span
            className={cx(styles.viewAll, {
              [styles.viewAllDarkMode]: isDarkMode(),
            })}
            onClick={handleViewAllClick}
            role="button"
            tabIndex={0}
          >
            {viewAllCta}
          </span>
        </div>
      </div>

      <div className={styles.overScrollContainer}>
        {chunkedNewsItems?.map((group, groupIndex) => (
          <div
            key={groupIndex}
            className={styles.newsGroup}
            ref={(el) => {
              groupRefs.current[groupIndex] = el;
            }}
            data-group-index={groupIndex}
          >
            {group?.length > 0
              ? group.map((item) => (
                  <NewsCard
                    key={item.id}
                    item={item}
                    handleCardClick={handleCardClick}
                    eventTrigger={onWidgetClick}
                  />
                ))
              : [1, 2].map((_, index) => <NewsCardLoading key={index} />)}
          </div>
        ))}
      </div>

      <div className={styles.paginationDots}>
        {[...Array(totalPages)].map((_, index) => (
          <button
            key={index}
            type="button"
            className={`${styles.dot} ${index === visibleGroupIndex ? styles.active : ''}`}
            onClick={() => handleSlideChange(index * ITEMS_PER_PAGE)}
            aria-label={`Go to page ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default MainNewsWidget;
