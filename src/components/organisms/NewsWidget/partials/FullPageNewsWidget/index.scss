@import '~@paytm-h5-common/paytm_common_ui/styles/colors';
@import '~@paytm-money/utils-frontend/styles/pml-typography';
@import '~@paytm-money/utils-frontend/styles/pml-mixins';

.headerTitle {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: 4px;
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  color: var(--text-neutral-strong);
}

.backButton {
  border: 1px solid var(--backgrounds-offset1);
  border-radius: 50%;
  padding: 10px;
}

.fullPageHeader {
  margin: 0;
  position: relative;
  z-index: 1;
  background-color: var(--background-universal-strong);

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: -32px;
    height: 32px;
    background-color: transparent;
    box-shadow: 0 -16px 0 0 var(--background-universal-strong);
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    z-index: -1;
  }
}

.tabContainer {
  display: flex;
  justify-content: flex-start;
  padding: 16px 8px 12px 8px;
  border-bottom: none;
  flex-shrink: 0;
  gap: 8px;
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  z-index: 1;

  .tabChip {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px 12px;
    border-radius: 8px;
    background-color: #ffffff;
    color: #101010;
    cursor: pointer;
    transition:
      background-color 0.3s ease,
      color 0.3s ease,
      border-color 0.3s ease;
  }
  .tabChipDark {
    background-color: #101010;
    color: #eeeeee;
  }

  .activeTab {
    background-color: #101010;
    color: #ffffff;
    font-weight: 600;
  }
  .activeTabDark {
    background-color: #eeeeee;
    color: #101010;
  }
}

.fullPageContainer {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #e8e1e1;
  z-index: 999;

  .mainContainer {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    display: flex;
    flex-direction: column;
    gap: 8px;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}
.fullPageContainerDark {
  background-color: #393434;
}
