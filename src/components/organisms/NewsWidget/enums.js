import { isDarkMode } from '../../../utils/commonUtil';

export const NEWS_CATEGORY = {
  HOT: 'Hot News',
  CORPORATE: 'Corporate News',
};

export const NEWS_CATEGORY_TABS = [
  { tab: NEWS_CATEGORY.HOT, label: `🔥 ${NEWS_CATEGORY.HOT}` },
  { tab: NEWS_CATEGORY.CORPORATE, label: NEWS_CATEGORY.CORPORATE },
];

export const WIDGET_EVENTS = {
  VIEW_ALL_SCROLL: {
    verticalName: '/news_widget',
    screenName: 'news_widget',
    category: 'news',
    action: 'news_widget_viewall_scroll',
  },
  VIEW_ALL_TAB: {
    verticalName: '/news_widget',
    screenName: 'news_widget',
    category: 'news',
    action: 'news_widget_viewall_tabs',
  },
};

export const ACTIONS = (styles) => [
  { type: 'S', label: 'Sell', style: styles.sell },
  { type: 'B', label: 'Buy', style: styles.buy },
];

export const CTA_ACTIONS_TYPE = {
  SELL: 'S',
  BUY: 'B',
};

export const LOCAL_STORAGE = {
  NEWS_POPUP_DATA: 'NEWS_POPUP_DATA',
  NEWS_WIDGET_ALL_NEWS: 'NEWS_WIDGET_ALL_NEWS',
};

export const event_actions = {
  NEWS_WIDGET_SCROLL: 'news_widget_scroll',
  NEWS_WIDGET_CLICKED: 'news_widget_clicked',
  NEWS_WIDGET_VIEWALL: 'news_widget_viewall',
  NEWS_WIDGET_VIEWALL_SCROLL: 'news_widget_viewall_scroll',
  NEWS_WIDGET_VIEWALL_CLICKED: 'news_widget_viewall_clicked',
  NEWS_WIDGET_VIEWALL_TABS: 'news_widget_viewall_tabs',
  NEWS_WIDGET_BUY: 'news_widget_buy',
  NEWS_COMPANY_PAGE_OPEN: 'news_company_page_open',
};

export const SCREEN_NAME = '/news_widget';

export const VERTICAL_NAME = 'news_widget';

export const EVENT = {
  CUSTOM_EVENT: 'custom_event',
};

export const EVENT_CATEGEORY = 'news';

export const STATUS_BAR_COLOR = {
  DRAWER_OPEN: isDarkMode() ? '#101010e3' : '#101010b2',
  DRAWER_CLOSE: isDarkMode() ? '#1a1a1a' : '#ffffff',
};

export const BOTTOM_BAR_COLOR = {
  NEWS_FULL_PAGE: {
    OPEN: { DARK: '#101010', LIGHT: '#ffffff' },
    CLOSE: { DARK: '#1a1a1a', LIGHT: '#ffffff' },
  },
  DRAWER: {
    OPEN: {
      DARK: '#1E1F21',
      LIGHT: '#FEFEFE',
    },
    CLOSE: {
      DARK: '#1a1a1a',
      LIGHT: '#FEFEFE',
    },
  },
};

export const WIDGET_LIST_STATUS_BAR_COLOR = {
  PAGE_OPEN: isDarkMode() ? '#101010' : '#ffffff',
  PAGE_CLOSE: isDarkMode() ? '' : '',
};
