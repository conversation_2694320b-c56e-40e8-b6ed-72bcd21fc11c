import { useRef } from 'react';
import { useAnalyticsEventForWidget } from '../../../hooks/analyticsHooks';
import {
  EVENT,
  EVENT_CATEGEORY,
  SCREEN_NAME,
  VERTICAL_NAME,
  event_actions,
} from './enums';
import { log } from '../../../utils/commonUtil';

const useNewsWidgetEvents = () => {
  const { sendAnalyticsEventWidget } = useAnalyticsEventForWidget();

  const handleEvents = (params) => {
    const eventData = {
      screenName: params?.screenName || SCREEN_NAME,
      event: params?.event || EVENT.CUSTOM_EVENT,
      category: params?.category || EVENT_CATEGEORY,
      verticalName: params?.verticalName || VERTICAL_NAME,
      label: params?.label,
      ...params,
    };

    log('#### News Widget Event:', eventData);
    sendAnalyticsEventWidget(eventData);
  };

  const events = useRef({
    // When user scrolls horizontally on news widget
    onWidgetScroll: () => {
      handleEvents({
        screenName: SCREEN_NAME,
        category: EVENT_CATEGEORY,
        event: EVENT.CUSTOM_EVENT,
        verticalName: VERTICAL_NAME,
        action: event_actions.NEWS_WIDGET_SCROLL,
      });
    },
    // When user clicks on any news on news widget
    onWidgetClick: ({ label = '' }) => {
      handleEvents({
        screenName: SCREEN_NAME,
        category: EVENT_CATEGEORY,
        event: EVENT.CUSTOM_EVENT,
        verticalName: VERTICAL_NAME,
        action: event_actions.NEWS_WIDGET_CLICKED,
        label,
      });
    },
    // When user clicks view all of news widget or lands on view all page
    onViewAll: () => {
      handleEvents({
        screenName: SCREEN_NAME,
        category: EVENT_CATEGEORY,
        event: EVENT.CUSTOM_EVENT,
        verticalName: VERTICAL_NAME,
        action: event_actions.NEWS_WIDGET_VIEWALL,
      });
    },
    // When user scrolls vertically on view all page
    onViewAllScroll: () => {
      handleEvents({
        screenName: SCREEN_NAME,
        category: EVENT_CATEGEORY,
        event: EVENT.CUSTOM_EVENT,
        verticalName: VERTICAL_NAME,
        action: event_actions.NEWS_WIDGET_VIEWALL_SCROLL,
      });
    },
    // When user clicks on any news on the view all page
    onViewAllClick: ({ label = '' }) => {
      handleEvents({
        screenName: SCREEN_NAME,
        category: EVENT_CATEGEORY,
        event: EVENT.CUSTOM_EVENT,
        verticalName: VERTICAL_NAME,
        action: event_actions.NEWS_WIDGET_VIEWALL_CLICKED,
        label,
      });
    },
    // When user switches between hot and corporate on the view all page
    onTabChange: ({ label = '' }) => {
      handleEvents({
        screenName: SCREEN_NAME,
        category: EVENT_CATEGEORY,
        event: EVENT.CUSTOM_EVENT,
        verticalName: VERTICAL_NAME,
        action: event_actions.NEWS_WIDGET_VIEWALL_TABS,
        label,
      });
    },
    // When user clicks on Buy button on news bottom sheet
    onBuyClick: ({ label = '' }) => {
      handleEvents({
        screenName: SCREEN_NAME,
        category: EVENT_CATEGEORY,
        event: EVENT.CUSTOM_EVENT,
        verticalName: VERTICAL_NAME,
        action: event_actions.NEWS_WIDGET_BUY,
        label,
      });
    },
    // When user clicks on Company Name on news bottom sheet and lands on company page
    onCompanyPageOpen: ({ label = '' }) => {
      handleEvents({
        screenName: SCREEN_NAME,
        category: EVENT_CATEGEORY,
        event: EVENT.CUSTOM_EVENT,
        verticalName: VERTICAL_NAME,
        action: event_actions.NEWS_COMPANY_PAGE_OPEN,
        label,
      });
    },
  });

  return events.current;
};

export { useNewsWidgetEvents };
