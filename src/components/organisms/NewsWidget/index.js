import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { openDeepLinkPaytmMoney, exitApp } from '@src/utils/bridgeUtils';
import { log } from '../../../utils/commonUtil';
import { isPaytmMoney } from '../../../utils/coreUtil';
import { newsWidgetListPageDeeplink } from '../../../utils/constants';
import { fetchNews, formatNewsData, stringifySafe } from './utils';
import MainNewsWidget from './partials/MainNewsWidget';
import FullPageNewsWidget from './partials/FullPageNewsWidget';
import { LOCAL_STORAGE } from './enums';
import { useNewsWidgetEvents } from './useNewsWidgetEvents';
import { withErrorBoundary } from '../../../HOC/WidgetErrorBoundary';

const NewsWidget = (props) => {
  const {
    data: widgetData,
    businessTypeFallback,
    isListPage = false,
    showNewsWidgetPopupHandler = () => {},
    setUserSelectedNewsItem = () => {},
  } = props;
  log('widgetData: ', widgetData);

  const [currentNews, setCurrentNews] = useState([]);
  const [allNewsItems, setAllNewsItems] = useState([]);
  const [isFullPage, setIsFullPage] = useState(isListPage);

  const { onViewAll } = useNewsWidgetEvents();

  useEffect(() => {
    if (isFullPage) {
      // Lock scroll
      document.body.style.overflow = 'hidden';
    } else {
      // Restore scroll
      document.body.style.overflow = '';
    }

    // Clean up just in case
    return () => {
      document.body.style.overflow = '';
    };
  }, [isFullPage]);

  const {
    // newsItems,
    chunkedNewsItems,
    widgetTitleArray,
    viewAllCta,
    // subCohortId,
  } = useMemo(() => formatNewsData(widgetData), [widgetData]);

  useEffect(() => {
    if (chunkedNewsItems) {
      setCurrentNews(chunkedNewsItems);
    }
  }, [chunkedNewsItems]);

  const handleBackClick = useCallback(() => {
    if (isPaytmMoney()) {
      exitApp();
    } else {
      setIsFullPage(false);
    }
  }, []);

  const handleViewAllClick = useCallback(() => {
    onViewAll({
      label: widgetData?.data?.meta?.businessType ?? businessTypeFallback,
    });

    if (isPaytmMoney()) {
      openDeepLinkPaytmMoney(newsWidgetListPageDeeplink);
    } else {
      setIsFullPage(true);
    }
  }, [businessTypeFallback, onViewAll, widgetData?.data?.meta?.businessType]);

  const handleCardClick = useCallback(
    (item) => {
      const newsItem = stringifySafe(item);
      try {
        localStorage.setItem(
          LOCAL_STORAGE.NEWS_POPUP_DATA,
          JSON.stringify(item),
        );
      } catch (error) {
        console.error('Error setting news popup data in localStorage:', error);
        localStorage.setItem(
          LOCAL_STORAGE.NEWS_POPUP_DATA,
          JSON.stringify(stringifySafe(newsItem)),
        );
      }
      setUserSelectedNewsItem(item);
      showNewsWidgetPopupHandler();
    },
    [setUserSelectedNewsItem, showNewsWidgetPopupHandler],
  );

  useEffect(() => {
    fetchNews('HOT', 0, allNewsItems, setAllNewsItems);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (currentNews?.length === 0) return null;

  if (isFullPage) {
    return (
      <FullPageNewsWidget
        allNewsItems={allNewsItems}
        handleBackClick={handleBackClick}
        handleCardClick={handleCardClick}
      />
    );
  }

  return (
    <MainNewsWidget
      HEADLINES={widgetTitleArray}
      handleViewAllClick={handleViewAllClick}
      viewAllCta={viewAllCta}
      chunkedNewsItems={chunkedNewsItems}
      handleCardClick={handleCardClick}
    />
  );
};

export default React.memo(withErrorBoundary(NewsWidget));
