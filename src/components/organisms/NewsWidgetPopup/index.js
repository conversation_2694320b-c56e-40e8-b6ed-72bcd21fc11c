import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Drawer from '../../molecules/Drawer/Drawer';
import { isPaytmMoney } from '../../../utils/coreUtil';
import {
  exitApp,
  paytmChangeBottomBarColorBridge,
  paytmChangeStatusBarColorBridge,
} from '../../../utils/bridgeUtils';

import NewsWidgetPopupCard from './partials/NewsWidgetPopupCard';
import {
  BOTTOM_BAR_COLOR,
  LOCAL_STORAGE,
  STATUS_BAR_COLOR,
} from '../NewsWidget/enums';
import { getSingleNews } from '../NewsWidget/utils';
import NewsWidgetPopupCardLoading from './partials/NewsWidgetPopupCardLoading';
import { isDarkMode, isIosBuild, log } from '../../../utils/commonUtil';
import { useBackPress } from '../../../hooks/useNativeBackPress';

const NewsWidgetPopup = ({
  history,
  navigateTo,
  isOpen = false,
  onClose,
  companyPageNavigation = () => {},
  userSelectedNewsItem,
  isListPage = false,
}) => {
  log('userSelectedNewsItem', userSelectedNewsItem);
  const [singleNewsItem, setSingleNewsItem] = useState();
  const [isLoading, setIsloading] = useState(true);

  const { stack } = useBackPress();

  const userSelectedNewsItemFromLS = useMemo(
    () => JSON.parse(localStorage.getItem(LOCAL_STORAGE.NEWS_POPUP_DATA)),
    [],
  );
  log('userSelectedNewsItemFromLS', userSelectedNewsItemFromLS);

  const preSelectedNewsItem = useMemo(
    () => userSelectedNewsItem || userSelectedNewsItemFromLS,
    [userSelectedNewsItem, userSelectedNewsItemFromLS],
  );

  useEffect(() => {
    if (isPaytmMoney() && !isIosBuild()) {
      const bottomBarColor = isDarkMode()
        ? BOTTOM_BAR_COLOR.DRAWER.OPEN.DARK
        : BOTTOM_BAR_COLOR.DRAWER.OPEN.LIGHT;
      // paytmChangeStatusBarColorBridge(STATUS_BAR_COLOR.DRAWER_OPEN);
      log('bottomBarColor news popup', bottomBarColor);
      paytmChangeBottomBarColorBridge(bottomBarColor);
    }

    // return () => {
    //   if (isPaytmMoney()) {
    //     paytmChangeStatusBarColorBridge(STATUS_BAR_COLOR.DRAWER_CLOSE);
    //     paytmChangeBottomBarColorBridge(BOTTOM_BAR_COLOR.DRAWER.CLOSE);
    //   }
    // };
  }, []);

  useEffect(() => {
    if (!preSelectedNewsItem && !isOpen) return;
    const fetchSingleNewsItem = async () => {
      try {
        const newsItem = await getSingleNews(preSelectedNewsItem);
        if (newsItem) {
          setSingleNewsItem(newsItem);
          return;
        }
        setSingleNewsItem(preSelectedNewsItem);
      } catch (error) {
        setSingleNewsItem(preSelectedNewsItem);
        console.error('[fetchSingleNewsItem()] Error fetching news:', error);
        return;
      } finally {
        setIsloading(false);
      }
    };

    fetchSingleNewsItem();
  }, [isOpen, preSelectedNewsItem]);

  const closePopup = useCallback(() => {
    localStorage.removeItem(LOCAL_STORAGE.NEWS_POPUP_DATA);
    setSingleNewsItem();
    setIsloading(true);

    if (!isPaytmMoney() || isListPage) {
      onClose();
      stack.pop();
    } else {
      exitApp();
    }
  }, [isListPage, onClose, stack]);

  if (!singleNewsItem) return null;

  return (
    <Drawer
      showCustomCloseIcon
      active={isOpen}
      showCloseIcon={false}
      triggerClose={closePopup}
    >
      {isLoading ? (
        <NewsWidgetPopupCardLoading />
      ) : (
        <NewsWidgetPopupCard
          newsItem={singleNewsItem}
          companyPageNavigation={companyPageNavigation}
          navigateTo={navigateTo}
          history={history}
        />
      )}
    </Drawer>
  );
};

export default React.memo(NewsWidgetPopup);
