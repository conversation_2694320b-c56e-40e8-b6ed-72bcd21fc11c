import cx from 'classnames';
import ArrowLeftIcon from '@paytm-h5-common/paytm_common_ui/icons/system/nav/ArrowLeft';
import FallbackCompanyIcon from '@assets/icons/company_fallback_icon.svg';
import CompanyIcon from '../../../../atoms/CompanyIcon/CompanyIcon';
import StockChange from '../../../StockChange/StockChange';
import { isDarkMode } from '../../../../../utils/commonUtil';
import {
  ACTIONS,
  CTA_ACTIONS_TYPE,
  NEWS_CATEGORY_TABS,
} from '../../../NewsWidget/enums';
import { handleBuySellClick } from '../../../NewsWidget/utils';
import { useNewsWidgetEvents } from '../../../NewsWidget/useNewsWidgetEvents';
import styles from './index.scss';

const NewsWidgetPopupCard = ({
  newsItem = {},
  companyPageNavigation,
  navigateTo,
  history,
}) => {
  const { onBuyClick, onCompanyPageOpen } = useNewsWidgetEvents();

  return (
    <div className={styles.newsPopupWrapper}>
      <div className={styles.newsPopupHeader}>
        <div
          className={styles.newsHeaderContent}
          onClick={() => {
            onCompanyPageOpen({ label: newsItem?.pml_id || 'NA' });
            companyPageNavigation(newsItem);
          }}
        >
          <div className={styles.companyLogo}>
            <CompanyIcon
              name={newsItem.pml_id}
              type="stocks"
              url={newsItem.companyLogo}
              fallbackImg={FallbackCompanyIcon}
            />
          </div>
          <div className={styles.titleSection}>
            <div className={styles.titleRow}>
              <span className={styles.title}>{newsItem.companyName}</span>
              <div className={styles.arrowIcon}>
                <ArrowLeftIcon />
              </div>
            </div>
            <div className={styles.priceInfo}>
              <span className={styles.price}>{newsItem.price}</span>
              <StockChange
                exchange={newsItem.exchange}
                segment={newsItem.segment}
                securityId={newsItem.security_id}
                instrumentType={newsItem.instrument}
                id={newsItem.pml_id}
                showLtp
              />
            </div>
          </div>
        </div>
      </div>

      <div className={styles.newsDetailBody}>
        <div className={styles.newsDetailContent}>
          <div
            className={cx(styles.hotNewsContainer, {
              [styles.hotNewsContainerDark]: isDarkMode(),
              [styles.hotNewsContainerLight]: !isDarkMode(),
            })}
          >
            {newsItem.isHotNews && (
              <div
                className={cx(styles.hotNewsTag, {
                  [styles.hotNewsTagBgDark]: isDarkMode(),
                  [styles.hotNewsTagBgLight]: !isDarkMode(),
                })}
              >
                <span>
                  {
                    NEWS_CATEGORY_TABS.find((tab) => tab.tab === 'Hot News')
                      .label
                  }
                </span>
              </div>
            )}
            <div className={styles.newsHeader}>
              <div className={styles.newsTitle}>{newsItem.news}</div>
              <div className={styles.timestamp}>{newsItem.timestamp}</div>
              {newsItem.caption ? (
                <div className={styles.fullNews}>{newsItem.caption}</div>
              ) : null}
            </div>
          </div>
          {newsItem?.fullNews && (
            <div className={styles.fullNews}>{newsItem.fullNews}</div>
          )}
        </div>
      </div>

      <div className={styles.newsPopupActionButtons}>
        {ACTIONS(styles).map(({ type, label, style }) => (
          <button
            key={type}
            type="button"
            className={`${styles.button} ${style}`}
            onClick={() => {
              if (type === CTA_ACTIONS_TYPE.BUY) {
                onBuyClick({ label: newsItem?.pml_id || 'NA' });
              }
              handleBuySellClick(newsItem, type, navigateTo, history);
            }}
          >
            {label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default NewsWidgetPopupCard;
