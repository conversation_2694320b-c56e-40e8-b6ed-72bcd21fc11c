@import '/src/commonStyles/variables.scss';

html,
body {
  background-color: transparent !important;
}

.newsPopupWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-pop-up);
  overflow-y: hidden;
  margin: 0 !important;
  padding: 0;
  box-sizing: border-box;

  .newsPopupHeader {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 0;
    padding-top: 0;
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--background-pop-up);
    margin: 0;
    box-sizing: border-box;
    flex-shrink: 0;

    .newsHeaderContent {
      margin-top: 0px;
      display: flex;
      align-items: center;
      padding: 0;
      gap: 12px;
      min-width: 0;

      .companyLogo {
        width: 36px;
        height: 36px;
        padding: 1px;
        background: var(--background-pop-up);
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .titleSection {
        min-width: 0;
        max-width: 70vw;

        .titleRow {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-bottom: 2px;
          width: 100%;

          .title {
            color: var(--icon-neutral-strong);
            font-size: 16px;
            font-weight: 500;
            letter-spacing: -0.01em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            // max-width: 80%;
          }

          .arrowIcon {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--icon-neutral-strong);
            transform: rotate(180deg);
            flex-shrink: 0;

            svg {
              -webkit-tap-highlight-color: transparent; // Disable tap/click highlight
              outline: none; // Remove outline on click/focus (optional)
            }
          }
        }

        .priceInfo {
          display: flex;
          align-items: baseline;
          width: max-content;

          .price {
            color: var(--icon-neutral-strong);
            font-size: 14px;
            font-weight: 400;
          }

          .change {
            font-size: 14px;
            font-weight: 400;

            &.positive {
              color: rgba(34, 197, 94, 0.95);
            }

            &.negative {
              color: rgba(239, 68, 68, 0.95);
            }
          }
        }
      }
    }
  }

  .newsDetailBody {
    flex-grow: 1;
    min-height: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    gap: 16px;
    max-height: 60vh;
    .newsDetailContent {
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
    }

    .newsDetailContentHeader {
      padding: 16px;
      border-radius: 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .hotNewsContainer {
      padding: 12px;
      border-radius: 12px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .hotNewsContainerLight {
      background: linear-gradient(
        90deg,
        rgba(213, 232, 255, 0.5) 0%,
        rgba(211, 212, 255, 0.5) 100%
      );
    }
    .hotNewsContainerDark {
      background: linear-gradient(
        90deg,
        var(--Vivid-gradientBlueLight, rgba(0, 17, 40, 0.5)) 0%,
        var(--Vivid-gradientPurpleLight, rgba(28, 17, 87, 0.5)) 100%
      );
    }

    .hotNewsTag {
      width: fit-content;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2px 4px;

      span {
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        background: linear-gradient(90deg, #fed533 0%, #eb4b4b 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .hotNewsTagBgLight {
      background: linear-gradient(
        90deg,
        var(--Vivid-yellowLight, rgba(255, 246, 178, 0.5)) 0%,
        var(--BG-lossAndErrorMedium, rgba(255, 235, 239, 0.5)) 100%
      );
    }
    .hotNewsTagBgDark {
      background: linear-gradient(
        90deg,
        var(--Vivid-yellowLight, rgba(67, 52, 0, 0.5)) 0%,
        var(--BG-lossAndErrorMedium, rgba(64, 17, 27, 0.5)) 100%
      );
    }

    .newsHeader {
      display: flex;
      flex-direction: column;
    }

    .newsTitle {
      color: var(--icon-neutral-strong);
      font-family: Inter;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
      letter-spacing: 0.01px;
      margin: 0;
    }

    .timestamp {
      color: var(--icon-neutral-strong);
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      margin-top: 8px;
    }

    .fullNews {
      margin-top: 24px;
      color: var(--icon-neutral-strong);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }
  }

  .newsPopupActionButtons {
    padding: 16px 0;
    display: flex;
    gap: 8px;
    background: var(--background-pop-up);
    position: sticky;
    bottom: 0;
    z-index: 1;
    box-sizing: border-box;
    flex-shrink: 0;

    .button {
      flex: 1;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: opacity 0.3s ease;
      width: 166px;
      height: 52px;
      gap: 4px;
      border-radius: 48px;
      padding-top: 15px;
      padding-right: 12px;
      padding-bottom: 15px;
      padding-left: 12px;

      &.buy {
        background: rgba(44, 176, 121, 1);
        color: rgba(255, 255, 255, 1);
        border: none;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;

        &:hover {
          opacity: 0.9;
        }
      }

      &.sell {
        background: rgba(235, 75, 75, 1);
        color: rgba(255, 255, 255, 1);
        border: none;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;

        &:hover {
          opacity: 0.9;
        }
      }
    }
  }
}
