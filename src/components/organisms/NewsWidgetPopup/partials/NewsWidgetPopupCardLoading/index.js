import cx from 'classnames';
import { isDarkMode } from '../../../../../utils/commonUtil';
import styles from './index.scss';
import Shimmer from '../../../../atoms/Shimmer';
import { ACTIONS } from '../../../NewsWidget/enums';

const NewsWidgetPopupCardLoading = () => (
  <div className={styles.newsPopupWrapper}>
    <div className={styles.newsPopupHeader}>
      <div className={styles.newsHeaderContent}>
        <div className={styles.companyLogo}>
          <Shimmer
            type="circle"
            width="38px"
            height="38px"
            borderRadius="2px"
          />
        </div>
        <div className={styles.titleSection}>
          <div className={styles.titleRow}>
            <Shimmer type="line" width="45%" height="20px" />
          </div>
          <div className={styles.priceInfo}>
            <Shimmer type="line" width="30%" height="20px" />
          </div>
        </div>
      </div>
    </div>

    <div className={styles.newsDetailBody}>
      <div className={styles.newsDetailContent}>
        <div
          className={cx(styles.hotNewsContainer, {
            [styles.hotNewsContainerDark]: isDarkMode(),
            [styles.hotNewsContainerLight]: !isDarkMode(),
          })}
        >
          <div className={styles.newsHeader}>
            <div className={styles.newsTitle}>
              <Shimmer type="line" width="80%" height="20px" />
            </div>
            <div className={styles.timestamp}>
              <Shimmer type="line" width="20%" height="20px" />
            </div>
            <Shimmer type="line" width="100%" height="20px" />
            <Shimmer type="line" width="100%" height="20px" />
          </div>
        </div>
        <div className={styles.fullPageContentShimmerContainer}>
          {[1, 2, 3, 4, 5, 6, 7].map((_, index) => (
            <Shimmer key={index} type="line" width="100%" height="20px" />
          ))}
        </div>
      </div>
    </div>

    <div className={styles.newsPopupActionButtons}>
      {ACTIONS(styles).map(({ type }) => (
        <Shimmer key={type} type="line" width="50%" height="50px" />
      ))}
    </div>
  </div>
);

export default NewsWidgetPopupCardLoading;
