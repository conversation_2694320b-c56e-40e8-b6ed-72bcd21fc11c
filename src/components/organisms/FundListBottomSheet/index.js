import { useMemo, useEffect, useState } from 'react';
import cx from 'classnames';
import DrawerV2, { ALIGNMENTS } from '../../molecules/DrawerV2';
import styles from './FundListBottomSheet.module.scss';
import FundTileView from '../../molecules/FundTileView/FundTileView';
import { isDarkMode } from '../../../utils/commonUtil';
import darkBackIcon from '../../../assets/icons/dark_back.png';
import lightBackIcon from '../../../assets/icons/light_back.png';
import {
  EVENT_ACTION_GENERIC_WIDGET,
  PULSE_STATICS_GENERIC_WIDGET,
} from '../../../utils/constants';
import { useGenericWidgetAnalyticsEvents } from '../../../hooks/useGenericWidgetAnalyticsEvents';

function FundListBottomSheet({
  isOpen,
  onClose,
  fundData,
  selectedDuration,
  onFundClick,
}) {
  const { sendAnalyticsEventGenWidget } = useGenericWidgetAnalyticsEvents();
  const [maxScrollPercentage, setMaxScrollPercentage] = useState(0);

  useEffect(() => {
    if (isOpen) {
      sendAnalyticsEventGenWidget({
        action: EVENT_ACTION_GENERIC_WIDGET.L2_PAGE_LANDED,
        event: PULSE_STATICS_GENERIC_WIDGET.EVENT_OPEN_SCREEN,
      });
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen && maxScrollPercentage > 0) {
      sendAnalyticsEventGenWidget({
        action: EVENT_ACTION_GENERIC_WIDGET.L2_PAGE_SCROLLED,
        label: Math.floor(maxScrollPercentage),
      });
      setMaxScrollPercentage(0);
    }
  }, [isOpen, maxScrollPercentage, sendAnalyticsEventGenWidget]);

  const contextData = fundData?.widget?.attributes?.find(
    (attr) => attr.name === 'GENERIC_DATA_FETCH',
  )?.value;

  const allFunds = useMemo(
    () =>
      fundData?.widget?.attributes?.find(
        (attr) => attr.name === 'GENERIC_MF_SECTOR_WISE_SCHEMES',
      )?.value || [],
    [fundData],
  );

  const getReturnValue = (fund, duration) => {
    const returnData = fund.fundReturns.returns.find(
      (r) => r.name === duration,
    );
    return returnData ? returnData.percentage : -Infinity;
  };

  const sortedFunds = useMemo(
    () =>
      [...allFunds].sort(
        (a, b) =>
          getReturnValue(b, selectedDuration) -
          getReturnValue(a, selectedDuration),
      ),
    [allFunds, selectedDuration],
  );

  const getFormattedReturns = (fund, duration) => {
    const returnData = fund.fundReturns.returns.find(
      (r) => r.name === duration,
    );
    return returnData
      ? `${returnData.percentage > 0 ? '+' : ''}${returnData.percentage.toFixed(2)}%`
      : 'N/A';
  };

  return (
    <DrawerV2
      isOpen={isOpen}
      onClose={onClose}
      align={ALIGNMENTS.LEFT}
      className={cx(styles.fullScreenDrawer, {
        [styles.darkMode]: isDarkMode(),
      })}
      showHandleBar={false}
      showCross={false}
      title={null}
      customContentClass={styles.maxHeight90vh}
      updateScroll={(scrollTop, scrollHeight, clientHeight) => {
        const scrollPercentage =
          (scrollTop / (scrollHeight - clientHeight)) * 100;
        setMaxScrollPercentage((prev) => Math.max(prev, scrollPercentage));
      }}
    >
      <div
        className={cx(styles.fundListHeader, {
          [styles.darkMode]: isDarkMode(),
        })}
      >
        <img
          src={isDarkMode() ? darkBackIcon : lightBackIcon}
          alt="Back Icon"
          className={cx(styles.fundListBackIcon, {
            [styles.darkMode]: isDarkMode(),
          })}
          style={{
            width: '40px',
            height: '40px',
            cursor: 'pointer',
          }}
          onClick={() => {
            sendAnalyticsEventGenWidget({
              action: EVENT_ACTION_GENERIC_WIDGET.L2_PAGE_BACK_CLICKED,
            });
            onClose();
          }}
        />
        <div className={styles.fundListTitleContainer}>
          <h2
            className={cx(styles.fundListTitle, {
              [styles.darkMode]: isDarkMode(),
            })}
          >
            {contextData?.l2_title}
          </h2>
          <p
            className={cx(styles.fundListSubtitle, {
              [styles.darkMode]: isDarkMode(),
            })}
          >
            {contextData?.l2_subtitle}
          </p>
        </div>
      </div>
      <div
        className={cx(styles.fundListScrollableContent, {
          [styles.darkMode]: isDarkMode(),
        })}
      >
        {sortedFunds.map((fund) => (
          <FundTileView
            key={fund.isin}
            logoUrl={fund.amcLogo}
            name={fund.schemeName}
            category={fund.category}
            subCategory={fund.subCategory}
            returnsLabel={selectedDuration.toUpperCase()}
            returnsValue={getFormattedReturns(fund, selectedDuration)}
            minAmount={fund.minAmount}
            aum={fund.aum}
            onClick={() => {
              sendAnalyticsEventGenWidget({
                action: EVENT_ACTION_GENERIC_WIDGET.L2_PAGE_FUND_CARD_CLICKED,
              });
              onFundClick(fund);
            }}
          />
        ))}
      </div>
    </DrawerV2>
  );
}

export default FundListBottomSheet;
