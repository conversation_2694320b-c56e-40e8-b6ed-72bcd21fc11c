.fullScreenDrawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom, #d0e5ff, #ffffff);
  z-index: 999;
  box-sizing: border-box;
  border-radius: 0;

  &.darkMode {
    background-image: linear-gradient(to bottom, #162738 10%, #0f0f0f);
  }
}

.fundListHeader {
  margin-bottom: 20px;
  padding: 16px 16px 0 16px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;

  &.darkMode {
    color: var(--grey900);
  }

  .fundListBackIcon {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-bottom: 8px;

    &.darkMode {
      color: var(--grey900);
    }
  }

  .fundListTitleContainer {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .fundListTitle {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 4px;
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.darkMode {
      color: var(--grey900);
    }
  }

  .fundListSubtitle {
    font-size: 14px;
    color: var(--text-neutral-medium);
    margin-top: 0;

    &.darkMode {
      color: var(--text-neutral-medium-dark);
    }
  }
}

.fundListScrollableContent {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 16px 16px 55px 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: var(--secondaryOffsetVariant);
  border-radius: 16px 16px 0 0;

  &.darkMode {
    background-color: var(--secondaryOffsetVariant);
  }
}

.customFullScreenOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom, #d0e5ff, #ffffff);
  z-index: 999;

  &.darkMode {
    background-image: linear-gradient(to bottom, #192d42, #1b263b);
  }
}

.maxHeight90vh {
  max-height: 95vh;
}
