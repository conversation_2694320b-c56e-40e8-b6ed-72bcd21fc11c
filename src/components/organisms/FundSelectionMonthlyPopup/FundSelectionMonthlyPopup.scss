@import '/src/commonStyles/variables.scss';

.fundSelectionPopupContainer {
  height: 85vh;
  [data-testid="button"] {
    border-radius: 48px;
    background: var(--Colors-Primary, #101010) !important;
  }
}

.scrollContainer {
  background-color: var(--background-pop-up);
  height: calc(85vh - 162px);
  overflow-y: auto;
  width: 100%;
}

.title {
  font-family: Inter, sans-serif;
  text-align: left;
  margin: 8px 0 0; 

  @include typography(heading1B4, var(--text-neutral-strong))
}

.fundList {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 10px;
}

.fundCard {
  border: 2px solid var(--background-offset-strong);
  border-radius: 10px;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.fundCard.selected {
  border-color: var(--border-primary-strong);
  z-index: 100;
}

.fundHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  gap: 10px;
}

.fundInfo {
  flex-grow: 1;
  margin-left: 10px;
  overflow: hidden;
}

.fundDetailLabel {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.fundDetailLabel{
  font-size: 12px;
  font-weight: 400;
  color: #1010108A;
}

.fundDetailValue {
  font-size: 14px;
  font-weight: 500;
  color: #101010;
}

.fundName {
  margin: 0;
  word-wrap: break-word;
  white-space: normal;

  @include typography(text2, var(--text-neutral-strong));
}

.fundType {
  margin-top: 5px;
  color: gray;
  word-wrap: break-word;
  @include typography(body2, var(--text-neutral-weak));
}

.radioButton {
  width: 24px;
}

.fundDetailsBoxWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  padding-top : 10px;
  // border-top: 1px solid var(--background-offset-strong);
} 

.fundRating {
  display: flex;
  flex-direction: column;
}

.fundRating span {
  @include typography(body2, var(--text-neutral-weak));
}

.fundReturns {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.fundReturns span:first-child {
  @include typography(body2, var(--text-neutral-weak));
}

.positive {
  @include typography(body2B2, var(--text-positive-strong));
}

.negative {
  @include typography(body2B2, var(--text-negative-strong));
}

.fundInvestorsContainer {
  background-color: var(--background-offset-strong);
  padding: 5px 5px 5px 15px;
  text-align: left;
  margin: -14px 1px 1px 1px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 12px 12px;
  width: calc(100% - 2px);
  box-sizing: border-box;

  > p {
    margin: 0px;
  }
}

.investors {
  margin: 0;
  padding: 2px 6px;
  font-size: 13px;
  @include typography(body3R1, var(--text-neutral-strong));
}

.sipWealthPlanText {
  padding: 2px 12px;
  @include typography(body3R3, var(--text-neutral-weak));
}

.finalWealthAmtText {
  display: inline;
  color: map-get($colors, var(--text-neutral-strong));
}

.stars {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.loadingContainer {
  height: 100%;
  display: flex;
  justify-content: center;
}