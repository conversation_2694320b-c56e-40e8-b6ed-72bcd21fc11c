import React, { useState, useRef, useEffect } from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Loader, RadioButton } from '@paytm-h5-common/paytm_common_ui';

import InfiniteScroll from '../../molecules/InfiniteScroll';
import Drawer from '../../molecules/Drawer/Drawer';
import Icon, { ICONS_NAME } from '../../molecules/Icon/index';

import { getFundList } from '../../../actions/dailySipAction';

import { formatPrice } from '../../molecules/Prices';
import { isPaytmMoney } from '../../../utils/coreUtil';
import { exitApp } from '../../../utils/bridgeUtils';
import { useBackPress } from '../../../hooks/useNativeBackPress';
import { useDailySIPAnalyticsEvents } from '../../../hooks/useDailySIPAnalyticsEvents';

import styles from './FundSelectionMonthlyPopup.scss';
import { convertToLakhsOrCrore } from '../../../pages/SipCard/utils';
import { PULSE_STATICS } from './enums';

const THREE_YEARS = '3y';
const FIVE_YEARS = '5y';

const FUND_DETAIL_BOX_STYLE = {
  display: 'flex',
  flexDirection: 'column',
  gap: 2,
  margin: 0,
};
const FUND_DETAIL_LABEL = {
  fontSize: 12,
  fontWeight: 400,
  color: '#1010108A',
  margin: 0,
};
const FUND_DETAIL_VALUE = {
  fontSize: 14,
  fontWeight: 500,
  color: '#101010',
  marign: 0,
};
const POSITIVE_VALUE = { color: '#21C179' };
// const NEGATIVE_VALUE = {color: '#FF3F3F'};

const FundSelectionMonthlyPopup = ({
  isOpen = false,
  preSelectedFund,
  setFund,
  onClose,
}) => {
  const [funds, setFunds] = useState([]);
  const [selectedFund, setSelectedFund] = useState(null);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [pageNumber, setPageNumber] = useState(1);
  const infiniteScrollRef = useRef(null);

  const { sendAnalyticsEventDailySIP } = useDailySIPAnalyticsEvents();
  const { stack } = useBackPress();

  const fetchFunds = async () => {
    if (hasMoreData && !loading) {
      setLoading(true);
      const { data } = await getFundList(pageNumber, 'MONTHLY');

      setLoading(false);

      const transformedFunds = data.results.map((scheme, index) => ({
        id: funds.length + index + 1,
        ...scheme,
      }));

      const newFundList = [...funds, ...transformedFunds];
      setFunds(newFundList);
      setHasMoreData(data.hasNextPage);
      setPageNumber(pageNumber + 1);

      if (newFundList.length > 0 && !selectedFund) {
        let selectedIndex = newFundList.findIndex(
          ({ isin }) => isin === preSelectedFund?.isin,
        );
        if (selectedIndex === -1) selectedIndex = 0;
        setSelectedFund(newFundList[selectedIndex || 0].id);
      }
    }
  };

  useEffect(() => {
    if (isOpen && preSelectedFund) {
      fetchFunds();

      sendAnalyticsEventDailySIP({
        action: PULSE_STATICS.ACTION.PAGE_LOAD,
      });
    }
  }, [isOpen, preSelectedFund]);

  const handleSelect = (id) => {
    setSelectedFund(id);
  };

  const closePopup = () => {
    const fund = funds.find(({ id }) => id === selectedFund);
    if (isPaytmMoney()) {
      localStorage.setItem('selectedFund', JSON.stringify(fund));
      exitApp();
    } else {
      setFund(fund);
      onClose();
      stack.pop();
    }

    sendAnalyticsEventDailySIP({
      action:
        fund?.isin === preSelectedFund?.isin
          ? PULSE_STATICS.ACTION.DEFAULT_SELECT_FUND
          : PULSE_STATICS.ACTION.NO_DEFAULT_SELECT_FUND,
    });
  };

  const onPopupClose = () => {
    sendAnalyticsEventDailySIP({
      action: PULSE_STATICS.ACTION.ON_CLOSE,
    });
    onClose();
  };

  return (
    <Drawer
      active={isOpen}
      triggerClose={onPopupClose}
      showGrabber
      showCloseIcon
      title={
        <p style={{ fontSize: '24px', fontWeight: 700, margin: 0 }}>
          Most SIP&apos;d Funds
        </p>
      }
      primaryButton={{
        label: 'Select Fund',
        onClick: closePopup,
        style: { borderRadius: '48px', backgroud: '#101010', color: '#FFFFFF' },
      }}
      customClass={styles.fundSelectionPopupContainer}
    >
      <div
        className={styles.scrollContainer}
        style={{
          height: 'calc(85vh - 162px)',
          width: '100%',
          overflow: 'auto',
        }}
        ref={infiniteScrollRef}
      >
        {loading ? (
          <div className={styles.loadingContainer}>
            <Loader size="medium" />
          </div>
        ) : (
          <InfiniteScroll
            pageStart={1}
            initialLoad={!loading}
            loadMore={/* fetchFunds */ () => {}}
            hasMore={hasMoreData}
            useWindow={false}
            useCapture={false}
            threshold={500}
            className={styles.fundList}
            getScrollParent={() => infiniteScrollRef.current}
          >
            {funds.map((fund) => {
              const isCardSelected = selectedFund === fund.id;
              const threeYearReturn = (
                (fund?.fundReturns?.returns || []).find(
                  (ar) => ar.name === THREE_YEARS,
                )?.percentage || 0
              )?.toFixed(2);
              const fiveYearReturn = (
                (fund?.fundReturns?.returns || []).find(
                  (ar) => ar.name === FIVE_YEARS,
                )?.percentage || 0
              )?.toFixed(2);

              return (
                <div
                  key={fund.id}
                  style={{ background: '#ECF2F8', borderRadius: '10px' }}
                >
                  <div
                    className={`${styles.fundCard} ${isCardSelected ? styles.selected : ''}`}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 16,
                      borderRadius: '10px',
                      padding: '16px',
                      border: isCardSelected
                        ? '2px solid #101010'
                        : '1px solid #D8E7F7',
                      background: '#FFFFFF',
                    }}
                    onClick={() => handleSelect(fund.id)}
                  >
                    <div
                      className={styles.fundHeader}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 12,
                        width: '100%',
                      }}
                    >
                      <Icon url={fund.amcLogo} width={40} />
                      <div
                        className={styles.fundInfo}
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          width: '100%',
                          gap: '4px',
                        }}
                      >
                        <div
                          className={styles.fundName}
                          style={{
                            fontSize: '14px',
                            whiteSpace: 'pre-wrap',
                            wordWrap: 'break-word',
                          }}
                        >
                          {fund.schemeName}
                        </div>
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px',
                          }}
                        >
                          <div
                            style={{
                              display: 'inline-flex',
                              alignItems: 'center',
                            }}
                          >
                            <p
                              style={{
                                marign: 0,
                                fontSize: '12px',
                                color: '#1010108A',
                                margin: 0,
                              }}
                            >
                              {fund.rating}
                            </p>
                            <Icon
                              name={ICONS_NAME.RATING_STAR}
                              width={16}
                              inLineStyles={{ width: 'fit-content' }}
                            />
                          </div>
                          <p
                            style={{
                              fontSize: '12px',
                              color: '#1010108A',
                              margin: 0,
                            }}
                          >
                            •
                          </p>
                          <p
                            className={styles.fundType}
                            style={{
                              fontSize: '12px',
                              color: '#1010108A',
                              margin: 0,
                            }}
                          >
                            {fund.category} • {fund.subCategory}
                          </p>
                        </div>
                      </div>
                      {/* <Icon
                        url={
                          selectedFund === fund.id
                            ? SuccessThemedIcon
                            : RadioOffIcon
                        }
                        size={2.4}
                      /> */}
                      <RadioButton
                        checked={selectedFund === fund.id}
                        onChecked={() => handleSelect(fund.id)}
                        customClass={styles.radioButton}
                        style={{ width: 'fit-content' }}
                      />
                    </div>

                    <div
                      style={{
                        width: '100%',
                        border: '1px solid #ECF2F8',
                      }}
                    />

                    <div
                      className={styles.fundDetailsBoxWrapper}
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <div
                        className={styles.fundDetailBox}
                        style={FUND_DETAIL_BOX_STYLE}
                      >
                        <p
                          className={styles.fundDetailLabel}
                          style={FUND_DETAIL_LABEL}
                        >
                          Min SIP
                        </p>
                        <p
                          className={styles.fundDetailValue}
                          style={{ ...FUND_DETAIL_VALUE, margin: 0 }}
                        >
                          ₹ {formatPrice(fund?.minAmount)}
                        </p>
                      </div>

                      <div
                        className={styles.fundDetailBox}
                        style={FUND_DETAIL_BOX_STYLE}
                      >
                        <p
                          className={styles.fundDetailLabel}
                          style={FUND_DETAIL_LABEL}
                        >
                          3 years
                        </p>
                        <p
                          className={styles.fundDetailValue}
                          style={{
                            ...FUND_DETAIL_VALUE,
                            ...POSITIVE_VALUE,
                            margin: 0,
                          }}
                        >
                          {threeYearReturn}%
                        </p>
                      </div>

                      <div
                        className={styles.fundDetailBox}
                        style={FUND_DETAIL_BOX_STYLE}
                      >
                        <p
                          className={styles.fundDetailLabel}
                          style={FUND_DETAIL_LABEL}
                        >
                          5 years
                        </p>
                        <p
                          className={styles.fundDetailValue}
                          style={{
                            ...FUND_DETAIL_VALUE,
                            ...POSITIVE_VALUE,
                            margin: 0,
                          }}
                        >
                          {fiveYearReturn}%
                        </p>
                      </div>

                      <div
                        className={styles.fundDetailBox}
                        style={FUND_DETAIL_BOX_STYLE}
                      >
                        <p
                          className={styles.fundDetailLabel}
                          style={FUND_DETAIL_LABEL}
                        >
                          AUM (Cr)
                        </p>
                        <p
                          className={styles.fundDetailValue}
                          style={{ ...FUND_DETAIL_VALUE, margin: 0 }}
                        >
                          89,903
                        </p>
                      </div>
                    </div>
                  </div>

                  <div
                    style={{
                      padding: '5px 5px 5px 15px',
                      textAlign: 'left',
                      margin: '0 1px 1px 1px',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                      borderRadius: '0 0 12px 12px',
                      width: 'calc(100% - 2px), ',
                    }}
                    className={styles.fundInvestorsContainer}
                  >
                    {fund?.estimatedFutureValue ? (
                      <p
                        className={styles.sipWealthPlanText}
                        style={{
                          fontSize: '10px',
                          color: '#101010B2',
                          margin: 0,
                        }}
                      >
                        ₹
                        {fund?.minAmount < 1000
                          ? '1K '
                          : `${fund.minAmount / 1000}K `}
                        Monthly to{' '}
                        <p
                          className={styles.finalWealthAmtText}
                          style={{ display: 'inline' }}
                        >
                          ₹
                          {convertToLakhsOrCrore(
                            fund?.estimatedFutureValue,
                            fund?.minAmount,
                          )}
                        </p>{' '}
                        * - Your 30 year Wealth Plan 🚀
                      </p>
                    ) : (
                      ''
                    )}
                  </div>
                </div>
              );
            })}
          </InfiniteScroll>
        )}
      </div>
    </Drawer>
  );
};

export default FundSelectionMonthlyPopup;
