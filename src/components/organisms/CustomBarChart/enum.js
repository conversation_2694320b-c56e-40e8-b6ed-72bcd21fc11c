import { isDarkMode } from "../../../utils/commonUtil";

export const CHART_CONFIG = {
  barSpacing: 5,
  borderRadius: 4,
  barPercentage: 0.5,
  categoryPercentage: 0.7,
};

export const DEFAULT_CHART_OPTIONS = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    mode: 'index',
    intersect: false,
  },
  layout: {
    padding: {
      left: 20,
    },
  },
  plugins: {
    legend: {
      display: false,
      position: 'top',
      labels: {
        usePointStyle: true,
        pointStyle: 'circle',
        color: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
        font: {
          size: 12,
          weight: '500',
        },
        generateLabels: (chart) => {
          const { datasets } = chart.data;
          return datasets.map((dataset, i) => ({
            text: dataset.label,
            fillStyle: dataset.backgroundColor,
            strokeStyle: dataset.borderColor,
            lineWidth: dataset.borderWidth,
            pointStyle: 'circle',
            datasetIndex: i,
          }));
        },
      },
    },
    tooltip: {
      enabled: false,
    },
  },
  scales: {
    x: {
      title: {
        display: false,
      },
      ticks: {
        color: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
        font: {
          size: 11,
        },
      },
      offset: true,
      grid: {
        display: false,
      },
      border: {
        display: true,
        color: isDarkMode() ? '#EEEEEE3D' : '#10101021',
      },
    },
    y: {
      type: 'linear',
      position: 'right',
      title: {
        display: false,
      },
      offset: false,
      grid: {
        display: true,
        color: isDarkMode() ? '#EEEEEE1F' : '#1010100F',
      },
      ticks: {
        color: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
        font: {
          size: 11,
        },
        padding: 10,
      },
      border: {
        display: true,
        color: isDarkMode() ? '#EEEEEE3D' : '#10101021',
      },
    },
  },
};
