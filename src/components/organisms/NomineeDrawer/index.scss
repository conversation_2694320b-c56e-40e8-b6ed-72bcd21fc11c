.drawerOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--overlay);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;

  .drawerContent {
    background-color: var(--plain);
    width: 100%;
    padding-bottom: 20px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    box-shadow: 0px -2px 10px var(--grey200);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    overflow: hidden;

    .bannerImage {
      width: 100%;
      height: auto;
      max-height: fit-content;
      display: block;
    }

    .contentWrapper {
      padding: 0 20px;
      width: 100%;
      margin-top: 20px;

      .title {
        font-size: 22px;
        font-weight: bold;
        color: var(--neutral);
        margin-bottom: 15px;
        max-width: 85%;
        margin-left: auto;
        margin-right: auto;
      }

      .description {
        font-size: 14px;
        color: var(--neutralModerate);
        margin-bottom: 25px;
        line-height: 1.5;

        .readMore {
          color: var(--linksSelections);
          cursor: pointer;
          text-decoration: none;
          margin-left: 5px;
          font-weight: 500;
        }
      }

      .addButton,
      .skipButton {
        width: 100%;
        font-size: 16px;
        cursor: pointer;
        border: none;
        transition: background-color 0.2s ease;
      }

      .addButton {
        padding: 12px 20px;
        border-radius: 25px;
        background-color: var(--primary);
        color: var(--silver900);
        margin-bottom: 15px;
        font-weight: bold;
      }

      .skipButton {
        padding: 8px 0;
        border-radius: 4px;
        background-color: transparent;
        color: var(--neutral);
        margin-bottom: 10px;
        font-weight: 500;
      }
    }
  }
} 