import nomineeBannerLight from "@assets/images/nominee_banner_light.svg";
import nomineeBannerDark from "@assets/images/nominee_banner_dark.svg";
import { isDarkMode } from '../../../utils/commonUtil';

import { NOMINEE_DRAWER_CONSTANTS } from "./NomineeDrawerData";
import styles from "./index.scss";

const NomineeDrawer = (props) => {
  const { onAddDetails, onSkipNomination, onReadMore, onClose } = props;

  return (
    <div className={styles.drawerOverlay} onClick={onClose}>
      <div
        className={styles.drawerContent}
        onClick={(e) => e.stopPropagation()}
      >
        <img
          src={isDarkMode() ? nomineeBannerDark : nomineeBannerLight}
          alt={NOMINEE_DRAWER_CONSTANTS.NOMINATION_DETAILS_ALT_TEXT}
          className={styles.bannerImage}
        />

        <div className={styles.contentWrapper}>
          <h2 className={styles.title}>{NOMINEE_DRAWER_CONSTANTS.TITLE}</h2>

          <p className={styles.description}>
            {NOMINEE_DRAWER_CONSTANTS.DESCRIPTION_PART_1}
            <span
              className={styles.readMore}
              onClick={onReadMore}
              role="button"
              tabIndex={0}
            >
              {NOMINEE_DRAWER_CONSTANTS.READ_MORE_TEXT}
            </span>
          </p>
          <button
            className={styles.addButton}
            onClick={onAddDetails}
            type="button"
          >
            {NOMINEE_DRAWER_CONSTANTS.ADD_DETAILS_BUTTON_TEXT}
          </button>
          <button
            className={styles.skipButton}
            onClick={onSkipNomination}
            type="button"
          >
            {NOMINEE_DRAWER_CONSTANTS.SKIP_NOMINATION_BUTTON_TEXT}
          </button>
        </div>
      </div>
    </div>
  );
};

export default NomineeDrawer;
