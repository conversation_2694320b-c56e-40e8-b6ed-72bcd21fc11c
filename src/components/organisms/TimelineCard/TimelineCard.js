/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect, memo } from 'react';

import Icon, { ICONS_NAME } from '../../molecules/Icon';

import { isDarkMode } from '../../../utils/commonUtil';
import GenericErrorLayout from '../../../HOC/GenericErrorLayout';
import { withErrorBoundary } from '../../../HOC/WidgetErrorBoundary';
import { revampDeeplinkHelper } from '../../../utils/navigationUtil';
import { useAnalyticsEventForFirstCards } from '../../../hooks/analyticsHooks';

import styles from './TimelineCard.scss';
import { PULSE_STATICS } from './enum';

const TimelineCard = (props) => {
  const { data } = props;
  if (!data) return null;

  const { items } = data;

  const onClickHandler = (step) => {
    if (step?.deeplinkMini) revampDeeplinkHelper(step.deeplinkMini);
  };

  const allItemsValid = items.every((step) => step.name && step.imageUrl);

  if (!allItemsValid) {
    return null;
  }
  const { sendAnalyticsEventFirstCard } = useAnalyticsEventForFirstCards();

  const sendPulseEvents = (action, event) => {
    sendAnalyticsEventFirstCard({
      event,
      action,
    });
  };

  useEffect(() => {
    // to do : add intersection observer for event
    sendPulseEvents(
      PULSE_STATICS.ACTION.Timeline_widget,
      PULSE_STATICS.OPEN_SCREEN_EVENT,
    );
  }, []);

  return (
    <GenericErrorLayout>
      <div className={styles.container}>
        <div className={styles.timeline}>
          {items.map((product, index) => (
            <div
              key={index}
              className={styles.step}
              onClick={() => onClickHandler(product)}
            >
              <div className={styles.iconWrapper}>
                <Icon
                  url={isDarkMode() ? product.imageUrlDark : product.imageUrl}
                  alt={product.name}
                  width="20px"
                />
              </div>
              <span
                className={`${
                  product.deeplinkMini && styles.productTitleLink
                } ${styles.productTitle}`}
              >
                {product.name}
                {product.deeplinkMini && (
                  <Icon
                    name={ICONS_NAME.LEFT_ARROW_BLUE}
                    className={styles.arrowIcon}
                    width="16px"
                  />
                )}
              </span>
            </div>
          ))}
        </div>
      </div>
    </GenericErrorLayout>
  );
};

export default memo(withErrorBoundary(TimelineCard));
