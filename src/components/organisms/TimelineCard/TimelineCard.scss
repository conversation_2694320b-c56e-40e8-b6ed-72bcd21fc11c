@import '~@paytm-h5-common/paytm_common_ui/styles/colors';
@import '~@paytm-money/utils-frontend/styles/pml-typography';
@import '~@paytm-money/utils-frontend/styles/pml-mixins';
@import '@src/commonStyles/variables.scss';
.container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 68px;
  padding: 8px 16px;
  margin-bottom: 8px;
}

.timeline {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  background-color: #D4DDFD;
  padding: 10px 0;
  border-radius: 12px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 100%;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 78%;
  left: 58%;
  width: 100%;
  height: 1px;
  z-index: 0;
  background-color: var(--background-primary-strong);
}

.iconWrapper {
  margin-bottom: 10px;
  position: relative;
  z-index: 0;
}

.productTitle {
  width: 80%;
  text-align: center;
  display: flex;
  justify-content: center;
  @include typography(body2B4, var(--text-neutral-medium));
}

.productTitleLink {
  @include typography(body2B2, var(--text-primary-strong));
}
