const TRANSACTION_TYPES = {
  BUY: 'B',
  SELL: 'S',
};

const ORDER_TYPE_ID = {
  REGULAR: 'REGULAR',
  STOP_LOSS: 'STOP_LOSS',
  COVER: 'COVER',
  BRACKET: 'BRACKET',
  GTT: 'GTT',
  SIP: 'SIP',
};

const ERROR_MESSAGES = {
  INSUFFICIENT_FUNDS: {
    TOAST:
      'Insufficient cash balance. Please reduce no. of shares or add balance',
  },
  INSUFFICIENT_SHARES: {
    TOAST: 'Insufficient shares in holdings. Kindly reduce no. of shares',
  },
  INSUFFICIENT_BUDGET: {
    TOAST: 'Insufficient balance. Please add your funds.',
    MESSAGE: 'Your funds is insufficient.',
  },
  INVALID_QTY: 'Minimum Share: 1',
  INVALID_LOT: 'Minimum Lot: 1',
  SOMETHING_WENT_WRONG: 'Something went wrong!',
  SOMETHING_WENT_WRONG_NEW:
    'We are hard at work to understand what went wrong. Please try again!',
  MIN_LIMIT: (value, isBuy) =>
    `Min. ${isBuy ? 'Buy' : 'Sell'} Amount: ₹${value}`,
  MAX_LIMIT: (value, isBuy) =>
    `Max. ${isBuy ? 'Buy' : 'Sell'} Amount: ₹${value}`,
  NO_EXCHANGE_SELECTED: {
    [TRANSACTION_TYPES.SELL]:
      "Please select the 'Sell at NSE' or 'Sell at BSE' to proceed",
    [TRANSACTION_TYPES.BUY]:
      "Please select 'Buy at NSE' or 'Buy at BSE' to proceed",
  },
  TICK_SIZE: (tickSize) => `Amount should be a multiple of ${tickSize}`,
  INSUFFICIENT_HOLDINGS: 'Insufficient stocks quantity in your holdings.',
  STOP_LOSS_LOWER_CIRCUIT: (value) =>
    `Trigger Price Should be more than ₹${value}`,
  STOP_LOSS_UPPER_CIRCUIT: (value) =>
    `Trigger Price Should be less than ₹${value}`,
  STOP_LOSS_BUY_TRIGGER_PRICE: 'Trigger Price should be lower than Buy Price',
  LIMIT_ORDER_PRICE_GREATER_THAN_LTP: 'Buy Price should be above Live Price',
  LIMIT_ORDER_PRICE_BELOW_THAN_LTP: 'Sell Price should be below Live Price',
  STOP_LOSS_GREATER_THAN_LTP: 'Trigger Price should be greater than live Price',
  STOP_LOSS_LOWER_THAN_LTP: 'Trigger Price should be lower than live Price',
  STOP_LOSS_LOWER_THAN_SELL_PRICE:
    'Trigger Price should be higher than Sell Price',
  STOP_LOSS_LOWER_THAN_BUY_PRICE:
    'Trigger Price should be lower than Buy Price',
  NON_ZERO: 'Value should be non zero',
  SL_NON_ZERO: 'SL Trigger value should be non zero',
  TRIGGER_NON_ZERO: 'Target value should be non zero',
  INVALID_LOT_SIZE: (lotSize) => `Should be Multiples of ${lotSize}`,
  DERIVATIVES_INVALID_ORDER:
    'Cover order and Bracket order are not available in FnO',
};

const STATICS = {
  QTY: 'Quantity',
  BUY: 'Buy',
  SELL: 'Sell',
  LIMIT: 'Limit',
  MARKET: 'Market',
  BUY_AT_MARKET_PRICE: 'Buy at Market Price',
  OR: 'OR',
  ORDER_VALIDITY: 'Order Validity: Day',
  MARKET_DEPTH: 'Market Depth',
  LOT: 'Lot',
  TRIGGER: 'Trigger',
  SL_TRIGGER: 'SL Trigger',
  TARGET: 'Target',
  TOTAL_QTY: (qty) => `(Total Qty: ${qty})`,
  BID: 'BID',
  OFFER: 'OFFER',
  QTY_TABLE: 'QTY',
  TOTAL: 'Total',
  BUY_ORDERS: 'Buy Orders',
  SELL_ORDERS: 'Sell Orders',
  ORDERS: 'ORDERS',
  SIP_CREATED: 'Sip Created',
  MTF_QTY_DESC: (mx, mp) => `Buy ${mx}x qty (Pay ${mp}% amount) under MTF.`,
};

const ADD_FUNDS = {
  HEADER: 'Insufficient funds',
  DESC: 'Add funds to proceed or retry with a different order',
  PRIMARY_CTA: 'Proceed to add funds',
  SECONDARY_CTA: 'Modify order',
};

const REQ_MARGIN = {
  REQ_MARGIN: 'Req Margin:',
  FUNDS_REQUIRED: 'Funds Required:',
  CHARGES: 'Charges',
  AVAILABLE: 'Avl:',
};

const CTA = {
  SWIPE_BUY: 'Swipe to Buy',
  SWIPE_SELL: 'Swipe to Sell',
  PROCEED: 'Swipe to Proceed',
};

export { ORDER_TYPE_ID, TRANSACTION_TYPES, ERROR_MESSAGES, STATICS ,ADD_FUNDS,REQ_MARGIN,CTA};
