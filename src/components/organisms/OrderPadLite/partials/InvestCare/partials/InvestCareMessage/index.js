import React from 'react';

import If from '@components/If';
import { openNewPage } from '@utils/bridgeUtils';
import { STATICS } from '../InvestCareDrawer/statics';

import styles from './index.scss';

function InvestCareMessage({
  displayName,
  message,
  ctaText,
  ctaLink,
  analyticalEvents,
  isFno,
}) {
  const onCtaClick = link => {
    openNewPage(link);
  };

  if (isFno) {
    return (
      <div className={styles.container}>
        <div className={styles.displayName}>
          <span>{displayName}</span>
          <span>{message}</span>
          <If test={ctaText && ctaLink}>
            <span
              className={styles.knowMore}
              onClick={() => {
                if (analyticalEvents && analyticalEvents?.investCareKnowMore)
                  analyticalEvents.investCareKnowMore();
                onCtaClick(ctaLink);
              }}
            >
              {ctaText}
            </span>
          </If>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.displayName}>
        <span>{displayName}</span>
        <span>{message}</span>
      </div>

      <If test={ctaText && ctaLink}>
        <div className={styles.descContainer}>
          <span className={styles.desc}>{STATICS.DESC}</span>
          <span
            className={styles.knowMore}
            onClick={() => {
              if (analyticalEvents) analyticalEvents.investCareKnowMore();
              onCtaClick(ctaLink);
            }}
          >
            {ctaText}
          </span>
        </div>
      </If>
    </div>
  );
}

export default InvestCareMessage;
