@import 'src/commonStyles/commoncss';

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.displayName {
  text-align: center;

   span:first-child {
    margin-right: 3px;

    @include typography(heading4B1, map-get($colors, Black5));
  }

  span:nth-child(2) {
    @include typography(text2, map-get($colors, Black5));
  }
}

.descContainer {
  margin-top: 10px;
}

.desc {
  @include typography(text2, map-get($colors, Black5));
}

.knowMore {
  white-space: nowrap;
  margin-left: 4px;

  @include typography(body2B1,  var( --background-primary-strong));
}


