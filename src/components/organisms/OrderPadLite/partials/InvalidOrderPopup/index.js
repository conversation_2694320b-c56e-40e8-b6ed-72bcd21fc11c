import { Button } from '@paytm-h5-common/paytm_common_ui';

import Drawer from '../../../../molecules/DrawerV2';

import styles from './index.scss';

function InvalidOrderPopup({ message, showPopup, onClose, header, cta }) {
  return (
    <Drawer isOpen={showPopup} onClose={onClose}>
      <div className={styles.container}>
        <p className={styles.header}>{header}</p>
        <p className={styles.message}>{message}</p>
        <Button label={cta} 
        emphasis="high"
        onClick={onClose} />
      </div>
    </Drawer>
  );
}

export default InvalidOrderPopup;
