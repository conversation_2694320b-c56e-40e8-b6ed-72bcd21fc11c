@import '@src/commonStyles/variables.scss';

.container {
  display: flex;
  justify-content: space-between;
  margin: 10px 16px;
  align-items: center;
}

.margin {
  display: flex;
  align-items: center;
  gap: 3px;
}

.key {
  padding: 4px 0;
  border-bottom: 1px dashed #10101021;
  margin: 0;
  @include typography(body2B3, rgba(16, 16, 16, 0.54));
}

.charges {
  color: var(--Text-grey500, rgba(16, 16, 16, 0.54));
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin-left: 4px;
}

.fundsRequired {
  color: var(--Text-grey500, rgba(16, 16, 16, 0.54));
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}

.reqMargin {
  margin: 0;
  @include typography(body2B3, rgba(16, 16, 16, 0.54));
}

.hideBorder {
  border-bottom: none;
}

.plus {
  margin: 0;
  @include typography(body2B3, rgba(16, 16, 16, 0.54));
}

.value {
  margin: 0;
  @include typography(body2B2, rgba(16, 16, 16, 0.54));
}

.marginContainer {
  display: flex;
  align-items: center;
}

.disabled {
  opacity: 0.5;
}

.negative {
  color: #fd5154;
}

.balance {
  align-items: center;
  margin-left: 14px;
  display: flex;
  gap: 3px;
}

.loader {
  align-self: center;
}

.addFundsContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: var(--BG-pendingAndAlertMedium, #fff8e1);

  .text {
    color: var(--Text-grey600, rgba(16, 16, 16, 0.7));
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;

    .difference {
      font-weight: 600;
    }
  }

  .addFundsText {
    color: var(--Text-link, #013da6);
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
  }
}
