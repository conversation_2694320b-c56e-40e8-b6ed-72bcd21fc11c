/* eslint-disable no-use-before-define */
import cx from 'classnames';
import { useEffect } from 'react';


import Icon,{ICONS_NAME} from '../../../../molecules/Icon';
import BrokerageInfo from '../../../BrokerageInfo';
import IndianNumberingSystem from '../../../../atoms/IndianNumberingSystem/IndianNumberingSystem';
import Shimmer from '../../../../atoms/Shimmer';
import { formatPrice } from '../../../../molecules/Prices';
import { useOrderFunds } from '../../hooks/useOrderFunds';
import FundsInfo from '../FundsInfo';
import { REQ_MARGIN } from '../../enum';
import { callPaymentFlow } from '../../../../../utils/navigationUtil';
import {
  BUFFER_MULTIPLIER_PLACE_ORDER,
  PRODUCT_TYPES,
  TRANSACTION_TYPES,
} from '../../../../../utils/Equities/enum';
import { useStockFeed } from '../../../../../utils/Equities/hooks';

import styles from './index.scss';

function ReqMargin({
  stockInfo,
  limitOrderData,
  activeProductType,
  quantity,
  txnType,
  brokerageInfoPayload,
  orderFundsPayload,
  analyticalEvents,
  clickedOrderCharges = () => {},
  chargesBottomSheet = () => {},
  chargesBottomSheetClosed = () => {},
  isIrOrderPadLite,
  events,
}) {
  const orderFundsHandler = useOrderFunds(orderFundsPayload);
  const firstTrade = orderFundsPayload?.firstTrade;

  const { isStale, marginData, fundsData } = orderFundsHandler;

  const { activeExchange: exchange, securityId, segment } = stockInfo;
  const { isLimitOrder, price } = limitOrderData;
  const { marginRequired, marginLoading } = marginData;

  const { data, isLoading, isError, firstTradeFundsData } = fundsData;

  const { ltp } = useStockFeed({
    exchange,
    segment,
    securityId,
  });

  const funds = firstTrade
    ? firstTradeFundsData?.data?.data?.funds_summary?.trade_balance
    : data?.data?.available_funds;

  const bufferedPrice =
    txnType === TRANSACTION_TYPES.SELL
      ? ltp
      : ltp * BUFFER_MULTIPLIER_PLACE_ORDER;

  const openPopup = cb => {
    if (isStale) refresh(cb);
    else cb();
  };

  useEffect(() => {
    if (events?.insufficientFunds) {
      if (getCost() > funds) {
        events.insufficientFunds();
      }
    }
  }, [quantity]);

  return (
    <>
      <div>
        {isIrOrderPadLite && getCost() > funds && (
          <div className={styles.addFundsContainer}>
            <div className={styles.text}>
              Add{' '}
              <span className={styles.difference}>
                ₹{formatPrice(getCost() - funds)}{' '}
              </span>
              to complete the order
            </div>
            <div
              className={styles.addFundsText}
              onClick={() => {
                events.addFundsClicked();
                callPaymentFlow({ amount: getCost() - funds });
              }}
            >
              Add Funds
            </div>
          </div>
        )}
      </div>
      <div className={styles.container}>
        <div
          className={cx(styles.marginContainer, {
            [styles.disabled]: isStale,
          })}
        >
          {txnType === TRANSACTION_TYPES.SELL &&
          activeProductType === PRODUCT_TYPES.DELIVERY ? (
            <div className={styles.margin}>
              <p className={styles.reqMargin}>{REQ_MARGIN.REQ_MARGIN}</p>
              <p className={styles.value}>
                <IndianNumberingSystem number={0} />
              </p>
            </div>
          ) : (
            <div className={styles.margin}>
              {isIrOrderPadLite ? (
                <p className={styles.fundsRequired}>
                  {REQ_MARGIN.FUNDS_REQUIRED}
                </p>
              ) : (
                <p className={styles.reqMargin}>{REQ_MARGIN.REQ_MARGIN}</p>
              )}
              {marginLoading ? (
                <Shimmer width="50px" height="18px" className={styles.loader} />
              ) : (
                <p className={styles.value}>
                  <IndianNumberingSystem number={getCost()} />
                </p>
              )}
            </div>
          )}

          <p className={styles.plus}>+</p>
          <BrokerageInfo
            render={onClick => (
              <p
                className={cx(styles.key, {
                  [styles.charges]: isIrOrderPadLite,
                })}
                onClick={() => {
                  if (brokerageInfoPayload.qty) {
                    openPopup(onClick);
                  }
                  if (analyticalEvents) {
                    analyticalEvents.tapsOrderCharges({
                      productType: activeProductType,
                    });
                  }
                  clickedOrderCharges();
                }}
              >
                {REQ_MARGIN.CHARGES}
              </p>
            )}
            price={isLimitOrder ? price : bufferedPrice}
            chargesBottomSheet={chargesBottomSheet}
            chargesBottomSheetClosed={chargesBottomSheetClosed}
            {...brokerageInfoPayload}
          />

          <div className={styles.balance}>
            <FundsInfo
              fundsSummary={orderFundsHandler.fundsData}
              render={({ onOpen, showDrawer }) => (
                <p
                  onClick={
                    showDrawer && !firstTrade
                      ? () => {
                          if (analyticalEvents) {
                            analyticalEvents.tapsAvailableFunds({
                              cash:
                                orderFundsHandler.fundsData?.data?.data
                                  ?.trade_balance,
                            });
                          }
                          openPopup(onOpen);
                        }
                      : () => {}
                  }
                  className={cx(styles.key, {
                    [styles.hideBorder]: !showDrawer || firstTrade,
                  })}
                >
                  {REQ_MARGIN.AVAILABLE}
                </p>
              )}
            />

            <p className={styles.value}>
              {(firstTrade ? (
                firstTradeFundsData.isLoading
              ) : (
                isLoading
              )) ? (
                <Shimmer width="50px" height="18px" className={styles.loader} />
              ) : isError ? (
                '-'
              ) : (
                <p
                  className={cx(styles.value, {
                    [styles.negative]: funds < 0 || getCost() > funds,
                  })}
                >
                  <IndianNumberingSystem number={funds} />
                </p>
              )}
            </p>
          </div>
        </div>
        <Icon name={ICONS_NAME.RELOAD} size={3} onClick={refresh} />
      </div>
    </>
  );

  function refresh(cb = () => {}) {
    if (analyticalEvents) {
      analyticalEvents.refreshMargin();
    }
    orderFundsHandler.retry(cb);
  }

  function getCost() {
    if (isLimitOrder && activeProductType === PRODUCT_TYPES.DELIVERY) {
      return price * quantity;
    } else if (activeProductType === PRODUCT_TYPES.INTRADAY) {
      return marginRequired;
    } else if (activeProductType === PRODUCT_TYPES.MARGIN) {
      return marginRequired;
    } else if (activeProductType === PRODUCT_TYPES.MTF) {
      return marginRequired;
    }
    return bufferedPrice * quantity;
  }
}

export default ReqMargin;
