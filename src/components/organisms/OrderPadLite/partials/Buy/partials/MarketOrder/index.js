import { useStockFeed } from '../../../../../../../utils/Equities/hooks';
import infoIconRevamp from '../../../../../../../assets/infoIconRevamp.svg';
import { formatPrice } from '../../../../../../molecules/Prices';

// import ErrorMessage from '../../../../enum';
import ErrorMessage from '../ErrorMessage';
import { STATICS } from '../../../../enum';
import If from '../../../../../../atoms/If';

import styles from './index.scss';

function MarketOrder({ onOpen, stocksInfo, qty, maxTradeValue }) {
  const { activeExchange, securityId, segment } = stocksInfo;
  const { ltp } = useStockFeed({
    exchange: activeExchange,
    securityId: parseInt(securityId, 10),
    segment,
  });

  return (
    <div className={styles.container}>
      <div className={styles.irOrderTypeContainer}>
        <p className={styles.orderType}>{STATICS.BUY_AT_MARKET_PRICE}</p>
        <img
          src={infoIconRevamp}
          className={styles.infoIcon}
          alt="info-icon"
          onClick={onOpen}
        />
      </div>
      <If test={ltp && qty && ltp * qty >= maxTradeValue}>
        <ErrorMessage
          errorMsg={`Max Trade value allowed: ${formatPrice(maxTradeValue, 0)}`}
        />
      </If>
    </div>
  );
}

export default MarketOrder;
