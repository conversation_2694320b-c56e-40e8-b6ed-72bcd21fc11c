import cx from 'classnames';
import If from '../../../../../../atoms/If';
import InputBox from '../../../../../../molecules/InputBox/InputBox';
import { useInputFocus } from '../../../../hooks/useInputFocus';
import ErrorMessage from '../ErrorMessage';

import { STATICS } from '../../../../enum';
import Label from '../../../Label';
import styles from './index.scss';
import Derivatives from './partials/Derivatives';

function Qty({
  qtyHandler,
  analyticalEvents,
  onQtyChange = () => {},
  isIrOrderPadLite,
}) {
  const {
    quantity,
    onQuantityChange,
    isDerivative,
    derivativeQtyHandler,
    disabled,
    onIncrement,
    onDecrement,
    disableIncrement,
    disableDecrement,
    errMessage,
  } = qtyHandler;

  const { inputRef, onFocus } = useInputFocus();
  return (
    <div
      className={cx(styles.container, {
        [styles.noBorder]: isIrOrderPadLite,
      })}
    >
      <If test={isDerivative}>
        <Derivatives
          derivativeQtyHandler={derivativeQtyHandler}
          analyticalEvents={analyticalEvents}
        />
      </If>
      <If test={!isDerivative}>
        <div style={{ width: '100%' }}>
          <div
            className={cx(styles.wrapper, {
              [styles.wrapperIrOrderPad]: isIrOrderPadLite,
            })}
          >
            <div>
              <Label label={STATICS.QTY} onClick={onFocus} />
              <p className={styles.mtfDesc}>{STATICS.MTF_QTY_DESC}</p>
            </div>
            <div className={styles.controls}>
              <span
                className={cx(styles.decrement, {
                  [styles.disable]: disableDecrement,
                })}
                onClick={
                  disableDecrement
                    ? () => {}
                    : () => {
                        onQtyChange();
                        onDecrement();
                        if (analyticalEvents) {
                          analyticalEvents.incrementDecrementQty({
                            type: 'remove',
                          });
                        }
                      }
                }
              />
              <InputBox
                reference={inputRef}
                value={quantity}
                className={styles.input}
                customStyles={styles.inputBox}
                onChangeHandler={e => {
                  if (analyticalEvents) analyticalEvents.typeQty();
                  onQuantityChange(e);
                  onQtyChange();
                }}
                inputMode="decimal"
                disabled={disabled}
                customInputBoxStyle={{
                  width: '110px',
                  border: 'none',
                  textAlign: 'center',
                  borderBottom: 'none',
                }}
              />
              <span
                className={cx(styles.increment, {
                  [styles.disable]: disableIncrement,
                })}
                onClick={
                  disableIncrement
                    ? () => {}
                    : () => {
                        onIncrement();
                        if (analyticalEvents) {
                          analyticalEvents.incrementDecrementQty({
                            type: 'add',
                          });
                        }
                        onQtyChange();
                      }
                }
              />
            </div>
          </div>
          <ErrorMessage
            errorMsg={errMessage}
            isIrOrderPadLite={isIrOrderPadLite}
          />
        </div>
      </If>
    </div>
  );
}

export default Qty;
