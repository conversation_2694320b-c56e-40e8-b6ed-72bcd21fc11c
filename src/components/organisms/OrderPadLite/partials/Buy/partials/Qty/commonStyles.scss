
.controls {
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 32px;
  width: 32px;
  border-radius: 50%;
  border: 1px solid map-get($colors, LightGrey)
}

.decrement {
  @extend .circle;

  &::before {
    position: relative;
    content: '';
    background-color: map-get($colors, Black5);
    height: 1px;
    width: 10px;
  }
}

.increment {
  @extend .circle;

  &::before {
    position: absolute;
    content: '';
    background-color: map-get($colors, Black5);
    height: 1px;
    width: 10px;
  }

  &::after {
    position: absolute;
    content: '';
    background-color: map-get($colors, Black5);
    height: 10px;
    width: 1px;
  }
}
