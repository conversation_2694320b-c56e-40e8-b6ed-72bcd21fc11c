.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  margin: 0 16px;
  border-bottom: 1px solid rgba(16, 16, 16, 0.13);
}

.noBorder {
  border-bottom: none;
}

.mtfDesc {
  opacity: 0.54;
  margin: 4px 0 0;
  font-size: 10px;
  line-height: 1.6;
  color: #101010;
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.wrapperIrOrderPad {
  flex-direction: column;
}

.input {
  text-align: end;
}

.inputBox {
  display: inline-flex;
}

.disable {
  opacity: 0.5;
}


.controls {
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 32px;
  width: 32px;
  border-radius: 50%;
  border: 1px solid rgba(16, 16, 16, 0.13)
}

.decrement {
  @extend .circle;

  &::before {
    position: relative;
    content: '';
    background-color: #101010;
    height: 1px;
    width: 10px;
  }
}

.increment {
  @extend .circle;

  &::before {
    position: absolute;
    content: '';
    background-color: #101010;
    height: 1px;
    width: 10px;
  }

  &::after {
    position: absolute;
    content: '';
    background-color: #101010;
    height: 10px;
    width: 1px;
  }
}
