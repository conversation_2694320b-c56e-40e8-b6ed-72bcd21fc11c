/* eslint-disable no-use-before-define */
import InputBox from '../../../../../../../../molecules/InputBox/InputBox';
import ErrorMessage from '../../../ErrorMessage';

import { STATICS } from '../../../../../../enum';

import styles from './index.scss';

function Derivatives({ derivativeQtyHandler, analyticalEvents }) {
  const {
    onLotQtyChange,
    onLotDecrement,
    onLotIncrement,
    lotQty,
    quantity,
    disableIncrement,
    disableDecrement,
    disabled,
    errMessage,
  } = derivativeQtyHandler;

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div>
          <span className={styles.label}>{STATICS.LOT}</span>
          <span className={styles.qty}>{STATICS.TOTAL_QTY(quantity)}</span>
        </div>
        <div className={styles.controls}>
          <span
            className={styles.decrement}
            onClick={() => {
              handleDecrement();
              if (analyticalEvents) {
                analyticalEvents.incrementDecrementQty({
                  type: 'remove',
                });
              }
            }}
          />
          <InputBox
            value={lotQty}
            onChangeHandler={e => {
              if (analyticalEvents) analyticalEvents.typeQty();
              onLotQtyChange(e);
            }}
            customInputBoxStyle={{
              width: '96px',
              border: 'none',
              textAlign: 'center',
            }}
            disabled={disabled}
            inputMode="decimal"
          />
          <span
            className={styles.increment}
            onClick={() => {
              handleIncrement();
              if (analyticalEvents) {
                analyticalEvents.incrementDecrementQty({
                  type: 'add',
                });
              }
            }}
          />
        </div>
      </div>
      <ErrorMessage errorMsg={errMessage} />
    </div>
  );

  function handleDecrement() {
    if (disableDecrement) return;
    if (disabled) return;
    onLotDecrement();
  }

  function handleIncrement() {
    if (disableIncrement) return;
    if (disabled) return;
    onLotIncrement();
  }
}

export default Derivatives;
