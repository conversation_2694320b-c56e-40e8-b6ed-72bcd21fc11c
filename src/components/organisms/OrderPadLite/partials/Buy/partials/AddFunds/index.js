/* eslint-disable no-use-before-define */
import React from 'react';
import { useNavigate } from 'react-router-dom';

import {Button} from "@paytm-h5-common/paytm_common_ui"
// import { navigateTo } from '@services/coreUtil';
// import history from '@src/history';

// import { MINI_APP_ROUTES } from '@config/urlConfig';
import Icon,{ICONS_NAME} from "../../../../../../molecules/Icon";
import { ADD_FUNDS } from '../../../../enum';

import { useBackPress } from '../../../../../../../hooks/useNativeBackPress';

import Drawer, { ALIGNMENTS } from '../../../../../../molecules/DrawerV2';
// import Icon, { ICONS_NAME } from '@components/Icon';

import styles from './index.scss';

const MINI_APP_ROUTES={}

function AddFunds({
  isOpen,
  onClose,
  errorMessage,
  funds,
  quantity,
  events,
  redirectRoute = MINI_APP_ROUTES.ADD_MONEY,
  source = MINI_APP_ROUTES.ORDER_PAD,
}) {
  const navigate = useNavigate();
  const { popStack } = useBackPress();
  return (
    <Drawer
      align={ALIGNMENTS.LEFT}
      isOpen={isOpen}
      onClose={onClose}
      title={
        <p className={styles.header}>
          {ADD_FUNDS.HEADER}
          <Icon
            name={ICONS_NAME.INFO_ICON_RED}
            size={3.2}
            className={styles.icon}
          />
        </p>
      }
    >
      <div className={styles.container}>
        <p className={styles.errorMessage}>{errorMessage}</p>
        <div className={styles.btnContainer}>
          <Button
            emphasis="high"
            label={ADD_FUNDS.PRIMARY_CTA}
            onClick={redirectToAddMoney}
          />
          <Button
          emphasis="medium"
          label={ADD_FUNDS.SECONDARY_CTA}
          onClick={() => {
              if (events) events.modifyFunds();
              popStack();
            }}
          />
        </div>
      </div>
    </Drawer>
  );

  function updateQty(str, updatedQty) {
    const temp = str.split('&');
    let i;
    for (i = 0; i < temp.length; i += 1) {
      if (temp[i].includes('quantity')) break;
    }
    if (i === temp.length) return str;
    temp[i] = `quantity=${updatedQty}`;
    return temp.join('&');
  }

  function redirectToAddMoney() {
    if (events) events.addFunds();
    // TODO : handle navigation
    // navigateTo(
    //   history,
    //   redirectRoute,
    //   {
    //     cashBalance: funds?.toString(),
    //     source,
    //     query: updateQty(history.location.search, quantity),
    //   },
    //   'push',
    // );
  }
}

export default AddFunds;
