.orderTypeContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  margin: 0 16px;
  border-bottom: 1px solid rgba(16, 16, 16, 0.13);
}

.orderType {
  margin: 0;
  font-size: 16px;
    font-weight: 600;
    color:  #101010;
}

.orderValidity {
  margin: 20px 16px;
  opacity: 0.7;
  font-size: 12px;
  font-weight: 400;
  color: #101010;
}

.container {
  height: calc(100vh - 72px);
  display: flex;
  flex-direction: column;
  position: relative;
}

.irContainer {
  height: max-content;
}

.content {
  overflow-y: auto;
}

.border {
  border-top: 1px solid rgba(16, 16, 16, 0.13);
}

.footer {
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.irFooter {
  flex: none;
  margin-top: 24px;
}

.drawerConatiner {
  display: flex;
  flex-direction: column;
  padding: 0 16px;

  .titleContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .title {
      color: var(--Text-grey900, #101010);
      font-size: 18px;
      font-weight: 500;
      line-height: 24px;
      font-family: Inter;
    }
  }

  .points {
    color: var(--Text-grey600, rgba(16, 16, 16, 0.7));
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    margin: 0 0 2px 8px;
  }

  .dismissButton {
    height: 52px;
    margin: 16px 0;
    border-radius: 8px;
    background: var(--BG-primary, #013da6);
  }

  .buttonTextClassName {
    color: var(--Text-silverForever, #fff);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: -0.32px;
  }
}
