import { useEffect } from 'react';

import {
  PRODUCT_TYPES,
  TRANSACTION_TYPES,
} from '../../../../../utils/Equities/enum';
import { ORDER_TYPE_ID } from '../../enum';
import { APPEARANCE_TYPES } from '../../../../../utils/constants';
import { useAmo } from '../../hooks/useAmo';
import { useQtyHandler } from '../../hooks/useQtyHandler';
import { useFundsHandler } from '../../hooks/useFundsHandler';

import { usePlaceOrder } from '../hooks/usePlaceOrder';
import { useOrderRestrictionJson } from '../../../../../query/generalQuery';

function useBuy({
  axiosSource,
  stockInfo,
  locationHeader,
  events,
  isIrOrderPadLite,
  onOrderSuccess,
}) {
  const { data } = useOrderRestrictionJson();
  const orderConfigEquity = data?.data?.Equity?.[stockInfo?.activeExchange];
  const maxOrderQty = orderConfigEquity?.MaxOrderQty;
  const maxTradeValue = orderConfigEquity?.MaxTradeValue;
  const quantityHandler = useQtyHandler({
    positionModify: false,
    netQty: false,
    modify: false,
    initialQuantity: 0,
    isDerivative: false,
    lotSize: stockInfo?.lotSize,
    qty: 1,
    boCoModify: false,
    modifyCover: false,
    boModify: false,
    maxQty: maxOrderQty,
  });

  const fundsHandler = useFundsHandler(false);

  const { amo } = useAmo({
    segment: stockInfo?.segment,
    activeExchange: stockInfo?.activeExchange,
  });

  const { onPlaceOrder, isLoading, invalidOrderPopup } = usePlaceOrder({
    amo,
    quantity: quantityHandler.quantity,
    stockInfo,
    locationHeader,
    fundsHandler,
    events,
    isIrOrderPadLite,
    onOrderSuccess,
  });

  useEffect(() => {
    if (!quantityHandler.isValid) {
      events.invalidLotSize();
    }
  }, [quantityHandler.isValid]);

  return {
    quantityHandler,
    brokerageInfoPayload: {
      transaction_type: TRANSACTION_TYPES.BUY,
      product_type: PRODUCT_TYPES.DELIVERY,
      exchange: stockInfo?.activeExchange,
      qty: quantityHandler.quantity,
      instrument_type: stockInfo.instrumentType,
      desc: {
        isSikkimUser: locationHeader,
        isSl: false,
        isLimitOrder: false,
      },
    },
    orderFundsPayload: {
      txnType: TRANSACTION_TYPES.BUY,
      productType: PRODUCT_TYPES.DELIVERY,
      instrumentType: stockInfo.instrumentType,
      exchange: stockInfo.activeExchange,
      quantity: quantityHandler.quantity,
      isLimitOrder: false,
      price: 0,
      segment: stockInfo.segment,
      securityId: stockInfo.securityId,
      axiosSource,
      activeOrderType: ORDER_TYPE_ID.REGULAR,
      firstTrade: !isIrOrderPadLite,
    },
    onPlaceOrder,
    isLoading,
    invalidOrderPopup,
    fundsHandler,
    errorMessage: quantityHandler.isValid
      ? null
      : {
          message: quantityHandler.errMessage,
          type: APPEARANCE_TYPES.FAIL,
        },
    maxTradeValue,
  };
}

export { useBuy };
