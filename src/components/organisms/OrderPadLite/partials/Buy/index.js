import cx from 'classnames';
import { lazy, Suspense, useEffect } from 'react';

// TODO : handle source for AddFunds
// import { MINI_APP_ROUTES } from '@config/urlConfig';
import { Button } from '@paytm-h5-common/paytm_common_ui';

import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import InvalidOrderPopup from '../InvalidOrderPopup';
import Label from '../Label';
import ReqMargin from '../ReqMargin';
import SwipeAbleButton from '../SwipeableButton';
import { PRODUCT_TYPES } from '../../../../../utils/Equities/enum';

import Qty from './partials/Qty';
import MarketOrder from './partials/MarketOrder';
import { STATICS } from '../../enum';
import Drawer, { useDrawer } from '../../../../molecules/DrawerV2';
import If from '../../../../atoms/If';
import SleekCard from '../SleekCard';

import styles from './index.scss';

import { useBuy } from './useBuy';

const AddFunds = lazy(
  () => import(/* OrderPadAddFunds */ './partials/AddFunds'),
);

function Buy({
  stockInfo,
  locationHeader,
  txnType,
  axiosSource,
  investCareLoading,
  events,
  isIrOrderPadLite,
  onOrderSuccess,
}) {
  const {
    quantityHandler,
    brokerageInfoPayload,
    orderFundsPayload,
    onPlaceOrder,
    isLoading,
    invalidOrderPopup,
    fundsHandler,
    errorMessage,
    maxTradeValue,
  } = useBuy({
    stockInfo,
    locationHeader,
    axiosSource,
    events,
    isIrOrderPadLite,
    onOrderSuccess,
  });

  const { isOpen, onOpen, onClose } = useDrawer();

  function handlePlaceOrder() {
    if (!errorMessage) {
      onPlaceOrder(!isIrOrderPadLite);
      events.onPlaceOrder();
    }
  }

  const {
    showAddFunds,
    errorMessage: fundsErrorMessage,
    closeFundsPopup,
  } = fundsHandler;

  useEffect(() => {
    if (showAddFunds) {
      events.insufficientFunds();
      events.insufficientFundsSheet();
    }
  }, [showAddFunds]);

  return (
    <div
      className={cx(styles.container, {
        [styles.irContainer]: isIrOrderPadLite,
      })}
    >
      <div className={styles.content}>
        {!isIrOrderPadLite && <SleekCard />}
        <Qty
          qtyHandler={quantityHandler}
          onQtyChange={events.userInputQty}
          isIrOrderPadLite={isIrOrderPadLite}
        />
        {isIrOrderPadLite ? (
          <MarketOrder
            onOpen={onOpen}
            qty={quantityHandler.quantity}
            stocksInfo={stockInfo}
            maxTradeValue={maxTradeValue}
          />
        ) : (
          <div>
            <div className={styles.orderTypeContainer}>
              <Label label={STATICS.BUY} />
              <p className={styles.orderType}>{STATICS.MARKET}</p>
            </div>
          </div>
        )}
        {!isIrOrderPadLite && (
          <p
            className={styles.orderValidity}
          >{`${STATICS.ORDER_VALIDITY}: 7 Days`}</p>
        )}
      </div>
      <div
        className={cx(styles.footer, {
          [styles.irFooter]: isIrOrderPadLite,
        })}
      >
        <div className={styles.border}>
          <ReqMargin
            stockInfo={stockInfo}
            limitOrderData={{
              isLimitOrder: false,
            }}
            activeProductType={PRODUCT_TYPES.DELIVERY}
            quantity={quantityHandler.quantity}
            txnType={txnType}
            orderFundsPayload={orderFundsPayload}
            brokerageInfoPayload={brokerageInfoPayload}
            clickedOrderCharges={events.clickedOrderCharges}
            chargesBottomSheet={events.chargesBottomSheet}
            chargesBottomSheetClosed={events.chargesBottomSheetClosed}
            isIrOrderPadLite={isIrOrderPadLite}
            events={events}
          />
        </div>
        <SwipeAbleButton
          txnType={txnType}
          isLocked={investCareLoading}
          onComplete={handlePlaceOrder}
          isLoading={isLoading}
        />
      </div>
      <If test={showAddFunds}>
        <Suspense fallback={<></>}>
          <AddFunds
            isOpen={showAddFunds}
            onClose={closeFundsPopup}
            errorMessage={fundsErrorMessage}
            events={events}
            redirectRoute="/kyc/v3/first-fund-addition"
            source="/order-pad-lite"
          />
        </Suspense>
      </If>
      <InvalidOrderPopup {...invalidOrderPopup} />
      <Drawer isOpen={isOpen} onClose={onClose} showCross={false}>
        <div className={styles.drawerConatiner}>
          <div className={styles.titleContainer}>
            <div className={styles.title}>Market Order</div>
            <Icon
              name={ICONS_NAME.CIRCULAR_CLOSE}
              width="24px"
              onClick={onClose}
            />
          </div>
          <div className={styles.points}>
            &#x2022; Post market hours, order will be placed as an AMO order
          </div>
          <div className={styles.points}>
            &#x2022; This will be a delivery order
          </div>
          <Button label="Dismiss" emphasis="high" onClick={onClose} />
        </div>
      </Drawer>
    </div>
  );
}

export default Buy;
