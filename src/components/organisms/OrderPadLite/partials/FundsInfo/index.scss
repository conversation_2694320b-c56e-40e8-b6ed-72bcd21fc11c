
.container {
  padding: 20px;
}

.header > p {
  font-size: 24px;
    font-weight: 700;
  color: #101010;
}

.column {
  display: flex;
  justify-content: space-between;
}

.label {
  font-size: 14px;
    font-weight: 500;
    line-height: 1.43;
  color: #101010;

  margin-bottom: 5px;
}

.value {
  font-size: 14px;
    font-weight: 500;
    line-height: 1.43;
  color: #101010;

}

.footer {
  margin-top: 15px;
}

.message {
  font-size: 12px;
    font-weight: 400;
  color: #101010;

}

.cta {
  display: inline-block;
  margin-left: 3px;
  font-size: 12px;
  font-weight: 400;
  color: #00b8f5;
}
