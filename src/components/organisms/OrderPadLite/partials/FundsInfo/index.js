import { BASE_URL } from '../../../../../config/envConfig';
import { openNewPage } from '../../../../../utils/bridgeUtils';
import { useEquityInfoCard } from '../../../../../query/stockQuery';
import Drawer, { useDrawer, ALIGNMENTS } from '../../../../molecules/DrawerV2';
import { Change } from '../../../../molecules/Prices';

import { STATICS } from './Statics';

import styles from './index.scss';

function FundsInfo({ fundsSummary, render }) {
  const { isOpen, onOpen, onClose } = useDrawer();
  const { data } = useEquityInfoCard();

  const available_cash = data?.data?.available_cash;

  const tradeBalance = fundsSummary?.data?.data?.trade_balance; // cash + collateral
  const availableCash = fundsSummary?.data?.data?.available_cash; // total cash

  const showDrawer = !(
    fundsSummary?.data?.data?.collaterals ===
    fundsSummary?.data?.data?.available_cash
  ); // if collaterals available only then render drawer

  return (
    <>
      {render({
        onOpen,
        showDrawer,
      })}
      <Drawer
        title={STATICS.HEADER}
        isOpen={isOpen && showDrawer}
        onClose={onClose}
        align={ALIGNMENTS.LEFT}
        headerStyles={styles.header}
      >
        <div className={styles.container}>
          <div className={styles.column}>
            <p className={styles.label}>{STATICS.TOTAL_CASH}</p>
            <Change
              value={availableCash}
              withRupee
              neutral
              className={styles.value}
              withSign={availableCash < 0}
            />
          </div>

          <div className={styles.column}>
            <p className={styles.label}>{STATICS.CASH_COLL}</p>
            <Change
              value={tradeBalance}
              withRupee
              neutral
              className={styles.value}
              withSign={tradeBalance < 0}
            />
          </div>

          <div className={styles.footer}>
            <span className={styles.message}>
              {available_cash?.message || STATICS.MESSAGE}
            </span>
            <span
              className={styles.cta}
              onClick={() =>
                openNewPage(
                  available_cash?.cta_link || BASE_URL?.AVAILABLE_FUNDS,
                )
              }
            >
              {available_cash?.cta_text || STATICS.KNOW_MORE}
            </span>
          </div>
        </div>
      </Drawer>
    </>
  );
}

export default FundsInfo;
