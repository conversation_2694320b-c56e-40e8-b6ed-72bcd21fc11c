/* eslint-disable no-use-before-define */
import { useState } from 'react';

import { getUserId } from '../../../../../utils/coreUtil';
import { placeOrder } from '../../../../../actions/orderPadActions';
import {
  PRODUCT_TYPES,
  TRANSACTION_TYPES,
} from '../../../../../utils/Equities/enum';
import {
  // constructPayload,
  handleFundsLite,
  INVALID_POPUP,
  LOCAL_STORAGE_KEYS,
  orderStatusChanges,
  GENERIC_ERROR_MESSAGE,
} from '../../../../../utils/orderPadUtils';

function usePlaceOrder({
  amo,
  quantity,
  stockInfo,
  locationHeader,
  fundsHandler,
  events,
  isIrOrderPadLite,
  onOrderSuccess,
}) {
  const exchange = stockInfo?.activeExchange;
  const securityId = stockInfo?.securityId;
  const segment = stockInfo?.segment;
  const [isLoading, setIsLoading] = useState(false);

  const [invalidOrder, setInvalidOrder] = useState({
    showPopup: false,
    message: '',
    header: '',
    cta: '',
  });

  async function onPlaceOrder() {
    const payload = {
      transactionType: TRANSACTION_TYPES.BUY,
      exchange,
      securityId,
      quantity,
      segment,
      amo,
      isLimitOrder: false,
      price: 0,
      product: PRODUCT_TYPES.DELIVERY,
      isSl: false,
      triggerPrice: '',
      locationHeader,
      orderHold: !isIrOrderPadLite,
      client_id: getUserId(),
    };

    try {
      setIsLoading(true);
      const { data } = await placeOrder(payload);
      afterApiCall({ data });
    } catch (error) {
      afterApiCall({ data: error });
    }
  }

  function afterApiCall({ data }) {
    const { insufficientFunds, message } = handleFundsLite(data);
    if (insufficientFunds) {
      const { setShowAddFunds, setErrorMessage } = fundsHandler;
      setErrorMessage(message);
      setShowAddFunds(true);
      setIsLoading(false);
      return;
    }
    const { statusChanged, message: statusErrMsg } = orderStatusChanges(data);

    if (statusChanged) {
      localStorage.setItem(
        LOCAL_STORAGE_KEYS.ORDER_CONFIRMATION_DATA,
        JSON.stringify({
          isError: true,
          message: statusErrMsg,
        }),
      );
    }

    if (data?.response?.status && data?.response?.status !== 200) {
      events.genericError();
      setInvalidOrder({
        message: data?.response?.data?.message || GENERIC_ERROR_MESSAGE,
        showPopup: true,
        header: INVALID_POPUP.ERROR_HEADER,
        cta: INVALID_POPUP.CTA,
      });
      setIsLoading(false);
      return;
    }
    if (isIrOrderPadLite && onOrderSuccess) {
      onOrderSuccess(data);
    }
    // TODO: Navigate to the confirmation page
    // navigateTo(
    //   history,
    //   `${MINI_APP_ROUTES.FIRST_INVESTMENT_ORDER_CONFIRMATION}`,
    // );
  }

  function closeInvalidOrderPopup() {
    setInvalidOrder({
      showPopup: false,
      message: '',
      header: '',
      cta: '',
    });
  }

  return {
    onPlaceOrder,
    isLoading,
    invalidOrderPopup: {
      onClose: closeInvalidOrderPopup,
      ...invalidOrder,
    },
  };
}

export { usePlaceOrder };
