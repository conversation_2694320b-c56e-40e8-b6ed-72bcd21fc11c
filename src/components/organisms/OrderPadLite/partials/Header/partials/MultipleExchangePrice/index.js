import Icon,{ICONS_NAME}  from '../../../../../../molecules/Icon';
import If from '../../../../../../atoms/If';

import Price from '../Price';

import styles from './index.scss';

function MultipleExchangePrice({
  exchange,
  segment,
  securityId,
  siblingData,
  activeExchange,
  onExchangeSelect,
  selectedExchangeEvent,
}) {
  const { siblingDataLoader, siblingData: siblingInfo } = siblingData;
  const siblingExchange = siblingInfo?.exchange;
  const siblingSecId = siblingInfo?.security_id;

  return (
    <div className={styles.container}>
      <div
        className={styles.priceContainer}
        onClick={() => {
          onExchangeSelect(exchange);
          selectedExchangeEvent(exchange);
        }}
      >
         <Icon
          name={
            activeExchange === exchange
              ? ICONS_NAME.RADIO_CHECKED
              : ICONS_NAME.RADIO_UNCHECKED
          }
          className={styles.icon}
          size={3.2}
        />
        <Price securityId={securityId} segment={segment} exchange={exchange} />
      </div>

      <If test={!siblingDataLoader}>
        <div
          className={styles.priceContainer}
          onClick={() => {
            onExchangeSelect(siblingExchange);
            selectedExchangeEvent(siblingExchange);
          }}
        >
          <Icon
            name={
              activeExchange === siblingExchange
                ? ICONS_NAME.RADIO_CHECKED
                : ICONS_NAME.RADIO_UNCHECKED
            }
            size={3.2}
            className={styles.icon}
          />
          <Price
            securityId={siblingSecId}
            segment={segment}
            exchange={siblingExchange}
          />
        </div>
      </If>
    </div>
  );
}

export default MultipleExchangePrice;
