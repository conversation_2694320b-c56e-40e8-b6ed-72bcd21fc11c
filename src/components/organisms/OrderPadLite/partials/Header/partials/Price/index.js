import { useStockFeed } from '../../../../../../../utils/Equities/hooks';
import { EXCHANGE_TYPE } from '../../../../../../../utils/constants';
import { Change } from '../../../../../../molecules/Prices';

import styles from './index.scss';

function Price({ exchange, securityId, segment }) {
  const { ltp } = useStockFeed({
    exchange,
    segment,
    securityId: parseInt(securityId, 10),
  });
  return (
    <div className={styles.container}>
      <p className={styles.exchange}>{EXCHANGE_TYPE[exchange]}: </p>
      <Change value={ltp} neutral withRupee className={styles.price} />
    </div>
  );
}

export default Price;
