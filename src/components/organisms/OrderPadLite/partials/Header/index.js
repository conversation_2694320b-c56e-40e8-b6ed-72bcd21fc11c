import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import cx from 'classnames';
import Icon ,{ICONS_NAME} from '../../../../molecules/Icon';
import If from '../../../../atoms/If';

import { useBackPress } from '../../../../../hooks/useNativeBackPress';

import Price from './partials/Price';
import MultipleExchangePrice from './partials/MultipleExchangePrice';

import styles from './index.scss';

function Header({
  name,
  exchange,
  symbol,
  segment,
  securityId,
  onExchangeSelect,
  showNudgeInfo,
  openNudgeInfo,
  siblingData,
  activeExchange,
  selectedExchangeEvent,
  backPressEvent,
  isIrOrderPadLite,
}) {
  const navigate = useNavigate();
  const { pushStack, popStack, clearStack } = useBackPress();

  useEffect(() => {
    pushStack(() => {
      navigate(-1);
      backPressEvent();
    });
    return () => {
      clearStack();
    };
  }, []);

  return (
    <div
      className={cx(styles.headerContainer, {
        [styles.noPaddingTop]: isIrOrderPadLite,
      })}
    >
      {!isIrOrderPadLite && (
        <div className={styles.headerIcon} onClick={popStack}>
         <Icon name={ICONS_NAME.BACK_ICON} size={3.8} />
        </div>
      )}
      <div className={styles.nameContainer}>
        <div className={styles.investCareContainer}>
          <p className={styles.name}>{name || symbol}</p>
          <If test={showNudgeInfo}>
          <Icon
              name={ICONS_NAME.NUDGE_INFO}
              size={4.4}
              onClick={() => openNudgeInfo(true)}
            />
          </If>
        </div>
        <If test={!siblingData.sibling}>
          <Price
            securityId={securityId}
            segment={segment}
            exchange={exchange}
          />
        </If>
        <If test={siblingData.sibling}>
          <MultipleExchangePrice
            segment={segment}
            securityId={securityId}
            exchange={exchange}
            siblingData={siblingData}
            activeExchange={activeExchange}
            onExchangeSelect={onExchangeSelect}
            selectedExchangeEvent={selectedExchangeEvent}
          />
        </If>
      </div>
    </div>
  );
}

export default Header;
