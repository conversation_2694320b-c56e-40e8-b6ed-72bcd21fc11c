import React from 'react';
import cx from 'classnames';
import { useSwipeable } from 'react-swipeable';

import { TRANSACTION_TYPES } from '../../../../../utils/Equities/enum';

import If from '../../../../atoms/If';
import FadeLoader from '../../../../atoms/FadeLoader';

import { useSwipeAbleButton } from './useSwipeableButton';

import Slider from './partials/Slider';

import styles from './index.scss';

function SwipeAbleButton({
  txnType,
  onComplete,
  isLoading = false,
  isLocked = false,
  reset = true,
}) {
  const {
    sliderRef,
    sliderContainerRef,
    onDragEnd,
    onDrag,
  } = useSwipeAbleButton({
    reset,
    isLocked,
    isLoading,
    onComplete,
    threshold: 70,
  });

  const handlers = useSwipeable({
    onSwipedRight: onDragEnd,
    onSwiping: onDrag,
  });

  const refPassThrough = el => {
    handlers.ref(el);
    sliderRef.current = el;
  };

  const isBuy = txnType === TRANSACTION_TYPES.BUY;
  return (
    <div className={styles.container}>
      <div
        className={cx(styles.track, {
          [styles.buy]: isBuy,
          [styles.sell]: !isBuy,
          [styles.disabled]: isLoading || isLocked,
        })}
        ref={sliderContainerRef}
      >
        <div {...handlers} ref={refPassThrough} className={styles.slider}>
          <Slider isBuy={isBuy} isLoading={isLoading} />
        </div>
      </div>

      <If test={isLoading}>
        <div className={styles.loaderContainer}>
          <FadeLoader />
        </div>
      </If>
    </div>
  );
}

export default SwipeAbleButton;
