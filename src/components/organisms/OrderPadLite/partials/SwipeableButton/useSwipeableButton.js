/* eslint-disable no-use-before-define */
import { useEffect, useState, useRef, useMemo } from 'react';
import { RIGHT } from 'react-swipeable';

const SLIDER_BUFFER = 50;
const ANIMATION_TIME = 300;
const THRESHOLD = 90;

function useSwipeAbleButton({
  threshold,
  onComplete,
  reset,
  isLocked,
  isLoading,
}) {
  const sliderContainerRef = useRef(null);
  const sliderRef = useRef(null);
  const [sliderContainerPositions, setSliderContainerPositions] = useState({});

  useEffect(() => {
    if (sliderContainerRef.current) {
      const {
        right: containerRight,
        width: containerWidth,
      } = sliderContainerRef.current.getBoundingClientRect();
      setSliderContainerPositions({
        containerRight,
        containerWidth,
      });
    }
  }, [sliderContainerRef.current]);

  const sliderWidth = useMemo(
    () => sliderRef.current?.getBoundingClientRect()?.width,
    [sliderRef.current],
  );

  function onDragEnd(e) {
    if (isLoading || isLocked) {
      return false;
    }
    const { containerWidth } = sliderContainerPositions;
    const { absX } = e;
    const change = percentageChange({
      parts: absX + SLIDER_BUFFER,
      whole: containerWidth - sliderWidth,
    });

    if (change >= threshold) {
      animateSlider({
        from: absX,
        to: containerWidth - sliderWidth,
        callback: () => {
          onComplete();
          if (reset) {
            resetSlider();
          }
        },
      });
    } else {
      animateSlider({
        from: absX,
        to: 0,
      });
    }
  }

  function onDrag(e) {
    if (isLoading || isLocked) {
      return;
    }
    if (e.dir === RIGHT) {
      const { absX } = e;
      const { containerWidth } = sliderContainerPositions;
      if (absX < containerWidth - sliderWidth) {
        sliderRef.current.style.left = `${absX}px`;
      }
    }
  }

  function animateSlider({ from, to, callback = () => {} }) {
    const { current: slider } = sliderRef;
    if (slider) {
      const animation = slider.animate(
        [{ left: `${from}px` }, { left: `${to}px` }],
        {
          duration: ANIMATION_TIME,
        },
      );
      animation.onfinish = () => {
        slider.style.left = `${to}px`;
        callback();
      };
    }
  }

  function resetSlider() {
    if (sliderRef.current) {
      sliderRef.current.style.left = '0px';
    }
  }

  return {
    sliderContainerRef,
    sliderRef,
    onDrag,
    onDragEnd,
  };
}

function percentageChange({ parts, whole }) {
  return (parts / whole) * 100;
}

export { useSwipeAbleButton, THRESHOLD };
