.container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.track {
  padding: 4px;
  width: 272px;
  height: 56px;
  border-radius: 60px;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.slider {
  position: relative;
}

.buy {
  background-color: #21c179;
  border: 2px solid #21c179;
}

.sell {
  background-color: #fd5154;
  border: 2px solid #fd5154;
}

.disabled {
  opacity: .7;
}

.loaderContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
