import React from 'react';
import cx from 'classnames';

import Icon ,{ICONS_NAME}from '../../../../../../molecules/Icon';

import { CTA } from '../../../../enum';

import styles from './index.scss';

function Slider({ isBuy, isLoading }) {
  return (
    <div
      className={cx(styles.container, {
        [styles.disable]: isLoading,
      })}
    >
      {isBuy ? (
        <Icon
          name={ICONS_NAME.ARROW_RIGHT_GREEN}
          size={5}
          className={styles.icon}
        />
      ) : (
        <Icon
          name={ICONS_NAME.ARROW_RIGHT_RED}
          size={5}
          className={styles.icon}
        />
      )}
      <p className={styles.cta}>{isBuy ? CTA.SWIPE_BUY : CTA.SWIPE_SELL}</p>
    </div>
  );
}

export default Slider;
