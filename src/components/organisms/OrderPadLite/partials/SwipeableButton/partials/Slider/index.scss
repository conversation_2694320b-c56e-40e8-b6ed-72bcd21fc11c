@import '@src/commonStyles/variables.scss';

.container {
  width: 60px;
  height: 48px;
  border-radius: 30px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
}

.icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}


.cta {
  white-space: nowrap;
  position: relative;
  margin: 0;
  left: 200%;
  pointer-events: none;
  @include typography(body2B1, #ffffff);
}

.disable {
  display: none;
}
