import { useRef } from 'react';

import {
  ACTIONS,
  CATEGORY,
  SCREENS,
} from '../../../analyticsEvents/orderPadLite';
import { generateLabel } from '../../../utils/coreUtil';
import { PULSE_STATICS } from '../SubsequentFundCard/enums';
import { PULSE_STATICS_FIRST_CARDS } from '../../../utils/constants';
import { useAnalyticsEvent } from '../../../hooks/analyticsHooks';

const useOrderPadLiteEvents = (
  isIrOrderPadLite,
  isSubsequentTrade,
  isSameSector,
) => {
  const { sendAnalyticsEvent } = useAnalyticsEvent();
  const commonParams = useRef([
    isIrOrderPadLite
      ? PULSE_STATICS_FIRST_CARDS.SCREEN_NAME
      : SCREENS.ADD_FUND_SUCCESS,
    CATEGORY.ONBOARDING_V3,
  ]);

  const getAction = (action) =>
    isSubsequentTrade
      ? action.replace(
          'firsttrade',
          isSameSector ? 'ss_subsequent_trade' : 'ds_subsequent_trade',
        )
      : action;

  if (isIrOrderPadLite)
    return {
      userLandOrderPadScreen: (stockName) => {
        const action =
          PULSE_STATICS.ACTION.first_trade.firsttrade_land_order_screen;
        sendAnalyticsEvent(
          ...commonParams.current,
          getAction(action),
          generateLabel({
            stockName,
          }),
        );
      },
      useSelectExchange: (exchange) => {
        const action =
          PULSE_STATICS.ACTION.first_trade.firsttrade_select_exchange;
        sendAnalyticsEvent(
          ...commonParams.current,
          getAction(action),
          generateLabel({
            exchange,
          }),
        );
      },
      userInputQty: () => {
        const action = PULSE_STATICS.ACTION.first_trade.firsttrade_input_qty;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      clickedOrderCharges: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade.firsttrade_charges_link_clicked;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      onBackClick: () => {
        const action = PULSE_STATICS.ACTION.first_trade.firsttrade_back_click;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      chargesBottomSheet: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade
            .firsttrade_charges_bottomsheet_displayed;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },

      chargesBottomSheetClosed: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade
            .firsttrade_charges_bottomsheet_closed;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },

      onPlaceOrder: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade.firsttrade_buy_order_clicked;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },

      investCare: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade.firsttrade_InvestCare_error_received;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      investCareDrawer: () => {
        const action = PULSE_STATICS.ACTION.first_trade.firsttrade_investcare;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      investCareCta: (cta) => {
        const action =
          cta === 'YES'
            ? PULSE_STATICS.ACTION.first_trade.firsttrade_investcare_yes_clicked
            : PULSE_STATICS.ACTION.first_trade.firsttrade_investcare_no_clicked;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      insufficientFunds: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade
            .firsttrade_insufficient_funds_error_recieved;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      addFundsClicked: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade.firsttrade_add_funds_clicked;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      invalidLotSize: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade
            .first_trade_order_lotsize_error_received;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      genericError: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade.first_trade_order_pad_error_received;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      insufficientFundsSheet: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade
            .first_trade_Insufficient_fund_bottomsheet_landed;
        sendAnalyticsEvent(
          ...commonParams.current,
          ACTIONS.INSUFFICIENT_FUND_BOTTOMSHEET_LANDED,
          getAction(action),
        );
      },
      addFunds: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade.first_trade_Add_fund_clicked;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
      modifyFunds: () => {
        const action =
          PULSE_STATICS.ACTION.first_trade
            .first_trade_modify_order_Insufficient_fund_bottomsheet_clicked;
        sendAnalyticsEvent(...commonParams.current, getAction(action));
      },
    };

  return {
    userLandOrderPadScreen: (stockName) => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.FIRST_TRADE_ORDER_PAD_LANDED,
        generateLabel({
          stockName,
        }),
      );
    },
    useSelectExchange: (exchange) => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.EXCHANGE_SELECTED,
        generateLabel({
          exchange,
        }),
      );
    },
    userInputQty: () => {
      sendAnalyticsEvent(...commonParams.current, ACTIONS.QUANTITY_SELECTED);
    },

    clickedOrderCharges: () => {
      sendAnalyticsEvent(...commonParams.current, ACTIONS.CHARGES_LINK_CLICKED);
    },

    onBackClick: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.FIRST_ORDER_PAD_SCREEN_BACK_CLICKED,
      );
    },

    chargesBottomSheet: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.CHARGES_BOTTOMSHEET_DISPLAYED,
      );
    },

    chargesBottomSheetClosed: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.CHARGES_BOTTOMSHEET_CTA_CLICKED,
      );
    },

    onPlaceOrder: () => {
      sendAnalyticsEvent(...commonParams.current, ACTIONS.BUY_ORDER_CLICKED);
    },

    investCare: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.INVESTCARE_ERROR_RECEIVED,
      );
    },
    investCareDrawer: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.INVESTCARE_BOTTOM_SHEET_DISPLAYED,
      );
    },
    investCareCta: (cta) => {
      sendAnalyticsEvent(
        ...commonParams.current,
        cta === 'YES'
          ? ACTIONS.INVESTCARE_BOTTOM_SHEET_YES_CLICKED
          : ACTIONS.INVESTCARE_BOTTOM_SHEET_NO_CLICKED,
      );
    },
    insufficientFunds: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.INSUFFICIENT_FUND_ERROR_RECEIVED,
      );
    },
    invalidLotSize: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.ORDER_LOTSIZE_ERROR_RECEIVED,
      );
    },
    genericError: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.ORDER_PAD_ERROR_RECEIVED,
      );
    },
    insufficientFundsSheet: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.INSUFFICIENT_FUND_BOTTOMSHEET_LANDED,
      );
    },
    addFunds: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.ADD_FUND_INSUFFICIENT_FUND_BOTTOMSHEET_CLICKED,
      );
    },
    modifyFunds: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.MODIFY_ORDER_INSUFFICIENT_FUND_BOTTOMSHEET_CLICKED,
      );
    },
  };
};

const useOrderConfirmationEvents = () => {
  const { sendAnalyticsEvent } = useAnalyticsEvent();
  const commonParams = useRef([
    SCREENS.ORDER_SUCCESS_SCREEN,
    CATEGORY.ONBOARDING_V3,
  ]);
  const commonParamsBottomSheet = useRef([
    SCREENS.ORDER_STATUS_BOTTOMSHEET_FIRST_ORDER_SUCCESS,
    CATEGORY.ONBOARDING_V3,
  ]);

  return {
    landOrderConfirmationScreen: (stocks) => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.FIRST_ORDER_SUCCESS_SCREEN_LANDED,
        { stocks },
      );
    },
    viewDetailsClick: (stock) => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.VIEW_DETAILS_FIRST_ORDER_SUCCESS_SCREEN_CLICKED,
        { stock },
      );
    },
    ctaClick: () => {
      sendAnalyticsEvent(
        ...commonParams.current,
        ACTIONS.GO_TO_HOMEPAGE_FIRST_ORDER_SUCCESS_SCREEN_CTA_CLICKED,
      );
    },
    needHelpCtaClick: () => {
      sendAnalyticsEvent(
        ...commonParamsBottomSheet.current,
        ACTIONS.NEED_HELP_ORDER_STATUS_BOTTOMSHEET_CLICKED,
      );
    },
    cancelOrderCtaClick: () => {
      sendAnalyticsEvent(
        ...commonParamsBottomSheet.current,
        ACTIONS.CANCEL_ORDER_STATUS_BOTTOMSHEET_CLICKED,
      );
    },
    bottomSheetOpened: () => {
      sendAnalyticsEvent(
        ...commonParamsBottomSheet.current,
        ACTIONS.ORDER_STATUS_BOTTOMSHEET_LANDED,
      );
    },
  };
};

export { useOrderConfirmationEvents, useOrderPadLiteEvents };
