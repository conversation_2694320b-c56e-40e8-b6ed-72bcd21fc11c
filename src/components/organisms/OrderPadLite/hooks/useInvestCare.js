import { useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { useEquityInfoCard, useNudgeInfo } from '../../../../query/stockQuery';

// import { goBack } from '@services/coreUtil';
// import history from '@src/history';

function useInvestCare({ securityId, segment, exchange, isin }) {
  const [openInvestCare, setOpenInvestCare] = useState(false);
  const navigate = useNavigate();

  const { data: { data: nudgeInfo } = {}, isLoading: nudgeInfoLoading } =
    useNudgeInfo({
      securityId,
      segment,
      exchange,
      isin,
    });

  const {
    data: { data: equityInfoCard } = {},
    isLoading: equityInfoCardLoading,
  } = useEquityInfoCard();

  useEffect(() => {
    const showNudgeInfo =
      nudgeInfo?.is_surveillance_indicator ||
      nudgeInfo?.is_ban ||
      ((nudgeInfo?.is_re_scrip || nudgeInfo?.is_t2t_scrip) && equityInfoCard);

    if (showNudgeInfo) setOpenInvestCare(true);
  }, [nudgeInfo, equityInfoCard]);

  function onAccept() {
    setOpenInvestCare(false);
  }

  function onReject() {
    navigate(-1);
  }

  return {
    openInvestCare,
    investCareLoading: nudgeInfoLoading || equityInfoCardLoading,
    onAccept,
    onReject,
    nudgeInfo,
    equityInfoCard,
    setOpenInvestCare,
    showNudgeInfo:
      nudgeInfo?.is_surveillance_indicator ||
      nudgeInfo?.is_ban ||
      ((nudgeInfo?.is_re_scrip || nudgeInfo?.is_t2t_scrip) && equityInfoCard),
  };
}

export { useInvestCare };
