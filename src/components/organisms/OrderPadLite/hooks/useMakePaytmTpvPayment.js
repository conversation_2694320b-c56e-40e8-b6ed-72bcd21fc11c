import { useCallback, useState } from 'react';
import {
  MAKE_PAYMENT_RESPONSE_TYPES,
  PAYMENT_OPTIONS,
  PAYMENT_UPI_INTENT,
} from '../../../../config/paymentConfig';
import {
  // openPaymentMinkasuFlow,
  openPaymentSDKFlow,
  openPaytmMoneyPaymentAndGenericWebActivity,
  openUpiIntentApp,
} from '../../../../utils/bridgeUtils';
// import { emptyObj, isIosBuild, log } from '@src/utils/commonUtil';

import { isEquityMiniApp } from '../../../../utils/apiUtil';
import { isIosBuild, log } from '../../../../utils/commonUtil';
// import { useNudgeCall } from '@src/query/paymentsQuery';
// import { useLocation } from 'react-router-dom';

const useMakePaytmTpvPayment = () => {
  const [txnStatusdata, setTxnStatusData] = useState(null);
  const [makeNudgeCall, setMakeNudgeCall] = useState(false);

  // let nudgePaymentForMfIntent = false;

  // const { state: historyState } = useLocation();
  // const { flow, paymentTxnId } = historyState || emptyObj;;
  // const { refetch: nudgeCall } = useNudgeCall(paymentTxnId);

  const getAndroidPaytmIntentDeeplink = (url, appIdentifier) => {
    // Deeplink upi://pay?pa=paytmmoneylimited@icici&pn...&am=2.36&cu=INR
    // converting to paytmmp://pay?pa=paytmmoney11.paytm@hdfcbank&pn...&am=2.36&cu=INR&utmSrc=PMIntentFlow
    const modifiedUrl = `${appIdentifier}${
      url.split('//')?.[1]
    }&utmSrc=PMIntentFlow`;
    return modifiedUrl;
  };

  const getIosIntentDeeplink = (url, appIdentifier) => {
    // Deeplink upi://pay?pa=paytmmoneylimited@icici&pn=PaytmMoneyLimited&mc=....
    // converting to tez://upi/pay?pa=paytmmoneylimited@icici&pn=..
    const modifiedUrl = `${appIdentifier}${url.split('//')?.[1]}`;
    return modifiedUrl;
  };

  // callback from bridge call
  const makePaymentBridgeCallCallBack = useCallback(
    async (result, isIosPush, isAutopayIntent, isAutopayCollectFlow) => {
      console.log('paymentCallBack result', result);
      console.log('makePaymentBridgeCallCallBack result ::: ', result);
      if (result && result?.data) {
        const data = JSON.parse(result.data);
        console.log(`Data back from the webview....${JSON.stringify(data)}`);
        const txnStatus = data.isTransactionDone;
        if (txnStatus) {
          console.log('makeNudgeCall status', makeNudgeCall);
          // if (makeNudgeCall || nudgePaymentForMfIntent) {
          //   await nudgeCall();
          // }
          setTxnStatusData({
            isTransactionDone: true,
            orderId: data.order_id,
            txnId: data.txnId,
            isIosPush,
            isAutopayIntent,
            isAutopayCollectFlow,
          });
        } else {
          console.log('cancelling the payment');
          setTxnStatusData({
            isTransactionDone: false,
            orderId: data.order_id,
            txnId: data.txnId,
            isIosPush,
            isAutopayIntent,
            isAutopayCollectFlow,
          });
        }
      } else {
        setTxnStatusData({
          isTransactionDone: false,
          isIosPush,
          isAutopayIntent,
          isAutopayCollectFlow,
        });
      }
    },
    [makeNudgeCall],
  );

  const makePaymentBridgeCall = useCallback(
    (data, sdkFlow, isAutopayCollectFlow) => {
      console.log('makePaymentBridgeCall data ===>', data);
      const exitIntentConfig = data?.exitIntentConfig?.exitIntentEnabled
        ? data?.exitIntentConfig
        : {};
      let postData = {};
      if (data.type === MAKE_PAYMENT_RESPONSE_TYPES.UPI_MANDATE) {
        postData = {
          webViewUrl: data.webViewUrl || null,
          webViewType:
            'CDSL' ||
            PAYMENT_OPTIONS?.[data.type]?.WEB_VIEW_TYPE ||
            data?.webViewType,
          webViewTitle:
            PAYMENT_OPTIONS?.[data.type]?.WEB_VIEW_TITLE || data?.webViewTitle,
          redirectionUrl: data?.redirectionUrl || null,
          param: data?.param || null,
          ...exitIntentConfig,
        };
      } else {
        postData = {
          webViewUrl: data.webViewUrl || null,
          webViewType: PAYMENT_OPTIONS[data.type].WEB_VIEW_TYPE,
          webViewTitle: PAYMENT_OPTIONS[data.type].WEB_VIEW_TITLE,
          redirectionUrl: data.redirectionUrl || null,
          txnId: data.txnId,
          vpa: data.vpa || null,
          processTxnRequestParams: data.processTxnRequestParams || null,
          amount: data?.amount?.toString(),
          ...exitIntentConfig,
        };
      }
      console.log(
        `paytmNavigateTo payload..,${JSON.stringify(postData)}`,
        postData,
      );

      if (sdkFlow) {
        openPaymentSDKFlow(
          postData,
          makePaymentBridgeCallCallBack,
          isIosBuild(),
        );
      } else {
        openPaytmMoneyPaymentAndGenericWebActivity(
          postData,
          makePaymentBridgeCallCallBack,
          isAutopayCollectFlow, // true - indicates autopaycollect flow
        );
      }
    },
    [makePaymentBridgeCallCallBack],
  );

  // post payment bridge call
  const makePaytmTpvPayment = useCallback(
    ({
      responseType,
      redirectionUrl = null,
      listenUrl = null,
      paymentsTxnId,
      vpa = null,
      processTxnRequestParams = null,
      amount,
      upiDeepLink,
      processName,
      isAutopayCollectFlow = false,
      param,
      exitIntentConfig = {},
      // isQuickPaymentFlow = false,
      txnToken = null,
      mid = null,
      fmsTxnId,
    }) => {
      // Net banking flow and UPI collect flow
      if (responseType === MAKE_PAYMENT_RESPONSE_TYPES.REDIRECT) {
        const paymentParams = {
          type: responseType,
          webViewUrl: redirectionUrl,
          redirectionUrl: listenUrl,
          txnId: paymentsTxnId,
          amount,
          exitIntentConfig,
        };
        console.log('makePaymentBridgeCall ::: params:', paymentParams);
        makePaymentBridgeCall(paymentParams, false);
      } else if (responseType === MAKE_PAYMENT_RESPONSE_TYPES.UPI_TPV_FORM) {
        // this is not supported for now
        makePaymentBridgeCall(
          {
            type: responseType,
            txnId: paymentsTxnId,
            vpa,
            amount,
          },
          false,
        );
      } else if (responseType === MAKE_PAYMENT_RESPONSE_TYPES.INITIALIZE_SDK) {
        // this is not supported for now
        setMakeNudgeCall(true);
        makePaymentBridgeCall(
          {
            type: responseType,
            txnId: paymentsTxnId,
            processTxnRequestParams,
            amount,
          },
          true,
        );
      } else if (responseType === MAKE_PAYMENT_RESPONSE_TYPES.UPI_DEEP_LINK) {
        // if (flow === BBC_PAYMENT_DATA.FLOW_TYPES.MF)
        //   nudgePaymentForMfIntent = true;
        // const deeplinkUrl = isIosBuild()
        //   ? getIosIntentDeeplink(upiDeepLink, processName)
        //   : upiDeepLink;
        let deeplinkUrl;
        deeplinkUrl = upiDeepLink;
        if (
          isEquityMiniApp() &&
          processName === PAYMENT_UPI_INTENT.PAYTM_PROCESS_NAME
        ) {
          deeplinkUrl = getAndroidPaytmIntentDeeplink(
            upiDeepLink,
            PAYMENT_UPI_INTENT.PAYTM_APP_IDENTIFIER,
          );
        } else if (isIosBuild()) {
          deeplinkUrl = getIosIntentDeeplink(upiDeepLink, processName);
        }

        openUpiIntentApp(
          // UPI intent flow
          {
            deeplink: deeplinkUrl,
            processName,
            txnId: paymentsTxnId,
          },
          makePaymentBridgeCallCallBack,
          false,
          isIosBuild(),
        );
      } else if (responseType === MAKE_PAYMENT_RESPONSE_TYPES.UPI_MANDATE) {
        // this is not supported for now
        makePaymentBridgeCall(
          {
            type: responseType,
            webViewUrl: redirectionUrl,
            redirectionUrl: listenUrl,
            param,
            exitIntentConfig,
          },
          false,
          isAutopayCollectFlow,
        );
      } else if (
        responseType === MAKE_PAYMENT_RESPONSE_TYPES.INITIALIZE_MINKASU_SDK
      ) {
        // TODO: Minkasu flow
        // openPaymentMinkasuFlow(
        //   {
        //     responseType,
        //     redirectionUrl,
        //     paymentsTxnId,
        //     listenUrl,
        //     txnToken,
        //     mid,
        //     fmsTxnId,
        //   },
        //   makePaymentBridgeCallCallBack,
        // );
      }
    },
    [makePaymentBridgeCall, makePaymentBridgeCallCallBack],
  );

  return {
    txnStatusdata,
    makePaytmTpvPayment,
  };
};

export default useMakePaytmTpvPayment;
