import { useState } from 'react';

import { PRODUCT_TYPES } from '../../../../utils/Equities/enum';

import { useDidUpdateEffect } from '../../../../utils/react';
import {
  useGetFundsSummary,
  useGetOrderFundsSummary,
} from '../../../../query/generalQuery';
import { useMarginRequired } from './useMarginRequired';

function useOrderFunds({
  exchange,
  txnType,
  productType,
  instrumentType,
  isSgb = false,
  price,
  isLimitOrder,
  quantity,
  segment,
  securityId,
  axiosSource,
  activeOrderType,
  firstTrade = false,
}) {
  const [isStale, setIsStale] = useState(false);

  const marginData = useMarginRequired({
    segment,
    securityId,
    exchange,
    txn_type: txnType,
    quantity,
    productType,
    isLimitOrder,
    price,
    isDisabled:
      productType === PRODUCT_TYPES.BRACKET_ORDER ||
      productType === PRODUCT_TYPES.COVER_ORDER,
    axiosSource,
  });

  useDidUpdateEffect(() => {
    setIsStale(true);
  }, [
    price,
    isLimitOrder,
    quantity,
    txnType,
    exchange,
    activeOrderType,
    productType,
  ]);

  const {
    data,
    refetch,
    isLoading,
    isError,
    isRefetching,
  } = useGetOrderFundsSummary(
    {
      transaction_type: txnType,
      product_type: productType,
      instrument_type: instrumentType,
      is_sgb: isSgb,
    },
    !firstTrade,
  );

  const firstTradeFundsData = useGetFundsSummary(firstTrade);

  async function retry(cb) {
    marginData.retry();
    if (firstTrade) {
      await firstTradeFundsData.refetch();
    } else {
      await refetch();
    }
    setIsStale(false);
    if (cb && typeof cb === 'function') {
      cb();
    }
  }

  return {
    fundsData: {
      data,
      refetch,
      isLoading: isLoading || isRefetching,
      isError,
      firstTradeFundsData,
    },
    isStale,
    marginData,
    retry,
  };
}
export { useOrderFunds };
