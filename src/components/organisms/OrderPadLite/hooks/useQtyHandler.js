/* eslint-disable no-use-before-define */
import { useState, useMemo } from 'react';
import { ERROR_MESSAGES } from '../enum';
import { formatPrice } from '../../../molecules/Prices';

function useQtyHandler({
  positionModify,
  netQty,
  modify,
  initialQuantity,
  isDerivative,
  lotSize,
  boCoModify,
  qty,
  modifyCover,
  boModify,
  maxQty = Infinity,
}) {
  const { modifyBracket, disableQty } = boModify;
  const disableQtyModify = modifyCover || (modifyBracket && disableQty);

  const [quantity, setQuantity] = useState(getQty());

  const derivativeQtyHandler = useDerivativeQtyHandler({
    lotSize,
    disableQtyModify,
    modify,
    initialQuantity,
    netQty,
    positionModify,
  });

  function onIncrement() {
    if (disableQtyModify) return;
    setQuantity(prevState => {
      let temp = prevState;
      if (temp && typeof temp === 'string') {
        temp = parseInt(temp, 10);
      }
      return temp === 0 ? temp : temp + 1;
    });
  }

  function onDecrement() {
    if (disableQtyModify) return;
    setQuantity(prevState => {
      if (prevState === 0) {
        return prevState;
      }
      return prevState - 1;
    });
  }

  return {
    isValid: !!quantity,
    errMessage: quantity
      ? quantity > maxQty
        ? `Max qty allowed: ${formatPrice(maxQty, 0)}`
        : ''
      : ERROR_MESSAGES.INVALID_QTY,
    quantity,
    setQuantity,
    onQuantityChange,
    derivativeQtyHandler,
    disabled: boCoModify?.disableQty || disableQtyModify,
    isDerivative,
    onIncrement,
    onDecrement,
    disableIncrement:
      (parseInt(quantity, 10) + 1).toString().length > 9 || disableQtyModify,
    disableDecrement: quantity <= 1 || disableQtyModify,
  };

  function onQuantityChange(e) {
    if (e.target.value.length < 10) {
      const sanitizedStr = e.target.value.replace(/^0+/, '');
      setQuantity(sanitizedStr.replace(/[^\d]/, ''));
    }
  }

  function getQty() {
    if (positionModify) {
      return netQty;
    }
    return modify ? initialQuantity : qty || 1;
  }
}

function useDerivativeQtyHandler({
  lotSize,
  disableQtyModify,
  modify,
  initialQuantity,
  netQty,
  positionModify,
}) {
  const [lotQty, setLotQty] = useState(getQty());

  const quantity = useMemo(() => lotQty * lotSize, [lotQty]);

  function onLotIncrement() {
    if (disableQtyModify) return;
    setLotQty(prevState => {
      let temp = prevState;
      if (temp && typeof temp === 'string') {
        temp = parseInt(temp, 10);
      }
      return temp === 0 ? temp : temp + 1;
    });
  }

  function onLotDecrement() {
    if (disableQtyModify) return;
    setLotQty(prevState => {
      if (prevState === 1) return prevState;
      return prevState - 1;
    });
  }

  function onLotQtyChange(e) {
    if (e.target.value.length < 10) {
      const sanitizedStr = e.target.value.replace(/^0+/, '');
      setLotQty(sanitizedStr.replace(/[^\d]/, ''));
    }
  }

  function getQty() {
    if (positionModify) {
      return netQty / lotSize;
    }
    if (modify) {
      if (initialQuantity) {
        return initialQuantity / lotSize;
      }
      return 1;
    }
    return 1;
  }

  return {
    isValid: !!quantity,
    errMessage: quantity ? '' : ERROR_MESSAGES.INVALID_LOT,
    quantity,
    lotQty,
    onLotQtyChange,
    onLotDecrement,
    onLotIncrement,
    disableIncrement:
      (parseInt(lotQty, 10) + 1).toString().length > 9 || disableQtyModify,
    disableDecrement: lotQty <= 1 || disableQtyModify,
    disabled: disableQtyModify,
  };
}

export { useQtyHandler };
