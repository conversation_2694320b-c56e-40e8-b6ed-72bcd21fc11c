import { useState, useRef } from 'react';

function useExchangeSelector({ exchange }) {
  const exchangeContainerRef = useRef(null);
  const [activeExchange, setActiveExchange] = useState(exchange);
  const [bufferedPrice, setBufferedPrice] = useState({});

  function onExchangeSelect(value) {
    setActiveExchange(value);
  }

  return {
    exchangeContainerRef,
    activeExchange,
    onExchangeSelect,
    bufferedPrice,
    setBufferedPrice,
    isLocked: !activeExchange,
  };
}

export { useExchangeSelector };
