import { useState, useEffect } from 'react';
import { getSingleCompanyDetails } from '../../../../actions/companyDetailsAction';

function useGetSiblings({ siblings, exchange, axiosSource }) {
  const [siblingData, setSiblingData] = useState(null);
  const [siblingDataLoader, setSiblingDataLoader] = useState(false);

  const sibling = siblings.find(
    (stockDetails) => stockDetails.exchange !== exchange,
  );

  useEffect(() => {
    if (sibling && !siblingData) {
      const id = parseInt(sibling.id, 10);
      if (Number.isFinite(id)) {
        setSiblingDataLoader(true);
        getSingleCompanyDetails(sibling.id, axiosSource)
          .then((response) => {
            setSiblingData(response.data.results[0]);
            setSiblingDataLoader(false);
          })
          .catch(() => {
            setSiblingDataLoader(false);
          });
      }
    }
  }, [siblings, exchange, siblingData]);

  return {
    sibling,
    siblingData,
    siblingDataLoader,
  };
}

export { useGetSiblings };
