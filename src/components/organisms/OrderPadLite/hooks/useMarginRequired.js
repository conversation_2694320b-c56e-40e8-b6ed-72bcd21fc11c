/* eslint-disable no-use-before-define */
import { useState, useCallback, useEffect } from 'react';

import { getMargin } from '../../../../actions/orderPadActions';
import { AxiosErrorHandler } from '../../../../utils/errorUtils';
import { PRODUCT_TYPES } from '../../../../utils/Equities/enum';

function useMarginRequired({
  segment,
  exchange,
  securityId,
  txn_type,
  quantity,
  productType,
  axiosSource,
  isLimitOrder,
  price,
  isDisabled,
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [marginRequired, setMarginRequired] = useState(0);

  const updateMargin = useCallback(async data => {
    setIsLoading(true);
    setIsError(false);
    try {
      const {
        data: {
          data: { t_total_margin },
        },
      } = await getMargin({
        ...data,
        axiosSource,
      });

      setMarginRequired(t_total_margin);
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
      setIsError(true);
      AxiosErrorHandler(err);
    }
  }, []);

  useEffect(() => {
    if (areParamsValid()) {
      if (productType !== PRODUCT_TYPES.DELIVERY && !isDisabled) {
        setIsLoading(true);
        updateMargin({
          segment,
          exchange,
          securityId,
          txn_type,
          quantity,
          productType,
          isLimitOrder,
          price,
        });
      }
    }
  }, []);

  function retry() {
    if (productType !== PRODUCT_TYPES.DELIVERY && !isDisabled) {
      if (areParamsValid()) {
        updateMargin({
          segment,
          exchange,
          securityId,
          txn_type,
          quantity,
          productType,
          isLimitOrder,
          price,
        });
      }
    }
  }

  return {
    marginError: isError,
    marginLoading: isLoading,
    marginRequired,
    retry,
  };

  function areParamsValid() {
    return (
      segment && exchange && securityId && txn_type && quantity && price !== ''
    );
  }
}

export { useMarginRequired };
