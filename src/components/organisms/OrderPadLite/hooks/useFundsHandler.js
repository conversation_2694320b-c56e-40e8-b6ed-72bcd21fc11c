import { useState } from 'react';
import { useGetFundsSummary } from '../../../../query/generalQuery';

function useFundsHandler(triggerApi) {
  const [showAddFunds, setShowAddFunds] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const fundsSummary = useGetFundsSummary(triggerApi);
  const funds = fundsSummary?.data?.data?.funds_summary?.trade_balance;

  function closeFundsPopup() {
    setShowAddFunds(false);
  }

  return {
    showAddFunds,
    fundsSummary,
    funds,
    setShowAddFunds,
    errorMessage,
    setErrorMessage,
    closeFundsPopup,
  };
}

export { useFundsHandler };
