import { useMemo } from 'react';
import { useMarketStatus } from '../../../../contexts/MarketStatusContext';

function useAmo({ segment, activeExchange }) {
  const { marketStatus } = useMarketStatus();
  const marketConfig = useMemo(
    () => marketStatus?.[segment]?.[activeExchange],
    [activeExchange, marketStatus, segment],
  );

  return {
    amo: marketConfig?.amo,
  };
}

export { useAmo };
