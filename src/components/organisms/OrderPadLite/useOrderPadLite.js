import { useExchangeSelector } from './hooks/useExchangeSelector';
import { useGetSiblings } from './hooks';
import { useInvestCare } from './hooks/useInvestCare';

function useOrderPadLite({ axiosSource, data }) {
  const {
    security_id: securityId,
    exchange,
    symbol,
    segment,
    name,
    isin,
    instrument_type: instrumentType,
    id,
    siblings,
    lot_size: lotSize,
  } = data;

  const { activeExchange, onExchangeSelect } = useExchangeSelector({
    exchange,
  });

  const { sibling, siblingData, siblingDataLoader } = useGetSiblings({
    siblings,
    exchange: activeExchange,
    axiosSource,
  });

  const investCareHandler = useInvestCare({
    securityId,
    segment,
    exchange,
    isin,
  });

  return {
    stockInfo: {
      initialExchange: exchange,
      initialSecurityId: securityId,
      name,
      symbol,
      segment,
      lotSize:
        activeExchange === siblingData?.exchange
          ? siblingData?.lot_size
          : lotSize,
      securityId:
        activeExchange === siblingData?.exchange
          ? siblingData?.security_id
          : securityId,
      instrumentType,
      id: activeExchange === siblingData?.exchange ? siblingData?.id : id,
    },
    exchangeHandler: {
      activeExchange,
      onExchangeSelect,
    },
    siblingData: {
      sibling,
      siblingData,
      siblingDataLoader,
    },
    investCareHandler,
  };
}

export { useOrderPadLite };
