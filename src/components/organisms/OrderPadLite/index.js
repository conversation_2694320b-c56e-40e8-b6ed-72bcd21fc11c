import { useEffect } from 'react';

import Header from './partials/Header';
import InvestCare from '../InvestCare';
import { useOrderPadLite } from './useOrderPadLite';
import { TRANSACTION_TYPES } from '../../../utils/Equities/enum';
import Buy from './partials/Buy';
import { useOrderPadLiteEvents } from './useAnalyticalEvents';

function OrderPadLite({
  axiosSource,
  locationHeader,
  data,
  isIrOrderPadLite,
  onOrderSuccess,
  isSubsequentTrade,
  isSameSector,
}) {
  const { stockInfo, exchangeHandler, siblingData, investCareHandler } =
    useOrderPadLite({ axiosSource, data });

  const events = useOrderPadLiteEvents(
    isIrOrderPadLite,
    isSubsequentTrade,
    isSameSector,
  );

  useEffect(() => {
    events.userLandOrderPadScreen(data.name);
  }, [data.name]);

  const { symbol, segment, name } = stockInfo;
  const { activeExchange, onExchangeSelect } = exchangeHandler;
  return (
    <div>
      <InvestCare
        investCareHandler={investCareHandler}
        displayName={symbol || name}
        analyticalEvents={events}
      >
        <Header
          name={name}
          exchange={stockInfo.initialExchange}
          symbol={symbol}
          segment={segment}
          securityId={stockInfo.initialSecurityId}
          onExchangeSelect={onExchangeSelect}
          showNudgeInfo={investCareHandler.showNudgeInfo}
          openNudgeInfo={investCareHandler.setOpenInvestCare}
          siblingData={siblingData}
          activeExchange={activeExchange}
          selectedExchangeEvent={events.useSelectExchange}
          backPressEvent={events.onBackClick}
          isIrOrderPadLite={isIrOrderPadLite}
        />
        <Buy
          stockInfo={{
            ...stockInfo,
            name: symbol || name,
            activeExchange,
          }}
          locationHeader={locationHeader}
          txnType={TRANSACTION_TYPES.BUY}
          axiosSource={axiosSource}
          investCareLoading={investCareHandler.investCareLoading}
          events={events}
          isIrOrderPadLite={isIrOrderPadLite}
          onOrderSuccess={onOrderSuccess}
        />
      </InvestCare>
    </div>
  );
}

export default OrderPadLite;
