import { lazy, Suspense, useEffect } from 'react';

import Drawer from '../../molecules/DrawerV2';

import If from '../../atoms/If';

const InvestCareDrawer = lazy(
  () =>
    import(
      /* webpackChunkName: 'InvestCareDrawer' */ './partials/InvestCareDrawer'
    ),
);

function InvestCare({
  displayName,
  children,
  investCareHandler,
  analyticalEvents,
}) {
  const { openInvestCare } = investCareHandler;
  useEffect(() => {
    if (openInvestCare) {
      if (analyticalEvents && analyticalEvents.investCare) {
        analyticalEvents.investCare();
      }
      if (analyticalEvents.investCareDrawer) {
        analyticalEvents.investCareDrawer();
      }
    }
  }, [openInvestCare]);

  return (
    <>
      {children}
      <Drawer isOpen={openInvestCare} showCross={false} closeAllowed={false}>
        <If test={openInvestCare}>
          <Suspense fallback={null}>
            <InvestCareDrawer
              investCareHandler={investCareHandler}
              displayName={displayName}
              analyticalEvents={analyticalEvents}
            />
          </Suspense>
        </If>
      </Drawer>
    </>
  );
}

export default InvestCare;
