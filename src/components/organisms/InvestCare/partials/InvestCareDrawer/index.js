import { Button } from '@paytm-h5-common/paytm_common_ui';

import { useImageLoader } from '../../../../../utils/react';
import If from '../../../../atoms/If';
import Shimmer from '../../../../atoms/Shimmer';

import { STATICS, CTA } from './statics';
import InvestCare from '../../../../../assets/investCare.svg';

import InvestCareMessage from '../InvestCareMessage';

import styles from './index.scss';

function InvestCareDrawer({
  investCareHandler,
  displayName,
  analyticalEvents,
}) {
  const { onAccept, onReject, nudgeInfo, equityInfoCard } = investCareHandler;

  const { image, isImageLoaded } = useImageLoader({ imgSrc: InvestCare });

  return (
    <div className={styles.container}>
      <figure className={styles.imageContainer}>
        {isImageLoaded ? (
          <img src={image} className={styles.image} alt="" />
        ) : (
          <Shimmer type="circle" width="150px" />
        )}
      </figure>
      <p className={styles.header}>{STATICS.HEADER}</p>

      <If test={nudgeInfo?.is_ban}>
        <InvestCareMessage
          displayName={displayName}
          message={nudgeInfo?.ban_desc}
          ctaText={STATICS.SURVEILLANCE_CTA_TEXT}
          ctaLink={STATICS.SURVEILLANCE_CTA_LINK}
          analyticalEvents={analyticalEvents}
          isFno
        />
      </If>

      <If test={nudgeInfo.is_surveillance_indicator}>
        <InvestCareMessage
          displayName={displayName}
          message={STATICS.SURVEILLANCE_DESC(nudgeInfo?.surveillance_desc)}
          ctaText={STATICS.SURVEILLANCE_CTA_TEXT}
          ctaLink={STATICS.SURVEILLANCE_CTA_LINK}
          analyticalEvents={analyticalEvents}
        />
      </If>

      <If test={nudgeInfo.is_re_scrip}>
        <InvestCareMessage
          displayName={displayName}
          message={equityInfoCard?.company_RE?.message}
          ctaLink={equityInfoCard?.company_RE?.cta_link}
          ctaText={equityInfoCard?.company_RE?.cta_text}
          analyticalEvents={analyticalEvents}
        />
      </If>

      <If test={nudgeInfo.is_t2t_scrip}>
        <InvestCareMessage
          message={equityInfoCard?.company_T2T?.message}
          ctaLink={equityInfoCard?.company_T2T?.cta_link}
          ctaText={equityInfoCard?.company_T2T?.cta_text}
          analyticalEvents={analyticalEvents}
        />
      </If>

      {nudgeInfo.is_surveillance_indicator ||
      nudgeInfo?.is_ban ||
      nudgeInfo?.is_t2t_scrip ? (
        <div className={styles.btnContainer}>
          <Button
            label={CTA.YES}
            emphasis="high"
            onClick={() => {
              onAccept();
              if (analyticalEvents) analyticalEvents.investCareCta('YES');
            }}
          />
          <Button
            label={CTA.NO}
            emphasis="medium"
            onClick={() => {
              onReject();
              if (analyticalEvents) analyticalEvents.investCareCta('NO');
            }}
          />
        </div>
      ) : (
        <div className={styles.btnContainer}>
          <Button
            emphasis="high"
            label={CTA.PROCEED}
            onClick={() => {
              onAccept();
              if (analyticalEvents) analyticalEvents.investCareCta('YES');
            }}
          />
        </div>
      )}
    </div>
  );
}

export default InvestCareDrawer;
