.container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.header {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: 700;
  color: #101010;
}

.btnContainer {
  width: 100%;
  margin-top: 33px;
}

.btn {
  background-color: var( --background-primary-strong);
  margin-bottom: 16px;
  border-radius: 8px;
}

.secondaryBtn {
  border: 1px solid  var( --background-primary-strong);
  border-radius: 8px;
}

.secondaryBtnText {
  color: var( --background-primary-strong);
}

.imageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0 10px;
}

.image {
  width: 150px;
}
