.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.displayName {
  text-align: center;

   span:first-child {
    margin-right: 3px;
    font-size: 14px;
    font-weight: 600;
    color: #101010;
  }

  span:nth-child(2) {
    font-size: 14px;
    color: #101010;

  }
}

.descContainer {
  margin-top: 10px;
}

.desc {
  font-size: 14px;
  color: #101010;

}

.knowMore {
  white-space: nowrap;
  margin-left: 4px;
  font-size: 12px;
  font-weight: 600;
  line-height: 1.5;
  color: var( --background-primary-strong);
}


