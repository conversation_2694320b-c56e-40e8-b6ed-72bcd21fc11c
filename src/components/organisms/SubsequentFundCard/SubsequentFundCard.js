/* eslint-disable react-hooks/rules-of-hooks */
import axios from 'axios';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import Button from '../../atoms/Button/Button';
import Drawer, { useDrawer } from '../../molecules/DrawerV2';
import { useOrders } from '../../../contexts/OrdersBookContext';
import { withErrorBoundary } from '../../../HOC/WidgetErrorBoundary';
import { emptyObj, generateQueryParamsString } from '../../../utils/commonUtil';
import { useAnalyticsEventForFirstCards } from '../../../hooks/analyticsHooks';
import CircularLoader from '../../atoms/CircularLoader/CircularLoader';
import CompanyIcon from '../../atoms/CompanyIcon/CompanyIcon';
import StockPriceChange from '../../molecules/StockPriceChange';
import VerifyUserLocationHOC from '../../../HOC/VerifyUserLocationHOC';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../../atoms/CompanyIcon/IconsList';
import { getGenericAppHeaders, makeApiPostCall } from '../../../utils/apiUtil';
import PMLDetailsWrapper from '../../../HOC/PMLDetailsWrapper';
import { GET_CHARTS_IMAGE } from '../../../config/urlConfig';
import Icon from '../../molecules/Icon';

import OrderPadLite from '../OrderPadLite/index';

import { ORDER_STATUS, TRANSACTION_TYPES } from '../../../utils/Equities/enum';
import { PULSE_STATICS } from './enums';
import styles from './SubsequentFundCard.scss';
import { useAppStore } from '../../../contexts/AppContextProvider';
import { APPEARANCE_TYPES } from '../../../utils/constants';

import SearchHomepage from '../../../assets/icons/search_homepage.svg';
import ROUTES from '../../../routes';
import { callOrderPadLiteDrawer } from '../../../utils/navigationUtil';

const SubsequentFundCard = (props) => {
  if (!props?.data) return <></>;
  const { isFirstTrade = false } = props;
  const config = props?.data?.items[0];
  const navigate = useNavigate();
  const { stocks, title, subtitle, buttons } = config || {};
  const [activeStep, setActiveStep] = useState(-1);
  const [chartImage, setChartImage] = useState(null);
  const [isSameSector, setIsSameSector] = useState();
  const { isOpen, onOpen, onClose } = useDrawer();
  const [orderNo, setOrderNo] = useState(null);
  // const toastRef = useRef(null);
  const searchRef = useRef(null);
  const { inProgress, ordersData, reFetchOrdersData } = useOrders();
  const { sendAnalyticsEventFirstCard } = useAnalyticsEventForFirstCards();

  const { addSnackBar } = useAppStore() || emptyObj;

  const sendPulseEvents = (
    action,
    event,
    label = '',
    label2 = '',
    label3 = '',
  ) => {
    sendAnalyticsEventFirstCard({
      event,
      action: isFirstTrade
        ? action
        : isSameSector
          ? `ss_${action}`
          : `ds_${action}`,
      label,
      label2,
      label3,
    });
  };

  const onOrderPadOpen = () => {
    if (isFirstTrade) {
      sendPulseEvents(
        PULSE_STATICS.ACTION.first_trade.firsttrade_orderpad,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
      );
    } else {
      sendPulseEvents(
        PULSE_STATICS.ACTION.subsequent_trade.subsequent_orderpad,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
      );
    }
  };

  const sendOrderStatus = (status) => {
    if (isFirstTrade) {
      sendPulseEvents(
        PULSE_STATICS.ACTION.first_trade.firsttrade_order_status,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
        status,
      );
    } else {
      sendPulseEvents(
        PULSE_STATICS.ACTION.subsequent_trade.subsequent_order_status,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
        status,
      );
    }
  };

  const onStockClicked = (item) => {
    if (isFirstTrade) {
      sendPulseEvents(
        PULSE_STATICS.ACTION.first_trade.firsttrade_changestock,
        PULSE_STATICS.CUSTOM_EVENT,
        item?.name,
      );
    } else {
      sendPulseEvents(
        PULSE_STATICS.ACTION.subsequent_trade.subsequent_changestock,
        PULSE_STATICS.CUSTOM_EVENT,
        item?.name,
      );
    }
  };

  const companyPageNavigation = () => console.log('companyPageNavigation');

  const triggerSearch = () => {
    if (searchRef.current) {
      searchRef.current.click();
    }
  };

  const fetchCharts = async (body) => {
    const url = GET_CHARTS_IMAGE;
    try {
      const response = await makeApiPostCall({
        url,
        body,
        headers: getGenericAppHeaders(),
      });
      return response.data;
    } catch (error) {
      if (!axios.isCancel(error)) {
        return null;
      }
      return null;
    }
  };
  useEffect(() => {
    if (!stocks?.length) return;
    const body = stocks.map(({ exchange, id }) => ({ exchange, pmlId: id }));
    fetchCharts(body).then((data) => {
      setChartImage(data?.data);
    });
    setIsSameSector(!stocks[0]?.indexId);
  }, [stocks]);

  useEffect(() => {
    if (isSameSector === undefined) return;

    if (isFirstTrade) {
      sendPulseEvents(
        PULSE_STATICS.ACTION.first_trade.firsttrade_widget,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
      );
    } else {
      sendPulseEvents(
        PULSE_STATICS.ACTION.subsequent_trade.subsequent_widget,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
      );
    }
  }, [isSameSector]);

  useEffect(() => {
    if (!orderNo || !ordersData || inProgress) return null;
    const filteredOrder = ordersData.find(
      (order) => order.order_no === orderNo,
    );
    if (filteredOrder?.status === ORDER_STATUS.TRADED) {
      props.refetchAggrData();
    }
  }, [ordersData, orderNo]);

  const handleOrderSuccess = (data) => {
    // TODO: remove this log
    console.log('handleOrderSuccess', data);
    if (data?.message) {
      addSnackBar({
        message: data?.message,
        type:
          data?.status?.toLowerCase() === 'success'
            ? APPEARANCE_TYPES.SUCCESS
            : APPEARANCE_TYPES.FAIL,
      });
      sendOrderStatus(data?.status?.toLowerCase());
    }
    if (data?.status?.toLowerCase() === 'success') {
      setOrderNo(data?.data?.[0]?.order_no);
      reFetchOrdersData();
    }
    navigate(-1);
    onClose();
  };

  function navigateToBuyScreen() {
    if (isFirstTrade) {
      sendPulseEvents(
        PULSE_STATICS.ACTION.first_trade.firsttrade_buyclick,
        PULSE_STATICS.CUSTOM_EVENT,
      );
    } else {
      sendPulseEvents(
        PULSE_STATICS.ACTION.subsequent_trade.subsequent_buyclick,
        PULSE_STATICS.CUSTOM_EVENT,
      );
    }
    const queryString = generateQueryParamsString({
      transactionType: TRANSACTION_TYPES.BUY,
      id: stocks?.[activeStep]?.id,
      securityId: stocks?.[activeStep]?.security_id,
      exchange: stocks?.[activeStep]?.exchange,
      name: stocks?.[activeStep]?.name,
      segment: stocks?.[activeStep]?.segment,
      quantity: 1,
      instrumentType: stocks?.[activeStep]?.instrument_type,
      isin: stocks?.[activeStep]?.isin,
      isFirstTrade,
      isSameSector,
    });
    // TODO handle: is h5 or module Federation
    callOrderPadLiteDrawer(`order-pard-lite${queryString}`);
    // navigate(`${ROUTES.ORDER_PAD_LITE}${queryString}`);
    // navigate(`/trade-card${queryString}`);
  }

  function navigateToSIP() {
    sendPulseEvents(
      PULSE_STATICS.ACTION.subsequent_trade.subsequent_stock_sip_click,
      PULSE_STATICS.CUSTOM_EVENT,
    );

    const queryString = generateQueryParamsString({
      txnType: TRANSACTION_TYPES.BUY,
      sipOrderType: true,
      id: stocks?.[activeStep]?.id,
      securityId: stocks?.[activeStep]?.security_id,
      exchange: stocks?.[activeStep]?.exchange,
      name: stocks?.[activeStep]?.name,
      segment: stocks?.[activeStep]?.segment,
      quantity: 1,
      instrumentType: stocks?.[activeStep]?.instrument_type,
      isin: stocks?.[activeStep]?.isin,
      isSubsequentTrade: true,
    });

    console.log(queryString);
    // TODO: Navigate to SIP Screen
    // navigateTo(
    //   history,
    //   `${MINI_APP_ROUTES.ORDER_PAD}${queryString}`,
    //   {},
    //   'push',
    // );
  }

  const addRemoveSriptAnalyticsEvent = (labels) => {
    if (isFirstTrade) {
      sendPulseEvents(
        PULSE_STATICS.ACTION.first_trade.firsttrade_watchlistclick,
        PULSE_STATICS.CUSTOM_EVENT,
        labels?.watchlistName,
        labels?.add ? labels?.add : labels?.remove,
        labels?.pmlId,
      );
    }
  };

  if (!config) return;
  return (
    <div className={styles.container}>
      <div className={styles.bgContainer}>
        <div className={styles.bgImageContainer}>
          <div className={styles.titleContainer}>
            <div className={styles.titleWrapper}>
              <span className={styles.text}> {title}</span>
              {/* // TODO: Search Icon */}
              <Icon url={SearchHomepage} width="15px" onClick={triggerSearch} />
            </div>
            <span className={styles.subTitle}> {subtitle}</span>
          </div>
          <CircularLoader
            items={stocks}
            duration={10000}
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            itemClickEvent={onStockClicked}
            isFirstTrade={isFirstTrade}
          />

          <div
            className={styles.companyDetailsWrapper}
            // TODO: Company Page Navigation
            // onClick={() => companyPageNavigation(stocks?.[activeStep]?.id)}
          >
            {!isFirstTrade && !isSameSector && (
              <CompanyIcon
                name={stocks?.[activeStep]?.id}
                type={COMPANY_ICONS_NAME.STOCKS}
                className={styles.iconWrapper}
              />
            )}
            <div className={styles.companyDetails}>
              <span className={styles.companyName}>
                {stocks?.[activeStep]?.name}
              </span>
              <div className={styles.ltpContainer}>
                <StockPriceChange
                  securityId={stocks?.[activeStep]?.security_id}
                  exchange={stocks?.[activeStep]?.exchange}
                  segment={stocks?.[activeStep]?.segment}
                  change={stocks?.[activeStep]?.seven_days_return}
                />
                <span className={styles.range}>(1 Week)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      {activeStep < chartImage?.length && (
        <img
          src={chartImage[activeStep]?.png}
          alt={stocks?.[activeStep]?.name}
          onClick={() => companyPageNavigation(stocks?.[activeStep]?.id)}
        />
      )}
      <div className={styles.ctaContainer}>
        {buttons?.map((button) => {
          if (button?.type === 'watchlist') {
            return (
              <div className={styles.watchListCta}>
                {/* // TODO handle watchlist */}
                {/* <FavIcon
                  companyDetails={stocks?.[activeStep]}
                  iconSize={4.8}
                  addRemoveSriptAnalyticsEvent={addRemoveSriptAnalyticsEvent}
                /> */}
              </div>
            );
          }
          return (
            <Button
              key={button.cta}
              buttonText={button.cta}
              className={
                button?.type === 'orderPad'
                  ? styles.greenCta
                  : styles.ctaCustomClass
              }
              buttonTextClassName={
                button.cta === 'Start SIP'
                  ? styles.ctaCustomButtonTextClass
                  : styles.buttonTextClassName
              }
              onClickHandler={() => {
                if (button.cta === 'Start SIP') {
                  navigateToSIP();
                  return;
                }
                navigateToBuyScreen();
                onOpen();
              }}
            />
          );
        })}
      </div>
      <Drawer
        isOpen={isOpen}
        onClose={() => {
          navigate(-1);
          onClose();
        }}
        showCross={false}
      >
        <VerifyUserLocationHOC>
          <PMLDetailsWrapper onError={onClose}>
            <OrderPadLite
              isIrOrderPadLite
              isSubsequentTrade={!isFirstTrade}
              onOrderSuccess={handleOrderSuccess}
              onOrderPadOpen={onOrderPadOpen}
              isSameSector={isSameSector}
            />
          </PMLDetailsWrapper>
        </VerifyUserLocationHOC>
      </Drawer>
      {/* // TODO : Navigate to Search Page */}
      {/* <SearchPageRevamp>
        <div ref={searchRef} />
      </SearchPageRevamp> */}
    </div>
  );
};

export default React.memo(withErrorBoundary(SubsequentFundCard));
