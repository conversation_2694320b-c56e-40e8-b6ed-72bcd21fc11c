.container {
  display: flex;
  flex-direction: column;
  margin: 0 16px 8px;
  background-color: #fff;
  border-radius: 12px;

  .bgContainer {
    border-radius: 12px 12px 0 0;
    display: flex;
    flex-direction: column;

    background: linear-gradient(180deg, #d4ddfd 0%, #c9dffd 50%, #ffffff 100%);
  }

  .bgImageContainer {
    border-radius: 12px 12px 0 0;
    display: flex;
    flex-direction: column;
    background-image: url(../../../assets/subsequent_bg.svg);
  }

  .titleContainer {
    display: flex;
    flex-direction: column;
    padding: 20px 16px;

    .titleWrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .text {
      color: var(--Text-grey900, #101010);
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: 0.01px;
      font-family: Inter;
    }

    .subTitle {
      margin-top: 2px;
      color: var(--Text-grey900, #101010);
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
    }
  }

  .companyDetailsWrapper {
    display: flex;
    padding: 16px 16px 12px;
    align-items: center;

    .iconWrapper {
      display: flex;
      width: 36px;
      height: 36px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background: var(--Icon-silverForever, #fff);
      margin-right: 12px;
    }
  }

  .companyDetails {
    display: flex;
    flex-direction: column;

    .companyName {
      overflow: hidden;
      color: var(--Text-grey900, #101010);
      text-overflow: ellipsis;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
    }

    .ltpContainer {
      display: flex;
      align-items: center;
      margin-top: 2px;

      .range {
        color: var(--Text-grey600, rgba(16, 16, 16, 0.7));
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }

  .ctaContainer {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;

    button:first-child {
      margin-right: 12px;
    }

    .greenCta {
      border-radius: 8px;
      background: var(--BG-profitAndSuccess, #21c179);
      height: 44px !important;

      .buttonTextClassName {
        color: var(--Text-silverForever, #fff);
        font-size: 14px !important;
        font-weight: 500 !important;
        line-height: 20px !important;
      }
    }

    .watchListCta {
      border-radius: 8px;
      border: 1px solid var(--Border-grey200, rgba(16, 16, 16, 0.13));
      background: var(--BG-default, #fff);
      width: 100%;
      margin-right: 10px;
      display: flex;
      justify-content: center;
    }

    .ctaCustomClass {
      border-radius: 8px;
      border: 1px solid var(--Border-grey200, rgba(16, 16, 16, 0.13));
      background: var(--BG-default, #fff);
      color: var(--Text-grey900, #101010);
      height: 44px;

      .ctaCustomButtonTextClass {
        color: var(--Text-link, #013da6);
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
      }
    }
  }
}
