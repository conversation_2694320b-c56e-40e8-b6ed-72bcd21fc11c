export const PULSE_STATICS = {
  OPEN_SCREEN_EVENT: 'openScreen',
  CUSTOM_EVENT: 'custom_event',
  ACTION: {
    first_trade: {
      firsttrade_widget: 'firsttrade_widget',
      firsttrade_changestock: 'firsttrade_changestock',
      firsttrade_watchlistclick: 'firsttrade_watchlistclick',
      firsttrade_buyclick: 'firsttrade_buyclick',
      firsttrade_orderpad: 'firsttrade_orderpad',
      firsttrade_investcare: 'firsttrade_investCare_bottom_sheet_displayed',
      firsttrade_InvestCare_error_received:
        'firsttrade_InvestCare_error_received',
      firsttrade_investcare_yes_clicked: 'firsttrade_investcare_yes_clicked',
      firsttrade_investcare_no_clicked: 'firsttrade_investcare_no_clicked',
      firsttrade_order_status: 'firsttrade_order_status',
      firsttrade_land_order_screen: 'firsttrade_land_order_screen',
      firsttrade_select_exchange: 'firsttrade_select_exchange',
      firsttrade_input_qty: 'firsttrade_input_qty',
      firsttrade_charges_link_clicked: 'firsttrade_charges_link_clicked',
      firsttrade_back_click: 'firsttrade_back_click',
      firsttrade_charges_bottomsheet_displayed:
        'firsttrade_charges_bottomsheet_displayed',
      firsttrade_charges_bottomsheet_closed:
        'firsttrade_charges_bottomsheet_closed',
      firsttrade_buy_order_clicked: 'firsttrade_buy_order_clicked',
      firsttrade_insufficient_funds_error_recieved:
        'firsttrade_insufficient_funds_error_recieved',
      firsttrade_add_funds_clicked:
        'firsttrade_insufficient_funds_add_funds_clicked',
      first_trade_order_lotsize_error_received:
        'firsttrade_order_lotsize_error_received',
      first_trade_order_pad_error_received:
        'firsttrade_order_pad_error_received',
      first_trade_Insufficient_fund_bottomsheet_landed:
        'firsttrade_Insufficient_fund_bottomsheet_landed',
      first_trade_Add_fund_clicked: 'firsttrade_Add_fund_clicked',
      first_trade_modify_order_Insufficient_fund_bottomsheet_clicked:
        'firsttrade_modify_order_Insufficient_fund_bottomsheet_clicked',
    },
    subsequent_trade: {
      subsequent_widget: 'subsequent_trade_widget_rendered',
      subsequent_changestock: 'subsequent_changestock',
      subsequent_buyclick: 'subsequent_trade_buy_cta_clicked',
      subsequent_stock_sip_click: 'subsequent_trade_stock_sip_cta_clicked',
      subsequent_orderpad:
        'subsequent_trade_order_pad_entry_from_subsequent_widget',
      subsequent_investcare: 'subsequent_investcare',
      subsequent_order_status: 'subsequent_order_status',
    },
  },
};
