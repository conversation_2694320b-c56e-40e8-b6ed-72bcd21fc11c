import { useMemo } from 'react';
import cx from 'classnames';
import { useGetMarginPerc } from '@src/query/stockQuery';
import styles from '../FundsCard/index.scss';
import CompanyIcon from '../../../../atoms/CompanyIcon/CompanyIcon';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../../../../atoms/CompanyIcon/IconsList';
import { isDarkMode } from '../../../../../utils/commonUtil';
import StockPriceChange from '../../../../molecules/StockPriceChange';
import Button from '../../../../atoms/Button/Button';
import { BACKGROUND_IMAGES, event_type } from '../../enums';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import { PRODUCT_TYPES } from '../../../../../utils/Equities/enum';
import { useStockAndInstrumentFeed } from '../../../../../utils/Equities/hooks';

const OrderCard = ({
  reminder,
  companyPageNavigation,
  buttons,
  onClickHandler,
  showCloseIcon,
  handleRemoveReminder,
  isFullPage,
  analyticEvents,
  index,
  totalRemindersInView,
}) => {
  const { ltp, pClose } = useStockAndInstrumentFeed({
    exchange: reminder.exchange,
    segment: reminder.segment,
    securityId: reminder.sec_id,
    instrumentType: reminder.inst_type,
    id: reminder.pml_id,
  });

  const rangeValue = useMemo(() => {
    if (pClose && ltp) {
      return ((ltp - pClose) / Math.abs(pClose)) * 100;
    }
    return null;
  }, [ltp, pClose]);
  const isRejected = reminder.event_type === event_type.order.FAILED_ORDERS;

  const getCtaClassName = (btn) =>
    cx(styles.cta, {
      [styles.greenButton]: btn?.cta?.toLowerCase?.()?.includes('buy'),
    });

  const getTitleClassName = () =>
    cx(styles.title, {
      [styles.rejectedText]: isRejected,
    });

  const getCardClassName = () =>
    cx(styles.card, {
      [styles.firstFullPageCard]: isFullPage && index === 0,
      [styles.partialCard]: !isFullPage,
      [styles.lastCard]: !isFullPage && index === totalRemindersInView - 1,
    });

  const { data } = useGetMarginPerc({
    query: {
      exchange: reminder.exchange,
      scrip_id: reminder.sec_id,
    },
    isEnabled: reminder.product === PRODUCT_TYPES.MTF,
  });

  const getBackgroundImage = () => {
    const mode = isDarkMode() ? 'dark' : 'light';
    const imageConfig =
      BACKGROUND_IMAGES[reminder.event_type] || BACKGROUND_IMAGES.default;
    return `url(${imageConfig[mode]})`;
  };

  return (
    <div
      className={getCardClassName()}
      style={{ backgroundImage: getBackgroundImage() }}
      key={reminder.id}
      onClick={() => {
        analyticEvents.onWidgetClick({
          isFullPage,
          label: reminder.event_type,
          label2: reminder.stock_name,
        });
        companyPageNavigation(reminder);
      }}
    >
      <div className={styles.headerWrapper}>
        <div className={styles.companyDetails}>
          <CompanyIcon
            name={reminder.id}
            type={COMPANY_ICONS_NAME.STOCKS}
            className={styles.companyIcon}
          />
          <div className={styles.stockHeader}>
            <div className={getTitleClassName()}>
              {reminder.title || 'Complete your order'}
            </div>
            <div className={styles.companyName}>
              {reminder.product === PRODUCT_TYPES.MTF && (
                <span className={styles.mtfChip}>MTF {data?.margin_x}x</span>
              )}
              {reminder.stock_name}
            </div>
          </div>
          {showCloseIcon ? (
            <Icon
              name={ICONS_NAME.CLOSE_ICON}
              className={styles.crossIcon}
              size={3.2}
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveReminder(reminder.id);
                analyticEvents.onDismiss({
                  label: reminder.event_type,
                  label2: reminder.stock_name,
                  label3: reminder.event_id,
                });
              }}
            />
          ) : null}
        </div>
        <div className={styles.returnsCtaContainer}>
          <div className={styles.returnsWrapper}>
            <StockPriceChange
              fromComponent="reminder"
              securityId={reminder.sec_id}
              exchange={reminder.exchange}
              segment={reminder.segment}
              change={rangeValue}
              isSymbol={false}
              withRupee={false}
              customClassName={styles.stockPriceChange}
            />
            <div className={styles.returnText}>(1 Day)</div>
          </div>
          {buttons?.map((button) => (
            <Button
              key={button?.cta}
              buttonText={button?.cta}
              className={getCtaClassName(button)}
              onClickHandler={(e) => {
                e.stopPropagation();
                onClickHandler(button, reminder);
              }}
              buttonTextClassName={styles.buttonTextClassName}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default OrderCard;
