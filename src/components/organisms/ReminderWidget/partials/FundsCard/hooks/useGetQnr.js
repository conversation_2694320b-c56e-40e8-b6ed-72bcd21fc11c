import { useMemo, useCallback } from 'react';
import {
  PAYMENT_MODES,
  QUICK_PAYMENT_DATA,
} from '../../../../../../config/paymentConfig';

function useGetQnR(paymentMethods) {
  const getLogo = useCallback(
    (id) =>
      paymentMethods.find((method) => method.paymentMethodId === id)
        ?.paymentMethodImgUrl,
    [paymentMethods],
  );

  return useMemo(() => {
    const qnr = paymentMethods.find(
      (method) => method.paymentMethodId === PAYMENT_MODES.PPBL,
    );

    if (!qnr) return null;

    const isNetBanking =
      qnr?.paymentMethodOptionList?.[0]?.paymentMethodId ===
      PAYMENT_MODES.NET_BANKING;

    const isUPICollect =
      qnr?.paymentMethodOptionList?.[0]?.upiFlow ===
      QUICK_PAYMENT_DATA.UPI_COLLECT;

    if (isNetBanking || isUPICollect) {
      const appUrl = getLogo(
        isUPICollect ? PAYMENT_MODES.UPI : PAYMENT_MODES.NET_BANKING,
      );
      return {
        ...qnr,
        appUrl,
      };
    }
    return qnr;
  }, [paymentMethods, getLogo]);
}

export { useGetQnR };
