import {
  PAYMENT_MODES,
  PAYMENT_UPI_INTENT,
} from '../../../../../../config/paymentConfig';

export function sanitizeAppName(appName) {
  if (!appName) return '';
  const chunk = appName.split(' ');
  if (chunk[chunk.length - 1].toLowerCase() === 'upi') {
    chunk.pop();
  }
  return chunk.join(' ');
}

function getMaskedValue(value) {
  if (typeof value !== 'string' || value.length < 4) {
    return '';
  }
  return value.slice(value.length - 4);
}

export function getIntentAppName(appName) {
  const appsNameConfig = {
    gpay: 'Google Pay',
    paytm: 'Paytm',
  };

  const app = appName.toLowerCase();
  if (appsNameConfig[app]) return appsNameConfig[app];
  return appName;
}

function useRepeatedUserConfig({
  paymentMethodOption,
  isRepeatedUser,
  selectedQNRPspApp,
}) {
  if (!paymentMethodOption) {
    return {};
  }
  switch (true) {
    case paymentMethodOption?.paymentMethodId === PAYMENT_MODES.NET_BANKING: {
      return {
        name: 'Net Banking',
        logo: paymentMethodOption?.bankImgSrc,
        displayName: paymentMethodOption?.displayName,
        accountNo: getMaskedValue(paymentMethodOption?.bankAccountNumber),
        appLogo: isRepeatedUser?.appUrl,
      };
    }
    case paymentMethodOption?.upiFlow === PAYMENT_UPI_INTENT.UPI_COLLECT: {
      return {
        name: paymentMethodOption?.upiDetails?.collect?.lastUsedVpa,
        logo: paymentMethodOption?.bankImgSrc,
        appLogo: isRepeatedUser?.appUrl,
        displayName: paymentMethodOption?.displayName,
        accountNo: getMaskedValue(paymentMethodOption?.bankAccountNumber),
      };
    }
    case paymentMethodOption?.upiFlow === PAYMENT_UPI_INTENT.UPI_INTENT: {
      return {
        name: getIntentAppName(
          sanitizeAppName(paymentMethodOption?.upiDetails?.intent?.name),
        ),
        appLogo:
          selectedQNRPspApp?.logoUrl ||
          paymentMethodOption?.upiDetails?.intent?.iconUrl,
        logo: paymentMethodOption?.bankImgSrc,
        displayName: paymentMethodOption?.displayName,
        accountNo: getMaskedValue(paymentMethodOption?.bankAccountNumber),
      };
    }
    default: {
      return {
        name: '',
        logo: paymentMethodOption?.bankImgSrc,
        appLogo: paymentMethodOption?.bankImgSrc,
        displayName: paymentMethodOption?.displayName,
        accountNo: getMaskedValue(paymentMethodOption?.bankAccountNumber),
      };
    }
  }
}

export { useRepeatedUserConfig };
