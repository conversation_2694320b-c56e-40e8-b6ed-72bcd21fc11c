import { PRODUCT_TYPES } from '../../../utils/Equities/enum';

export const formatReminders = (data) => {
  const { data: { meta, widget } = {} } = data;
  const attributes = widget?.attributes || [];

  const attributesMap = attributes.reduce((acc, attr) => {
    acc[attr.name] = attr.value;
    return acc;
  }, {});

  const popularStocksList = (attributesMap.REMINDER_DATA || []).map(
    (stock) => ({
      ...stock,
      id: stock.pml_id || '',
      name: stock.name || '',
      exchange: stock.exchange || '',
      segment: stock.segment || '',
      inst_type: stock.instrument_type || '',
      sec_id: stock.security_id || '',
      product: stock.product || PRODUCT_TYPES.DELIVERY,
    }),
  );

  return {
    data: {
      reminders: popularStocksList,
      title: attributesMap.TITLE_REMINDER_WIDGET,
      closeButton: attributesMap.CROSS_BUTTON,
      viewAll: attributesMap.BUTTON_VIEW_ALL,
      buttons: attributesMap.BUTTON_ACTION_MAPPING,
      ...meta,
    },
  };
};

export const getCurrentDayName = () => {
  const days = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];

  const dayName = days[new Date().getDay()];
  return dayName;
};
