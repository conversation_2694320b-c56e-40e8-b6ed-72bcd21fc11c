@import '~@paytm-h5-common/paytm_common_ui/styles/colors';
@import '~@paytm-money/utils-frontend/styles/pml-typography';
@import '~@paytm-money/utils-frontend/styles/pml-mixins';

.headerTitle {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: 4px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-neutral-strong);
}

.backButton {
  border: 1px solid var(--backgrounds-offset1);
  border-radius: 50%;
  padding: 10px;
}

.fullPageHeader {
  margin: 0;
}

.fullPageContainer {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-default2);
  z-index: 999;

  .mainContainer {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 12px;
  }
}

.isFullPage {
  margin: 15px;
}

.mainContainer {
  display: flex;
  flex-direction: column;
  
  .header {
    display: flex;
    justify-content: space-between;
    gap: 4px;
    align-items: start;
    flex-shrink: 0;
    margin: 0 15px;
  
    .text {
      .title {
        font-size: 14px;
        color: var(--text-neutral-strong);
        font-weight: 500;
      }
    }
  }
  .viewAllCard {
    min-width: max-content;
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 14px;
    color: var(--text-primary-strong);
    font-weight: 500;
  }
}

.crossIcon {
  margin-right: 16px;
}

.bannersCarousel {
  // bottom: 6px !important;
  position: relative !important;
  bottom: 0 !important;
}
