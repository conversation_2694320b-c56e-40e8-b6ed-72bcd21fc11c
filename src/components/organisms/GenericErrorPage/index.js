/* eslint-disable no-undef */
import { useEffect } from 'react';
import { useRouteError } from 'react-router-dom';

import useAppNavigate from '../../../hooks/useAppNavigate';

// components
import HeaderWrapper from '../../molecules/HeaderWrapper';

// utils, constants, enums, config
import { exitApp, getStartupParamsAllCallback, notifyNativeApp } from '../../../utils/bridgeUtils';
import { sendErrorToBackend } from '../../../actions/runtime';
import { EMPTY_SCREEN, GENERIC_ERROR_MSG } from '../../../utils/constants';
import SomethingWentWrong from '../../../assets/errorScreenIcon.png';
import NoInternetIcon from '../../../assets/noInternetMini.png';

import styles from './index.scss';

const GenericErrorPage = ({
  cardHeightView,
  errorBoundary = false,
  icon = '',
  message = '',
  errorMsg = '',
  onBackPress,
  noInternet = false,
  noIcon = false,
  heightInViewport = false,
  isWidget = true,
}) => {
  const error = useRouteError();
  const { appNavigate } = useAppNavigate();

  const cardHeight = cardHeightView || window.innerHeight - 95;
  const isProd = __BUILD__ === 'prod';

  const handleBackPress = () => {
    if (typeof onBackPress === 'function') {
      onBackPress();
    } else if (window.location.pathname === '/') {
      exitApp();
    } else {
      // Navigate back
      appNavigate(-1);
    }
  };

  useEffect(() => {
    if (isProd) {
      sendErrorToBackend({
        data: {
          data: error.stack,
        },
        level: 'fatal',
        key: 'fatal_error',
        page: window.location.href,
      });
    }
    if (!isWidget) {
      getStartupParamsAllCallback((result) => {
        if (result?.nativeData) {
          const nativeData = JSON.parse(result.nativeData);
          notifyNativeApp({
            flowType: 'removeH5FragmentCombinedHome',
            widgetId: nativeData.widgetType,
          });
        }
      });
    }
  }, [error.stack, isProd, isWidget]);

  if (!isWidget) return null;
  return (
    <>
      {errorBoundary && <HeaderWrapper onBackClick={handleBackPress} />}
      <div
        className={styles.root}
        style={{ height: heightInViewport ? cardHeight : `${cardHeight}px` }}
      >
        {!noIcon && (
          <img
            src={icon || (noInternet ? NoInternetIcon : SomethingWentWrong)}
            alt=""
            className={styles.icon}
          />
        )}
        <div className={styles.message}>{message || GENERIC_ERROR_MSG}</div>
        {error || errorMsg ? (
          <div className={styles.subMessage}>{error?.message || errorMsg}</div>
        ) : (
          <div className={styles.subMessage}>
            {noInternet ? EMPTY_SCREEN.NO_INTERNET.SUB_MESSAGE : null}
          </div>
        )}
        <div />
        {noInternet && (
          <div className={styles.retry}>{EMPTY_SCREEN.NO_INTERNET.RETRY}</div>
        )}
      </div>
    </>
  );
};
export default GenericErrorPage;
