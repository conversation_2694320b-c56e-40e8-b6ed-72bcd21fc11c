import cx from 'classnames';
import styles from './FullScreenModal.module.scss';
import Icon, { ICONS_NAME } from '../../molecules/Icon';
import { isDarkMode } from '../../../utils/commonUtil';

function FullScreenModal({ isOpen, onClose, title, subtitle, children }) {
  if (!isOpen) {
    return null;
  }

  return (
    <div
      className={cx(styles['full-screen-overlay'], {
        [styles.darkMode]: isDarkMode(),
      })}
    >
      <div className={styles['overlay-header']}>
        <div className={styles['overlay-title-container']}>
          <h2
            className={cx(styles['overlay-title'], {
              [styles.darkMode]: isDarkMode(),
            })}
          >
            {title}
          </h2>
          <p
            className={cx(styles['overlay-subtitle'], {
              [styles.darkMode]: isDarkMode(),
            })}
          >
            {subtitle}
          </p>
        </div>
        <Icon
          name={ICONS_NAME.CLOSE}
          className={styles['overlay-close-icon']}
          size={5}
          onClick={onClose}
        />
      </div>

      <div className={styles['overlay-scrollable-content']}>{children}</div>
    </div>
  );
}

export default FullScreenModal;
