import { useCallback, useEffect, useRef, useState } from 'react';
import cx from 'classnames';
import styles from './index.scss';
import { isDarkMode } from '../../../utils/commonUtil';
import StockChange from '../StockChange/StockChange';
import { TEXT_DATA } from '../../../pages/ETFCard/utils';
import CompanyIcon from '../../atoms/CompanyIcon/CompanyIcon';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../../atoms/CompanyIcon/IconsList';
import { Percent } from '../../molecules/Prices';
import { useStockAndInstrumentFeed } from '../../../utils/Equities/hooks';
import { useETFAnalyticsEvent } from '../../../hooks/useETFAnalyticsEvent';
import { PULSE_STATICS } from '../../../pages/ETFCard/enums';

const ETFDetails = ({
  showAnimation,
  transitionTime,
  etf,
  showPopupHandler,
  returns,
  widgetData,
  sipTitleCustomClass,
  sipReturnsCustomClass,
  showSipLoaderBg,
  companyPageNavigation = () => {},
}) => {
  const loaderRef = useRef();
  const { sendAnalyticsEventETF } = useETFAnalyticsEvent({
    widgetId: widgetData?.widgetId || '',
    subCohortId: widgetData?.subCohortId || '',
  });
  const [internalShowAnimation, setInternalShowAnimation] = useState(false);
  const { ltp } = useStockAndInstrumentFeed({
    exchange: etf.exchange,
    segment: etf.segment,
    securityId: etf.security_id,
    instrumentType: etf.instrument_type,
    id: etf.id,
  });

  useEffect(() => {
    setInternalShowAnimation(false);
    setTimeout(() => {
      setInternalShowAnimation(showAnimation);
    }, 0);
  }, [showAnimation, etf]);

  const getPercentageReturns = useCallback(
    (pClose) => {
      if (pClose && ltp) {
        return ((ltp - pClose) / Math.abs(pClose)) * 100;
      }
      return null;
    },
    [ltp],
  );

  const handleCompanyPageNavigation = () => {
    companyPageNavigation(etf);
  };

  const etfClicked = () => {
    sendAnalyticsEventETF({
      action: PULSE_STATICS.ACTION.ETF_CLICKED,
      label: etf.name,
    });
  };

  const returnsClicked = () => {
    sendAnalyticsEventETF({
      action: PULSE_STATICS.ACTION.RETURNS_CLICKED,
    });
  };

  return (
    <div className={styles.cardRoot}>
      <div
        className={cx(styles.sipTitle, sipTitleCustomClass)}
        onClick={() => {
          handleCompanyPageNavigation();
          etfClicked();
        }}
      >
        <CompanyIcon
          name={etf.id}
          type={COMPANY_ICONS_NAME.STOCKS}
          className={styles.companyIcon}
        />
        <div className={styles.stockDetails}>
          <div className={styles.sipDetails}>
            <div className={styles.sipName}>{etf.name}</div>
            <StockChange
              exchange={etf.exchange}
              segment={etf.segment}
              securityId={etf.security_id}
              instrumentType={etf.instrument_type}
              id={etf.id}
            />
          </div>
          <div
            className={styles.viewAllCta}
            onClick={(e) => {
              e.stopPropagation();
              showPopupHandler(true);
            }}
          >
            {widgetData?.viewAll?.cta || TEXT_DATA.VIEW_ALL}
          </div>
        </div>
        {showAnimation && internalShowAnimation ? (
          <div
            className={cx(styles.sipLoaderContainer, {
              [styles.sipLoaderContainer]: showSipLoaderBg,
              [styles.sipLoaderContainerDark]: showSipLoaderBg && isDarkMode(),
            })}
          >
            <div
              ref={loaderRef}
              className={cx(styles.sipLoader, {
                [styles.sipLoaderDark]: isDarkMode(),
              })}
              style={{ animationDuration: `${transitionTime - 0.1}s` }}
            />
          </div>
        ) : (
          <div
            className={cx({
              [styles.sipLoaderContainer]: showSipLoaderBg,
              [styles.sipLoaderContainerLine]: showSipLoaderBg,
              [styles.sipLoaderContainerDark]: showSipLoaderBg && isDarkMode(),
            })}
          />
        )}
      </div>
      <div
        className={cx(styles.sipReturns, {
          [styles.sipReturnsBg]: !sipReturnsCustomClass,
          [sipReturnsCustomClass]: sipReturnsCustomClass,
        })}
        onClick={() => {
          handleCompanyPageNavigation();
          returnsClicked();
        }}
      >
        {returns.map((ele) =>
          etf?.[ele.key] ? (
            <div className={styles.returnsWrapper} key={ele.title}>
              <div>{ele.title}</div>
              <Percent
                value={getPercentageReturns(etf?.[ele.key])}
                className={styles.value}
                withSign
              />
            </div>
          ) : null,
        )}
      </div>
    </div>
  );
};

export default ETFDetails;
