@import '/src/commonStyles/variables.scss';

.cardRoot {
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    width: 90%;
    overflow: hidden;
    background: var(--etf-card-details-bg);
}

.sipTitle {
  position: relative;
  padding: 12px;
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
}

.viewAllCta {
  @include typography(body2B2, var(--text-primary-strong));
}

.stockDetails {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.sipDetails {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  width: 80%;

  .sipName {
    @include typography(body2B2, var(--text-neutral-strong));
    max-width: 100%;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    white-space: normal;
  }
}

.sipReturns {
  min-height: 60px;
  backdrop-filter: blur(134px);
  display: flex;
  justify-content: space-around;
  @include typography(body2R4, var(--text-neutral-strong));
}

.sipReturnsBg {
  background: var(--etf-returns-bg);
}

.sipLoaderContainer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 2px;
  background-color: #10101021;
}

.sipLoaderContainerLine {
  height: 1px;
}

.sipLoaderContainerDark {
  background-color: #EEEEEE3D;
}

.sipLoader {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 0;
  height: 2px;
  animation: loading linear forwards;
  background: linear-gradient(90deg, #00B8F5 0%, #2F81ED 100%);
}

.sipLoaderDark {
  background: linear-gradient(90deg, #0A86BF 0%, #2361B2 100%);
}

@keyframes loading {
  0% {
    width: 0%;
    background-size: 200% 100%;
  }
  40% {
    background-size: 200% 100%;
  }
  50% {
    background-size: 200% 100%;
  }
  75% {
    background-size: 200% 100%;
  }
  100% {
    width: 100%;
    background-size: 200% 100%;
  }
}

.returnsWrapper {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}
