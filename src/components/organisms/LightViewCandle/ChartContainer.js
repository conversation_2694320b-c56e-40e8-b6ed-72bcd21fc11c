import { memo } from 'react';
import DEFAULT_CHART_LIGHT from '../../../assets/icons/default-light-chart.png';
import DEFAULT_CHART_LIGHT_DARK from '../../../assets/icons/default-light-chart-dark.png';
import styles from './index.scss';
import { isDarkMode } from '../../../utils/commonUtil';

export const ChartContainer = memo((props) => {
  const {
    symbol = {},
    companyPageNavigation,
    error,
    loading,
    chartData,
    chartContainerRef,
    chartClickedEvent,
    allowClick = false,
  } = props;

  if (error || loading || (!loading && !chartData.length)) {
    return (
      <div className={styles.imgWrapper}>
        <img
          className={styles.img}
          src={isDarkMode() ? DEFAULT_CHART_LIGHT_DARK : DEFAULT_CHART_LIGHT}
          alt="fallbackChart"
        />
      </div>
    );
  }

  return (
    <div
      onClick={() => {
        if (chartClickedEvent) chartClickedEvent();
        if (companyPageNavigation) companyPageNavigation(symbol);
      }}
      className={styles.chartContainer}
    >
      <div
        ref={chartContainerRef}
        className={styles.chartWrapper}
        style={!allowClick ? { pointerEvents: 'none' } : {}}
      />
    </div>
  );
});

export default ChartContainer;
