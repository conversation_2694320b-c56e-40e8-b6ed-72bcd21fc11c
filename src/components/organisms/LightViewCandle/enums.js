const lightTheme = {
  layout: {
    textColor: 'black',
    background: {
      type: 'solid',
      color: 'white',
    },
  },
  timeScale: {
    borderVisible: false,
    timeVisible: true,
    shiftVisibleRangeOnNewBar: true,
  },
  rightPriceScale: {
    borderVisible: false,
    visible: false,
  },
  grid: {
    horzLines: {
      visible: false,
    },
    vertLines: {
      visible: false,
    },
  },
  crosshair: {
    horzLine: {
      visible: false,
      labelVisible: false,
    },
    vertLine: {
      visible: true,
      style: 0,
      labelVisible: false,
    },
  },
  //   handleScale: {
  //     mouseWheel: false,
  //     pinch: false,
  //     axisPressedMouseMove: false,
  //     axisDoubleClickReset: false,
  //   },
  //   handleScroll: {
  //     mouseWheel: false,
  //     pressedMouseMove: false,
  //     horzTouchDrag: false,
  //     vertTouchDrag: false,
  //   },
  watermark: {
    visible: false,
  },
};

const darkTheme = {
  layout: {
    background: { color: '#1C1C1E' },
    lineColor: '#2B2B43',
    textColor: '#D9D9D9',
  },
  timeScale: {
    borderVisible: false,
    timeVisible: true,
    barSpacing: 3,
    shiftVisibleRangeOnNewBar: true,
  },
  rightPriceScale: {
    borderVisible: false,
    visible: false,
  },
  grid: {
    horzLines: {
      visible: false,
    },
    vertLines: {
      visible: false,
    },
  },
  crosshair: {
    horzLine: {
      visible: false,
      labelVisible: false,
    },
    vertLine: {
      visible: true,
      style: 0,
      labelVisible: false,
      color: '#758696',
    },
  },
  handleScale: {
    mouseWheel: false,
    pinch: false,
    axisPressedMouseMove: false,
    axisDoubleClickReset: false,
  },
  handleScroll: {
    mouseWheel: false,
    pressedMouseMove: false,
    horzTouchDrag: false,
    vertTouchDrag: false,
  },
  watermark: {
    color: 'rgba(0, 0, 0, 0)',
  },
};

const CHART_PROPERTIES = (theme) => {
  if (theme === 'dark') return darkTheme;
  return lightTheme;
};

const PERIOD_CONSTANTS = {
  firstValueInRangeDays: {
    '1d': 0,
    '1w': 7,
    '1m': 31,
    '3m': 91,
    '1y': 365,
    '3y': 1095,
    '5y': 1825,
  },
  range: {
    '1d': {
      multiplier: 1,
      base: 'today',
      padding: 10,
      periodicity: {
        interval: 1,
        period: 5,
        timeUnit: 'minute',
      },
    },
    '1w': {
      multiplier: 5,
      base: 'day',
      padding: 10,
      periodicity: {
        interval: 1,
        period: 30,
        timeUnit: 'minute',
      },
    },
    '1m': {
      multiplier: 1,
      base: 'month',
      padding: 10,
      periodicity: {
        interval: 1,
        period: 1,
        timeUnit: 'day',
      },
    },
    '3m': {
      multiplier: 3,
      base: 'month',
      padding: 10,
      periodicity: {
        interval: 1,
        period: 1,
        timeUnit: 'day',
      },
    },
    '6m': {
      multiplier: 6,
      base: 'month',
      padding: 10,
      periodicity: {
        interval: 1,
        period: 1,
        timeUnit: 'week',
      },
    },
    '1y': {
      multiplier: 1,
      base: 'year',
      padding: 10,
      periodicity: {
        interval: 1,
        period: 1,
        timeUnit: 'week',
      },
    },
    '3y': {
      multiplier: 3,
      base: 'year',
      padding: 10,
      periodicity: {
        interval: 1,
        period: 1,
        timeUnit: 'month',
      },
    },
    '5y': {
      multiplier: 5,
      base: 'year',
      padding: 10,
      periodicity: {
        interval: 1,
        period: 1,
        timeUnit: 'month',
      },
    },
  },
};

const SERIES_OPTIONS = {
  priceLineVisible: false,
  lineWidth: 2,
  lastPriceAnimation: 2,
};

const SESSION_TIMES = {
  start: { hour: 9, minutes: 15 },
  end: { hour: 15, minutes: 30 },
};

const PRE_OPEN_SESSION_TIME = { hour: 9, minutes: 0 };

const SERIES_MARKER_CONFIG = {
  shape: 'arrowUp',
};

export {
  CHART_PROPERTIES,
  PERIOD_CONSTANTS,
  SERIES_OPTIONS,
  SESSION_TIMES,
  PRE_OPEN_SESSION_TIME,
  SERIES_MARKER_CONFIG,
};
