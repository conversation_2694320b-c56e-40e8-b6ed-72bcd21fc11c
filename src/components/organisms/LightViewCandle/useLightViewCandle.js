import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { CandlestickSeries, createChart } from 'lightweight-charts';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { getPriceChart } from '../../../actions/chartAction';
import {
  getSeriesColor,
  periodRangeCalculator,
  transformData,
  updateLastTradedPrice,
  calculateStartDate,
  isPostSession,
} from './utils';
import { SERIES_OPTIONS } from './enums';
import { useStockAndInstrumentFeed } from '../../../utils/Equities/hooks';
import { isDarkMode } from '../../../utils/commonUtil';
import { THEMES } from '../../../utils/enums';

dayjs.extend(customParseFormat);

const useLightViewCandle = ({
  symbol,
  range,
  chartContainerRef,
  isTransparent,
  chartConfig = null,
  show24hrChart = false,
}) => {
  const [chartData, setChartData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [seriesConfig, setSeriesConfig] = useState({});
  const chartRef = useRef(null);
  const theme = isDarkMode() ? THEMES.DARK : THEMES.LIGHT;
  const [firstValueInRange, setFirstValueInRange] = useState(null);
  const [chartSeries, setChartSeries] = useState();

  const chartResizeObserver = useCallback(() => {
    if (!chartContainerRef.current) return null;

    const resizeObserver = new ResizeObserver((entries) => {
      if (
        entries.length === 0 ||
        entries[0].target !== chartContainerRef.current
      ) {
        return;
      }
      const newRect = entries[0].contentRect;
      if (chartRef.current) {
        chartRef.current.applyOptions({
          height: newRect.height || 100,
          width: newRect.width || 272,
        });
        chartRef.current.timeScale().fitContent();
      }
    });

    resizeObserver.observe(chartContainerRef.current);
    return resizeObserver;
  }, [chartContainerRef]);

  useEffect(() => {
    if (
      !error &&
      !loading &&
      chartContainerRef.current &&
      chartData &&
      chartData.length > 0
    ) {
      const defaultConfig = {
        layout: {
          attributionLogo: false,
          background: isTransparent
            ? { color: 'transparent' }
            : theme === THEMES.DARK
              ? { color: '#1a1a1a' }
              : { color: '#ffffff' },
          textColor: theme === THEMES.DARK ? '#EEEEEE8A' : '#1010108A',
          fontSize: 11,
        },
        rightPriceScale: {
          visible: true,
          borderVisible: true,
          borderColor: theme === THEMES.DARK ? '#EEEEEE3D' : '#10101021',
          autoScale: true,
          mode: 0,
          alignLabels: true,
        },
        timeScale: {
          visible: true,
          borderVisible: true,
          timeVisible: true,
          secondsVisible: false,
          allowBoldLabels: false,
          borderColor: theme === THEMES.DARK ? '#EEEEEE3D' : '#10101021',
          tickMarkFormatter: (time) => {
            const date = new Date(time * 1000);
            if (range === '1d') {
              return date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false,
              });
            }
            return date.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
            });
          },
          rightOffset: 12,
          leftOffset: 12,
          fixLeftEdge: true,
          fixRightEdge: isPostSession(dayjs()),
        },
        grid: {
          vertLines: {
            visible: false,
          },
          horzLines: {
            visible: false,
          },
        },
        crosshair: {
          mode: 2,
          vertLine: {
            visible: false,
            labelVisible: false,
          },
          horzLine: {
            visible: false,
            labelVisible: false,
          },
        },
        handleScroll: {
          mouseWheel: true,
          pressedMouseMove: true,
          horzTouchDrag: true,
          vertTouchDrag: true,
        },
      };

      chartRef.current = createChart(
        chartContainerRef.current,
        chartConfig || defaultConfig,
      );
      const resizeObserver = chartResizeObserver();

      const newSeries = chartRef.current.addSeries(CandlestickSeries, {
        upColor: theme === THEMES.DARK ? '#02A85D' : '#21C179',
        downColor: theme === THEMES.DARK ? '#d23d50' : '#d23d50',
        borderVisible: false,
        wickUpColor: theme === THEMES.DARK ? '#02A85D' : '#21C179',
        wickDownColor: theme === THEMES.DARK ? '#d23d50' : '#d23d50',
        ...SERIES_OPTIONS,
        priceLineVisible: true,
        lastValueVisible: true,
        priceFormat: {
          type: 'price',
          precision: 2,
          minMove: 0.01,
        },
      });
      setChartSeries(newSeries);
      newSeries.setData(chartData);
      chartRef.current.timeScale().fitContent();

      return () => {
        resizeObserver?.disconnect();
        if (chartRef.current) {
          chartRef.current.remove();
        }
      };
    }
  }, [
    chartContainerRef,
    chartData,
    error,
    loading,
    theme,
    chartResizeObserver,
    isTransparent,
    range,
    chartConfig,
  ]);

  const fetchChartData = useCallback(async () => {
    if (!symbol || !range) return;

    setLoading(true);
    setError(null);
    let marketTimer;
    try {
      const interval = ['1w', '1d'].includes(range) ? 'MINUTE' : 'DAY';
      const dateRange = periodRangeCalculator(range);
      const { startDate, newSession, timerId, toDate } = calculateStartDate(
        range,
        fetchChartData,
        show24hrChart,
      );
      marketTimer = timerId;
      const body = {
        fromDate: newSession ? dateRange.startDate : startDate,
        toDate: newSession ? dateRange.endDate : toDate,
        interval,
        pmlId: symbol.id,
      };

      const response = await getPriceChart(body);
      const data = transformData(response.data, range, show24hrChart);
      let transformedData;
      let intradayInd;
      if (range === '1d') {
        transformedData = data.data;
        intradayInd = data.ind;
      } else transformedData = data;
      const colors = getSeriesColor(
        transformedData,
        range,
        response.pc,
        theme,
        intradayInd,
      );
      setFirstValueInRange(
        range === '1d' ? response.pc : transformedData[0].value,
      );
      setSeriesConfig(colors);
      setChartData(transformedData);
    } catch (err) {
      setError(err.message || 'Failed to fetch chart data');
    } finally {
      setLoading(false);
    }

    return () => {
      if (marketTimer) {
        clearTimeout(marketTimer);
      }
    };
  }, [symbol, range, theme, show24hrChart]);

  useEffect(() => {
    fetchChartData();
  }, [fetchChartData]);

  const { ltp, lastTradeTime, pClose } = useStockAndInstrumentFeed({
    id: symbol.id,
    securityId: symbol.security_id,
    exchange: symbol.exchange,
    segment: symbol.segment,
    instrumentType: symbol.instrument_type,
  });

  useEffect(() => {
    if (!chartData || !ltp || !chartRef.current || !chartSeries) return;
    const updatedData = updateLastTradedPrice({
      data: [...chartData],
      tickValue: ltp,
      range,
      chartRef: chartRef.current,
      pClose,
      theme,
      chartSeries,
      show24hrChart,
    });

    if (updatedData?.shifted && show24hrChart) {
      setChartData(updatedData?.data);
    }

    if (updatedData) {
      setSeriesConfig(updatedData.colors);
    }
  }, [
    chartData,
    chartSeries,
    lastTradeTime,
    ltp,
    pClose,
    range,
    theme,
    show24hrChart,
  ]);

  const rangeValue = useMemo(() => {
    if (firstValueInRange && ltp) {
      return ((ltp - firstValueInRange) / Math.abs(firstValueInRange)) * 100;
    }
    return null;
  }, [ltp, firstValueInRange]);

  return {
    chartData,
    loading,
    error,
    seriesConfig,
    chartRef,
    rangeValue,
  };
};

export default useLightViewCandle;
