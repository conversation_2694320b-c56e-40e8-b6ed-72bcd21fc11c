import { useRef, memo, useMemo } from 'react';
import ChartContainer from './ChartContainer';
import useLightViewCandle from './useLightViewCandle';

const LightViewCandle = memo(
  ({ symbol, range, isTransparent, show24hrChart }) => {
    const chartContainerRef = useRef();

    // Memoize the props object passed to useLightViewCharts
    const chartProps = useMemo(
      () => ({
        symbol,
        range,
      }),
      [symbol, range],
    ); // Only depend on essential values

    const { error, loading, chartData } = useLightViewCandle({
      ...chartProps,
      chartContainerRef,
      isTransparent,
      showHighLow: true,
      show24hrChart,
    });

    // Memoize the props passed to LightViewCharts
    const lightViewProps = useMemo(
      () => ({
        symbol,
        error,
        loading,
        chartData,
        chartContainerRef,
      }),
      [symbol, error, loading, chartData],
    );

    return <ChartContainer {...lightViewProps} />;
  },
  (prevProps, nextProps) =>
    // Custom comparison function for memo
    prevProps.symbol?.id === nextProps.symbol?.id &&
    prevProps.range === nextProps.range,
);

export default LightViewCandle;
