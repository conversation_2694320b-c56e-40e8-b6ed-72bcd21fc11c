const getToolTipElement = (ref) => {
  const pointRef = ref;
  // Create tooltip element
  pointRef.current = document.createElement('div');
  pointRef.current.style.position = 'absolute';
  pointRef.current.style.display = 'none';
  pointRef.current.style.padding = '4px';
  pointRef.current.style.borderRadius = '4px';
  pointRef.current.style.fontSize = '12px';
  pointRef.current.style.pointerEvents = 'none';
  pointRef.current.style.zIndex = '1000';
  return pointRef;
};

const getLowStyles = (ref, theme) => {
  const lowRef = getToolTipElement(ref);
  const color = theme === 'dark' ? '#d23d50' : '#d23d50';
  lowRef.current.style.border = `1px solid ${color}`;
  lowRef.current.style.color = color;
  return lowRef;
};

const getHighStyles = (ref, theme) => {
  const highRef = getToolTipElement(ref);
  const color = theme === 'dark' ? '#02A85D' : '#21C179';
  highRef.current.style.border = `1px solid ${color}`;
  highRef.current.style.color = color;
  return highRef;
};

const createSvgLine = (ref) => {
  const svgRef = ref;
  svgRef.current = document.createElementNS(
    'http://www.w3.org/2000/svg',
    'svg',
  );
  svgRef.current.style.position = 'absolute';
  svgRef.current.style.top = '0';
  svgRef.current.style.left = '0';
  svgRef.current.style.width = '100%';
  svgRef.current.style.height = '100%';
  svgRef.current.style.pointerEvents = 'none';
  svgRef.current.style.zIndex = '999';

  const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
  line.setAttribute('stroke-width', '1');
  svgRef.current.appendChild(line);
  const circle = document.createElementNS(
    'http://www.w3.org/2000/svg',
    'circle',
  );
  circle.setAttribute('r', 3);
  svgRef.current.appendChild(circle);
  return svgRef;
};

export { getHighStyles, getLowStyles, createSvgLine };
