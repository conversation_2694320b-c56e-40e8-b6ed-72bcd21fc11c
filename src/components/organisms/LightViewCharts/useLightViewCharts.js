import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { AreaSeries, createChart } from 'lightweight-charts';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { getPriceChart } from '../../../actions/chartAction';
import {
  getSeriesColor,
  periodRangeCalculator,
  transformData,
  updateLastTradedPrice,
  calculateStartDate,
} from './utils';
import { SERIES_OPTIONS } from './enums';
import { useStockAndInstrumentFeed } from '../../../utils/Equities/hooks';
import { isDarkMode } from '../../../utils/commonUtil';
import { THEMES } from '../../../utils/enums';
import { getHighStyles, getLowStyles, createSvgLine } from './tooltip-utils';
import ROUTES from '../../../routes';

dayjs.extend(customParseFormat);

const toolTipWidth = 80;
const toolTipLeft = 20;

const LINE_COLOR_HIGH = isDarkMode() ? '#02A85D' : '#21C179';
const LINE_COLOR_LOW = isDarkMode() ? '#d23d50' : '#d23d50';

const BORDER_RADIUS = 2;

const useLightViewCharts = ({
  symbol,
  range,
  chartContainerRef,
  showHighLow = false,
  isTransparent,
  chartConfig = null,
  height = null,
  show24hrChart = false,
  widgetRoute,
}) => {
  const [chartData, setChartData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [seriesConfig, setSeriesConfig] = useState({});
  const chartRef = useRef(null);
  const theme = isDarkMode() ? THEMES.DARK : THEMES.LIGHT;
  const [firstValueInRange, setFirstValueInRange] = useState(null);
  const [highValueInRange, setHighValueInRange] = useState(null);
  const [lowValueInRange, setLowValueInRange] = useState(null);

  const [chartSeries, setChartSeries] = useState();
  const [isCoordinateReady, setIsCoordinateReady] = useState(false);

  const highPoint = useRef(null);
  const lowPoint = useRef(null);
  const highLine = useRef(null);
  const lowLine = useRef(null);

  const chartResizeObserver = useCallback(() => {
    if (!chartContainerRef.current) return null;

    const resizeObserver = new ResizeObserver((entries) => {
      if (
        entries.length === 0 ||
        entries[0].target !== chartContainerRef.current
      ) {
        return;
      }
      const newRect = entries[0].contentRect;
      if (chartRef.current) {
        chartRef.current.applyOptions({
          height: height || newRect.height || 100,
          width: newRect.width || 272,
        });
        chartRef.current.timeScale().fitContent();
      }
    });

    resizeObserver.observe(chartContainerRef.current);
    return resizeObserver;
  }, [chartContainerRef, height]);

  const timeScaleChangeHandler = (timeRange) => {
    console.log('timeRange', timeRange);
    if (timeRange.from === 0) {
      setIsCoordinateReady(true);
    } else {
      setIsCoordinateReady(false);
    }
  };

  useEffect(() => {
    if (
      !error &&
      !loading &&
      chartContainerRef.current &&
      chartData &&
      chartData.length > 0
    ) {
      const ref1 = getHighStyles(highPoint, theme);
      const ref2 = getLowStyles(lowPoint, theme);
      const svgHighLine = createSvgLine(highLine);
      const svgLowLine = createSvgLine(lowLine);

      chartContainerRef.current.appendChild(ref1.current);
      chartContainerRef.current.appendChild(ref2.current);
      if (showHighLow) {
        chartContainerRef.current.appendChild(svgHighLine.current);
        chartContainerRef.current.appendChild(svgLowLine.current);
      }

      const defaultConfig = {
        layout: {
          attributionLogo: false,
          background: isTransparent
            ? { color: 'transparent' }
            : theme === THEMES.DARK
              ? { color: '#1a1a1a' }
              : { color: '#ffffff' },
        },
        rightPriceScale: {
          visible: false,
        },
        timeScale: {
          visible: false,
        },
        grid: {
          vertLines: {
            visible: false,
          },
          horzLines: {
            visible: false,
          },
        },
        crosshair: {
          mode: 2,
          vertLine: {
            visible: false,
            labelVisible: false,
          },
          horzLine: {
            visible: false,
            labelVisible: false,
          },
        },
        handleScroll: {
          mouseWheel: false,
          pressedMouseMove: false,
          horzTouchDrag: false,
          vertTouchDrag: false,
        },
      };

      chartRef.current = createChart(
        chartContainerRef.current,
        chartConfig || defaultConfig,
      );
      const resizeObserver = chartResizeObserver();

      const newSeries = chartRef.current.addSeries(AreaSeries, {
        lineColor: seriesConfig.color,
        topColor: seriesConfig.color,
        bottomColor: seriesConfig.shade,
        ...SERIES_OPTIONS,
        priceLineVisible: false,
        lastValueVisible: false,
        priceFormat: {
          type: 'price',
          precision: 2,
          minMove: 0.01,
        },
      });
      setChartSeries(newSeries);
      newSeries.setData(chartData);

      // Add vertical line for specific time
      if (show24hrChart && range === '1d') {
        const firstTimestamp = chartData[0]?.time;
        const lastTimestamp = chartData[chartData.length - 1]?.time;

        let markerTime;
        let firstDate;
        let lastDate;
        if (firstTimestamp && lastTimestamp) {
          firstDate = dayjs.unix(firstTimestamp);
          lastDate = dayjs.unix(lastTimestamp);
          console.log(
            'firstDate lastDate',
            firstDate.format('YYYY-MM-DD'),
            lastDate.format('YYYY-MM-DD'),
          );

          if (
            firstDate.format('YYYY-MM-DD') !== lastDate.format('YYYY-MM-DD')
          ) {
            const currentDayData = chartData.find((point) => {
              const pointDate = dayjs.unix(point.time);
              return (
                pointDate.format('YYYY-MM-DD') === lastDate.format('YYYY-MM-DD')
              );
            });

            if (currentDayData) {
              markerTime = currentDayData.time;
            }
          }
        }

        if (markerTime) {
          const verticalLine = document.createElement('div');
          verticalLine.style.position = 'absolute';
          verticalLine.style.width = '2px';
          verticalLine.style.backgroundColor =
            theme === THEMES.DARK ? '#10101013' : '#10101013';
          verticalLine.style.top = '0';
          verticalLine.style.bottom = '14px';
          verticalLine.style.pointerEvents = 'none';
          verticalLine.style.zIndex = '1';

          // Create date label element
          const dateLabel = document.createElement('div');
          dateLabel.style.position = 'absolute';
          dateLabel.style.bottom =
            widgetRoute === ROUTES.FO_INDEX_ANALYSIS ? '26px' : '0';
          dateLabel.style.transform = 'translateX(-50%)';
          dateLabel.style.fontSize = '10px';
          dateLabel.style.fontWeight = '500';
          dateLabel.style.lineHeight = '10px';
          dateLabel.style.color =
            theme === THEMES.DARK ? '#10101064' : '#10101064';
          dateLabel.style.pointerEvents = 'none';
          dateLabel.style.zIndex = '1';
          dateLabel.textContent = lastDate.format('DD MMM');

          const updateVerticalLine = () => {
            if (chartRef.current) {
              const x = chartRef.current
                .timeScale()
                .timeToCoordinate(markerTime);
              if (x !== null) {
                verticalLine.style.left = `${x}px`;
                verticalLine.style.display = 'block';
                dateLabel.style.left = `${x}px`;
                dateLabel.style.display = 'block';
              } else {
                verticalLine.style.display = 'none';
                dateLabel.style.display = 'none';
              }
            }
          };

          chartContainerRef.current.appendChild(verticalLine);
          chartContainerRef.current.appendChild(dateLabel);
          chartRef.current
            .timeScale()
            .subscribeVisibleLogicalRangeChange(updateVerticalLine);
          updateVerticalLine();
        }
      }

      chartRef.current.timeScale().fitContent();

      return () => {
        resizeObserver?.disconnect();
        if (chartRef.current) {
          chartRef.current.remove();
        }
        if (highPoint.current) {
          highPoint.current.remove();
        }
        if (lowPoint.current) {
          lowPoint.current.remove();
        }
        if (highLine.current) {
          highLine.current.remove();
        }
        if (lowLine.current) {
          lowLine.current.remove();
        }
      };
    }
  }, [
    chartContainerRef,
    chartData,
    error,
    loading,
    seriesConfig.color,
    seriesConfig.shade,
    theme,
    chartResizeObserver,
    showHighLow,
    isTransparent,
    chartConfig,
    show24hrChart,
    range,
  ]);

  useEffect(() => {
    if (showHighLow) {
      const { highest, lowest } = chartData.reduce(
        (acc, point) => {
          if (!acc.highest || point.value > acc.highest.value) {
            acc.highest = point;
          }
          if (!acc.lowest || point.value < acc.lowest.value) {
            acc.lowest = point;
          }
          return acc;
        },
        { highest: null, lowest: null },
      );
      console.log('Valid High and Low Values: ', lowest, highest);
      setLowValueInRange(lowest);
      setHighValueInRange(highest);
    }
  }, [chartData, showHighLow]);

  useEffect(() => {
    if (chartRef.current && chartData.length > 0 && chartSeries) {
      chartRef.current
        .timeScale()
        .subscribeVisibleLogicalRangeChange(timeScaleChangeHandler);
      return () => {
        chartRef.current
          .timeScale()
          .unsubscribeVisibleLogicalRangeChange(timeScaleChangeHandler);
      };
    }
  }, [chartData, chartSeries]);

  const getDataPointPosition = useCallback(
    (dataPoint) => {
      if (!chartRef.current || !chartSeries) {
        console.log('Chart not ready:', {
          hasChart: !!chartRef.current,
          hasSeries: !!chartSeries,
        });
        return null;
      }

      if (
        !dataPoint ||
        typeof dataPoint.time === 'undefined' ||
        typeof dataPoint.value === 'undefined'
      ) {
        console.log('Invalid dataPoint:', dataPoint);
        return null;
      }

      try {
        const yCoordinate = chartSeries.priceToCoordinate(dataPoint.value);
        const xCoordinate = chartRef.current
          .timeScale()
          .timeToCoordinate(dataPoint.time);

        if (!yCoordinate || !xCoordinate) {
          console.log('Invalid coordinates:', {
            x: xCoordinate,
            y: yCoordinate,
          });
          return null;
        }

        return {
          x: xCoordinate,
          y: yCoordinate,
        };
      } catch (err) {
        console.log('Error calculating coordinates:', err);
        return null;
      }
    },
    [chartSeries],
  );

  const getLeftAndTop = useCallback(
    (point, pointRef, isLow) => {
      if (pointRef && point) {
        const { x, y } = point;
        let left = x + 20;
        let isLeftSide = true;

        if (
          left >
          chartContainerRef.current.clientWidth - pointRef.current.clientWidth
        ) {
          left = x - toolTipLeft - toolTipWidth;
          isLeftSide = false;
        }

        if (isLow) {
          const bottom = 0;
          return { left, bottom, isLeftSide };
        }
        return { left, top: 0, isLeftSide };
      }
    },
    [chartContainerRef],
  );

  const showTooltip = useCallback(
    (position, pointRef, point, isLow) => {
      if (
        !position ||
        !pointRef.current ||
        !chartContainerRef.current ||
        !chartSeries
      ) {
        return;
      }

      // Wait for chart container to stabilize after range changes
      const updateTooltip = () => {
        pointRef.current.style.display = 'block';
        pointRef.current.textContent = `${point.value.toFixed(2)}`;
        const positions = getLeftAndTop(position, pointRef, isLow);

        if (!positions) return;

        pointRef.current.style.left = `${positions.left}px`;

        if (isLow) {
          pointRef.current.style.top = 'auto';
          pointRef.current.style.bottom = `${positions.bottom}px`;
        } else {
          pointRef.current.style.bottom = 'auto';
          pointRef.current.style.top = `${positions.top}px`;
        }

        const lineRef = isLow ? lowLine : highLine;
        if (!lineRef.current) return;

        const line = lineRef.current.querySelector('line');
        const circle = lineRef.current.querySelector('circle');
        if (!line) return;

        const tooltipRect = pointRef.current.getBoundingClientRect();
        const chartRect = chartContainerRef.current.getBoundingClientRect();

        // Get fresh coordinates from the chart
        const currentCoords = chartSeries.priceToCoordinate(point.value);
        if (currentCoords === null) return;

        const x1 = position.x;
        const y1 = currentCoords;

        let x2;
        let y2;

        if (positions.isLeftSide) {
          x2 = positions.left;
          y2 = isLow
            ? tooltipRect.top - chartRect.top + BORDER_RADIUS
            : tooltipRect.bottom - chartRect.top - BORDER_RADIUS;
        } else {
          x2 = positions.left + tooltipRect.width;
          y2 = isLow
            ? tooltipRect.top - chartRect.top + BORDER_RADIUS
            : tooltipRect.bottom - chartRect.top - BORDER_RADIUS;
        }

        if (x1 && y1 && x2 && y2) {
          // Position the circle at the start point
          circle.setAttribute('cx', x1);
          circle.setAttribute('cy', y1);
          circle.setAttribute('fill', isLow ? LINE_COLOR_LOW : LINE_COLOR_HIGH);

          line.setAttribute('x1', x1);
          line.setAttribute('y1', y1);
          line.setAttribute('x2', x2);
          line.setAttribute('y2', y2);
          line.setAttribute('stroke', isLow ? LINE_COLOR_LOW : LINE_COLOR_HIGH);
          lineRef.current.style.display = 'block';
        }
      };

      requestAnimationFrame(updateTooltip);
    },
    [getLeftAndTop, chartContainerRef, chartSeries],
  );

  useEffect(() => {
    if (isCoordinateReady && chartData.length > 0) {
      const lowPosition = getDataPointPosition(lowValueInRange);
      const highPosition = getDataPointPosition(highValueInRange);

      if (
        !lowPosition ||
        !highPosition ||
        lowPosition.x < 0 ||
        highPosition.x < 0 ||
        lowPosition.y < 0 ||
        highPosition.y < 0
      ) {
        console.log(
          'Invalid positions, retrying...',
          lowPosition,
          highPosition,
        );
        return;
      }
      console.log(
        'Valid Position: ',
        lowPosition,
        highPosition,
        lowValueInRange,
        highValueInRange,
      );
      if (highPoint.current && lowPoint.current) {
        showTooltip(highPosition, highPoint, highValueInRange, false);
        showTooltip(lowPosition, lowPoint, lowValueInRange, true);
      }
    }
  }, [
    chartData,
    getDataPointPosition,
    highValueInRange,
    isCoordinateReady,
    lowValueInRange,
    showTooltip,
  ]);

  const fetchChartData = useCallback(async () => {
    if (!symbol || !range) return;

    setLoading(true);
    setError(null);
    let marketTimer;
    try {
      const interval = ['1w', '1d'].includes(range) ? 'MINUTE' : 'DAY';
      const dateRange = periodRangeCalculator(range);
      const { startDate, newSession, timerId, toDate } = calculateStartDate(
        range,
        fetchChartData,
        show24hrChart,
      );
      marketTimer = timerId;
      const body = {
        fromDate: newSession ? dateRange.startDate : startDate,
        toDate: newSession ? dateRange.endDate : toDate,
        interval,
        pmlId: symbol.id,
      };

      const response = await getPriceChart(body);
      const data = transformData(response.data, range, show24hrChart);
      let transformedData;
      let intradayInd;
      if (range === '1d') {
        transformedData = data.data;
        intradayInd = data.ind;
      } else transformedData = data;
      const colors = getSeriesColor(
        transformedData,
        range,
        response.pc,
        theme,
        intradayInd,
      );
      setFirstValueInRange(
        range === '1d' ? response.pc : transformedData[0].value,
      );
      setSeriesConfig(colors);
      setChartData(transformedData);
    } catch (err) {
      setError(err.message || 'Failed to fetch chart data');
    } finally {
      setLoading(false);
    }

    return () => {
      if (marketTimer) {
        clearTimeout(marketTimer);
      }
    };
  }, [symbol, range, theme, show24hrChart]);

  useEffect(() => {
    fetchChartData();
  }, [fetchChartData]);

  const { ltp, lastTradeTime, pClose } = useStockAndInstrumentFeed({
    id: symbol.id,
    securityId: symbol.sec_id,
    exchange: symbol.exchange,
    segment: symbol.segment,
    instrumentType: symbol.inst_type,
  });

  useEffect(() => {
    if (!chartData || !ltp || !chartRef.current || !chartSeries) return;
    const updatedData = updateLastTradedPrice({
      data: [...chartData],
      tickValue: ltp,
      range,
      chartRef: chartRef.current,
      pClose,
      theme,
      chartSeries,
      show24hrChart,
    });

    if (updatedData?.shifted && show24hrChart) {
      setChartData(updatedData?.data);
    }

    if (updatedData) {
      setSeriesConfig(updatedData.colors);
    }
  }, [
    chartData,
    chartSeries,
    lastTradeTime,
    ltp,
    pClose,
    range,
    theme,
    show24hrChart,
  ]);

  const rangeValue = useMemo(() => {
    if (firstValueInRange && ltp) {
      return ((ltp - firstValueInRange) / Math.abs(firstValueInRange)) * 100;
    }
    return null;
  }, [ltp, firstValueInRange]);

  return {
    chartData,
    loading,
    error,
    seriesConfig,
    chartRef,
    rangeValue,
  };
};

export default useLightViewCharts;
