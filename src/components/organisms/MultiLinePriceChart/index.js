import { useRef, memo, useMemo } from 'react';
import ChartContainer from './ChartContainer';
import useMultiLinePrice from './useMultiLinePrice';

const MultiLinePriceChart = ({
  symbol,
  range,
  isTransparent,
  showHighLow = true,
  chartConfig = null,
  additionalData = [],
  additionalSeriesColor = '#FF0000',
  additionalDataLoading = false,
  useDefaultColors = false,
}) => {
  const chartContainerRef = useRef();

  // Memoize the props object passed to useLightViewCharts
  const chartProps = useMemo(
    () => ({
      symbol,
      range,
    }),
    [symbol, range],
  ); // Only depend on essential values

  const { error, loading, chartData } = useMultiLinePrice({
    ...chartProps,
    chartContainerRef,
    isTransparent,
    showHighLow,
    chartConfig,
    additionalData,
    additionalSeriesColor,
    useDefaultColors,
  });

  // Memoize the props passed to LightViewCharts
  const lightViewProps = useMemo(
    () => ({
      symbol,
      error,
      loading,
      chartData,
      chartContainerRef,
      additionalData,
      additionalDataLoading,
    }),
    [symbol, error, loading, chartData, additionalData, additionalDataLoading],
  );

  return <ChartContainer {...lightViewProps} />;
};

export default MultiLinePriceChart;
