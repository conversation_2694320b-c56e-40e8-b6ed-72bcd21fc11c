import React from 'react';
import { RadioButton } from '@paytm-h5-common/paytm_common_ui';
import { Percent } from '../../molecules/Prices';
import StockChange from '../StockChange/StockChange';
import CompanyIcon from '../../atoms/CompanyIcon/CompanyIcon';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../../atoms/CompanyIcon/IconsList';
import { useStockAndInstrumentFeed } from '../../../utils/Equities/hooks';
import styles from './ETFSelectionPopup.scss';

const ETFCard = ({ etf, isSelected, onSelect, returns }) => {
  const { ltp } = useStockAndInstrumentFeed({
    exchange: etf?.exchange,
    segment: etf?.segment,
    securityId: etf?.security_id,
    instrumentType: etf?.instrument_type,
    id: etf?.id,
  });

  const getPercentageReturns = (pClose) => {
    if (pClose && ltp) {
      return ((ltp - pClose) / Math.abs(pClose)) * 100;
    }
    return null;
  };

  return (
    <div
      className={`${styles.cardRoot} ${isSelected ? styles.selected : ''}`}
      onClick={() => onSelect(etf)}
    >
      <div className={styles.sipTitle}>
        <CompanyIcon
          name={etf.id}
          type={COMPANY_ICONS_NAME.STOCKS}
          className={styles.companyIcon}
        />
        <div className={styles.stockDetails}>
          <div className={styles.sipDetails}>
            <div className={styles.sipName}>{etf.name}</div>
            <RadioButton
              checked={isSelected}
              onChecked={() => onSelect(etf)}
              customClass={styles.radioButton}
            />
          </div>
          <StockChange
            exchange={etf.exchange}
            segment={etf.segment}
            securityId={etf.security_id}
            instrumentType={etf.instrument_type}
            id={etf.id}
          />
        </div>
      </div>
      <div className={styles.sipReturns}>
        {returns.map((ele) =>
          etf?.[ele.key] ? (
            <div className={styles.returnsWrapper} key={ele.title}>
              <div>{ele.title}</div>
              <Percent
                value={getPercentageReturns(etf?.[ele.key])}
                className={styles.value}
                withSign
              />
            </div>
          ) : null,
        )}
      </div>
    </div>
  );
};

export default ETFCard;
