@import '/src/commonStyles/variables.scss';

html,
body {
  background-color: transparent !important;
}

.etfSelectionPopupContainer {
  height: 85vh;
}

.scrollContainer {
  background-color: var(--background-pop-up);
  height: calc(85vh - 162px);
  overflow-y: auto;
  width: 100%;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */

  &::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background-color: transparent;
  }
}

.title {
  text-align: left;
  margin: 8px 0 0;

  @include typography(heading5B2, var(--text-neutral-strong))
}

.etfList {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 10px;
}

.radioButton {
  width: 24px;
}

.loadingContainer {
  height: 100%;
  display: flex;
  justify-content: center;
}

.cardRoot {
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-neutral-weak);
}

.cardRoot.selected {
  border-color: var(--border-primary-strong);
  z-index: 100;
}

.sipTitle {
    align-self: stretch;
    position: relative;
    overflow: hidden;
    padding: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    border-bottom: 1px solid var(--border-neutral-weak);
}

.viewAllCta {
  @include typography(body2B2, var(--text-primary-strong));
}

.stockDetails {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
}

.sipDetails {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .sipName {
    @include typography(body2B2, var(--text-neutral-strong));
    max-width: 100%;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    white-space: normal;
  }
}

.sipReturns {
    min-height: 60px;
    background: var(--BG-Glass, #EEEEEE0A);
    padding: 12px;
    display: flex;
    justify-content: space-around;
    @include typography(body2R4, var(--text-neutral-strong));
}

.returnsWrapper {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}