import React, { useCallback } from 'react';
import styles from './index.scss';
import { Badge } from '@paytm-h5-common/paytm_common_ui';
import {
  ChangeWithPercentIndianNumberingSystem,
  formatPrice,
} from '../../../components/molecules/Prices';
import { useStockAndInstrumentFeed } from '../../../utils/Equities/hooks';
import { roundValue } from '../../../utils/commonUtil';
import Shimmer from '../../../components/atoms/Shimmer';

const StockChange = ({
  exchange,
  segment,
  instrumentType,
  securityId,
  id,
  isBadge = false,
  stylesObj = {},
  showLtp = false,
}) => {
  const { ltp, change, percentageChange } = useStockAndInstrumentFeed({
    exchange,
    segment,
    securityId,
    instrumentType,
    id,
  });
  const getPreChild = () => {
    if (roundValue(change) > 0) return '+';
    if (roundValue(change) < 0) return '-';
    return '';
  };

  const getBadgeLabl = useCallback(() => {
    const changedPrice = roundValue(change);
    const percChangedPrice = roundValue(percentageChange);

    if (changedPrice > 0) {
      return `${showLtp ? formatPrice(ltp) : changedPrice} (+${percChangedPrice}%)`;
    }

    return `${showLtp ? formatPrice(ltp) : changedPrice * -1} (${percChangedPrice})%`;
  }, [change, ltp, percentageChange, showLtp]);

  if (isBadge) {
    return (
      <Badge
        type={change > 0 ? 'positive' : 'negative'}
        label={getBadgeLabl()}
        customClass={
          change > 0 ? stylesObj.positiveBadge : stylesObj.negativeBadge
        }
      />
    );
  }

  return (
    <div className={styles.stockChangeWrapper}>
      {ltp === undefined ? (
        <Shimmer
          type="line"
          width="70px"
          height="20px"
          margin="0px 5px 0px 0px"
        />
      ) : (
        <span className={styles.ltp} data-testid="ltp" id="ltp">
          {formatPrice(ltp)}
        </span>
      )}
      <ChangeWithPercentIndianNumberingSystem
        preChild={getPreChild()}
        changeWithSign
        value={change}
        percent={percentageChange}
        signClassName={styles.returnsPercentage}
        withRupee={false}
      />
    </div>
  );
};

export default StockChange;
