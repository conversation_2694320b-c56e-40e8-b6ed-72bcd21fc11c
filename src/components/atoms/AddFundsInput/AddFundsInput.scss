.inputWrapper {
  width: 100%;
}

.inputContainer {
  position: relative;
  width: 100%;
}

.inputStyle {
  width: 100%;
  color: #101010;
  background-color: #FFFFFF;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  padding: 6px 50px 6px 8px;
  border: none;
  outline: none;
  box-sizing: border-box;
  position: relative;
  cursor: default;
  pointer-events: none;
  user-select: none;
}

.darkMode {
  color: #EEEEEE;
  background-color: #101010;
  border-radius: 4px;
}

.editButton {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  color: var(--brand-primary-strong);
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
}

.inputLine {
  height: 1px;
  background-color: #ccc;
  margin-top: 4px;
}
