import cx from 'classnames';
import styles from './AddFundsInput.scss';
import { isDarkMode } from '../../../utils/commonUtil';

const AddFundsInput = ({
  value,
  type = 'text',
  name,
  id,
  disabled = false,
  className,
  onEditClick,
}) => (
  <div
    className={cx(
      styles.inputWrapper,
      { [styles.darkMode]: isDarkMode() },
      className,
    )}
  >
    <div className={styles.inputContainer}>
      <input
        readOnly
        value={value || ''}
        type={type}
        name={name}
        id={id}
        disabled={disabled}
        data-testid="amount"
        inputMode="decimal"
        className={cx(styles.inputStyle, {
          [styles.darkMode]: isDarkMode(),
        })}
      />
      <div className={styles.editButton} onClick={onEditClick}>
        Edit
      </div>
    </div>
    {/* <div className={styles.inputLine} /> */}
  </div>
);

export default AddFundsInput;
