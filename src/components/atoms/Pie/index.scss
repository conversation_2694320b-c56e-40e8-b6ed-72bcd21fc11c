@keyframes progress {
  0% {
    stroke-dasharray: 0 138.16; /* 2 * π * radius (22) */
  }
  100% {
    stroke-dasharray: calc(138.16 * var(--percentage) / 100) 138.16;
  }
}

.pie {
  border-radius: 50%;
  height: 48px;
  width: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  svg {
    position: absolute;
    top: 0;
    left: 0;
    transform: rotate(-90deg);
  }

  circle {
    fill: none;
    stroke-width: 4;
    stroke-linecap: round;

    &.background {
      stroke: var(--border-neutral-medium);
    }

    &.progress {
      stroke: var(--pie-color);
      stroke-dasharray: var(--percentage) 100;
      animation: progress 1s ease-out forwards;
    }
  }

  .number {
    font-size: 12px;
    height: fit-content;
    color: var(--text-neutral-strong);
  }
}
