import React from 'react';

import s from './index.scss';

const cleanPercentage = (percentage) => {
  const tooLow = !Number.isFinite(+percentage) || percentage < 0;
  const tooHigh = percentage > 100;
  return tooLow ? 0 : tooHigh ? 100 : +percentage;
};

const Pie = ({ percentage, colour }) => {
  const pct = cleanPercentage(percentage);

  return (
    <div
      className={s.pie}
      style={{
        '--percentage': pct,
        '--pie-color': colour,
      }}
    >
      <svg viewBox="0 0 48 48">
        <circle className={s.background} cx="24" cy="24" r="22" />
        <circle className={s.progress} cx="24" cy="24" r="22" />
      </svg>
      <div className={s.number}>{pct.toFixed(0)}%</div>
    </div>
  );
};

export default Pie;
