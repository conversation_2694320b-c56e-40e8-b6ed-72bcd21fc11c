/* eslint-disable no-param-reassign */
import cx from 'classnames';

import { COMPANY_LOGO_URL } from '../../../config/urlConfig';
import { isDarkMode } from '../../../utils/commonUtil';

import { ICONS, ICONS_NAME } from './IconsList';
import styles from './CompanyIcon.scss';
import Shimmer from '../Shimmer';

const CompanyIcon = ({
  name,
  type,
  className,
  url,
  isLoading = false,
  iconVariant = '',
  fallbackImg = '',
}) => {
  const getFallBackImage = () => {
    const isDarkModeEnable = isDarkMode();
    const iconType = ICONS[type] || ICONS[ICONS_NAME.STOCKS];
    return isDarkModeEnable ? iconType.dark : iconType.light;
  };

  const getCompanyLogoUrl = () => {
    if (url) {
      return url;
    }
    return COMPANY_LOGO_URL.GET_LOGO(name);
  };

  return isLoading ? (
    <Shimmer
      height="38px"
      width="48px"
      type="line"
      className={cx(styles.companyIcon, className, styles.shimmerAnimation)}
    />
  ) : (
    <div
      className={cx(styles.companyIcon, className, {
        [styles.fundsIcon]: iconVariant === 'funds',
      })}
    >
      <img
        className={styles.companyIcon}
        src={getCompanyLogoUrl()}
        alt="company logo"
        loading="eager"
        fetchPriority="high"
        onError={(event) => {
          event.target.src = fallbackImg || getFallBackImage();
          event.onerror = null;
        }}
      />
    </div>
  );
};

export default CompanyIcon;
