.circularLoaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

.circularLoader {
  position: relative;
  width: 36px;
  height: 36px;
  opacity: 0.3;
  transition: opacity 0.3s ease;
  background: #fff;
  border-radius: 50%;
}

.circularLoader.active {
  opacity: 1;
}

.loaderImage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  overflow: hidden;
  background: #fff;

  .iconWrapper {
    height: 20px;
    width: 20px;
  }
}

.loaderImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.circularChart {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circleBackground {
  fill: none;
  stroke: #eee;
  stroke-width: 2;
}

.circle {
  fill: none;
  stroke: #21c179;
  stroke-width: 2;
  stroke-dasharray: 100, 100;
  stroke-dashoffset: 100;
}

@keyframes progress {
  from {
    stroke-dashoffset: 100;
  }
  to {
    stroke-dashoffset: 0;
  }
}
