import React, { useEffect } from 'react';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../CompanyIcon/IconsList';
import CompanyIcon from '../CompanyIcon/CompanyIcon';
import styles from './CircularLoader.scss';

const CircularLoader = ({
  items,
  duration,
  activeStep,
  setActiveStep,
  itemClickEvent,
  isFirstTrade,
}) => {
  useEffect(() => {
    const timer = setTimeout(
      () => {
        setActiveStep(prev => (prev + 1) % items?.length); // Loop back to the first step
      },
      activeStep === -1 ? 0 : duration,
    ); // Delay first step's animation slightly

    return () => clearTimeout(timer);
  }, [activeStep, items?.length, duration]);

  const onStockClick = index => {
    setActiveStep(index);
    if (itemClickEvent) itemClickEvent(items[index]);
  };

  return (
    <div className={styles.circularLoaderContainer}>
      {items?.map((stock, index) => (
        <div
          key={index}
          className={`${styles.circularLoader} ${
            index === activeStep ? styles.active : ''
          }`}
          onClick={() => onStockClick(index)}
        >
          <div className={styles.loaderImage}>
            <CompanyIcon
              name={!isFirstTrade && stock?.indexId ? stock.indexId : stock?.id}
              type={COMPANY_ICONS_NAME.STOCKS}
              className={styles.iconWrapper}
            />
          </div>
          <svg viewBox="0 0 36 36" className={styles.circularChart}>
            <path
              className={styles.circleBackground}
              d="M18 2.0845
                 a 15.9155 15.9155 0 0 1 0 31.831
                 a 15.9155 15.9155 0 0 1 0 -31.831"
            />
            <path
              className={styles.circle}
              d="M18 2.0845
                 a 15.9155 15.9155 0 0 1 0 31.831
                 a 15.9155 15.9155 0 0 1 0 -31.831"
              style={{
                transition:
                  index === activeStep
                    ? `stroke-dashoffset ${duration / 1000}s ease-in-out`
                    : 'none',
                strokeDashoffset: index === activeStep ? '0' : '100',
                animation:
                  index === activeStep
                    ? `progress ${duration / 1000}s linear forwards`
                    : 'none',
              }}
            />
          </svg>
        </div>
      ))}
    </div>
  );
};

export default CircularLoader;
