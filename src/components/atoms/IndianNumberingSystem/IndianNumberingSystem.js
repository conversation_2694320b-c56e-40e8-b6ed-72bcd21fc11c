import React from 'react';
import { floorDecimalValue } from '../../../utils/commonUtil';

import styles from './IndianNumberingSystem.scss';

const IndianNumberingSystem = ({
  number,
  withRupeeSymbol = true,
  ...props
}) => {
  const prefix = withRupeeSymbol ? '₹' : '';
  // <span>₹</span> : <span />;
  if (number == null) return <div>0</div>;

  let stringNumber = number.toString().split('.');
  const len = stringNumber[0].length;
  const lenDup = len >= 8 ? 8 : len;
  stringNumber = stringNumber.join('');
  if (Number.isNaN(number)) {
    return (
      <div>
        <span className={styles.amount}>-</span>
      </div>
    );
  }

  switch (lenDup) {
    case 8: {
      let newNumber = '';
      newNumber += stringNumber.substring(0, len - 7);
      newNumber += '.';
      newNumber += stringNumber.substring(len - 7);
      return (
        <div {...props}>
          {prefix}
          <span className={styles.amount}>
            {floorDecimalValue(parseFloat(newNumber))}
          </span>
          <span>Cr</span>
        </div>
      );
    }
    case 7:
    case 6: {
      let newNumber = '';
      newNumber += stringNumber.substring(0, len - 5);
      newNumber += '.';
      newNumber += stringNumber.substring(len - 5);
      return (
        <div {...props}>
          {prefix}
          <span className={styles.amount}>
            {floorDecimalValue(parseFloat(newNumber))}
          </span>
          <span>L</span>
        </div>
      );
    }
    case 5:
    case 4: {
      let newNumber = '';
      newNumber += stringNumber.substring(0, len - 3);
      newNumber += '.';
      newNumber += stringNumber.substring(len - 3);
      return (
        <div {...props}>
          {prefix}
          <span className={styles.amount}>
            {floorDecimalValue(parseFloat(newNumber))}
          </span>
          <span>K</span>
        </div>
      );
    }
    case 3:
    case 2:
    case 1: {
      return (
        <div {...props}>
          {prefix}
          <span className={styles.amount}>
            {floorDecimalValue(number, true)}
          </span>
        </div>
      );
    }
    default:
      return (
        <div {...props}>
          {prefix}
          <span className={styles.amount}>
            {floorDecimalValue(number, true)}
          </span>
        </div>
      );
  }
};

export default IndianNumberingSystem;
