import React from 'react';
import classNames from 'classnames';
import PropTypes from 'prop-types';

import styles from './Button.scss';
import FadeLoader from '../FadeLoader';

const Button = ({
  id,
  buttonText,
  isDisabled,
  isPrimaryBlue,
  isSecondary,
  isRed,
  isTertiary,
  isTextOnly,
  onClickHandler,
  image,
  rightImage,
  className,
  isPrimary,
  isLoading,
  loadingText,
  showGreySpinner = true,
  applyCss,
  isPrimaryText,
  buttonTextClassName,
  buttonClassName,
  testId,
  customImageStyle,
  customRightImageStyle,
}) => {
  const buttonClass = classNames({
    [styles.button]: true,
    [className]: className,
    [styles.isSecondary]: isSecondary,
    [styles.isTertiary]: isTertiary,
    [styles.isTextOnly]: isTextOnly,
    [styles.isPrimaryText]: isPrimaryText,
    [styles.isDisabled]: isDisabled || isLoading,
    [styles.isRedButton]: isRed,
    [styles.isPrimaryBlue]: isPrimaryBlue,
    [styles.isPrimary]: isPrimary,
    [buttonClassName]: buttonClassName,
  });

  const buttonTextClass = classNames({
    [styles.buttonText]: !applyCss,
    [styles.isWhiteText]: isPrimaryBlue,
    [styles.isSecondaryText]: isSecondary,
    [styles.isTertiaryText]: isTertiary,
    [styles.isRed]: isRed,
    [styles.isDisabledText]: isDisabled,
    [buttonTextClassName]: buttonTextClassName,
  });

  return (
    <button
      id={id}
      data-testid={testId || ''}
      disabled={isDisabled || isLoading}
      onClick={onClickHandler}
      className={buttonClass}
    >
      {isLoading ? (
        <>
          <FadeLoader showGreySpinner={showGreySpinner} />
          {loadingText && (
            <span className={buttonTextClass}>{loadingText}</span>
          )}
        </>
      ) : (
        <>
          {image && (
            <img
              className={classNames(styles.image, customImageStyle)}
              src={image}
              alt=""
            />
          )}
          <span className={buttonTextClass} id={id}>
            {buttonText}
          </span>
          {rightImage && (
            <img
              className={classNames(styles.image, customRightImageStyle)}
              src={rightImage}
              alt=""
            />
          )}
        </>
      )}
    </button>
  );
};

Button.propTypes = {
  id: PropTypes.string,
  buttonText: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  image: PropTypes.string,
  isDisabled: PropTypes.bool,
  isPrimaryBlue: PropTypes.bool,
  isSecondary: PropTypes.bool,
  isTertiary: PropTypes.bool,
  isTextOnly: PropTypes.bool,
  onClickHandler: PropTypes.func,
  className: PropTypes.string,
  buttonTextClassName: PropTypes.string,
  isPrimary: PropTypes.bool,
  applyCss: PropTypes.bool,
  isPrimaryText: PropTypes.bool,
};

Button.defaultProps = {
  id: '',
  buttonText: '',
  image: '',
  isDisabled: false,
  isPrimaryBlue: false,
  isSecondary: false,
  isTertiary: false,
  isTextOnly: false,
  onClickHandler: () => {},
  className: '',
  buttonTextClassName: '',
  isPrimary: false,
  applyCss: false,
  isPrimaryText: false,
};

export default Button;
