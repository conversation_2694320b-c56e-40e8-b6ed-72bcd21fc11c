.button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 46px;
  padding: 0 10px;
  width: 100%;
  border-radius: 4px;
  border: none;
  outline: none;
}

.isPrimaryBlue {
  background-color: #004393;
}

.isSecondary {
  background-color: transparent;
  border: 1px solid #3e74dd;
}

.isTertiary {
  background-color: transparent;
  border: 1px solid #004393;
  height: 30px;
  max-width: 130px;
}

.isRedButton {
  background-color: #ffffff;
  box-shadow: 0 3px 11px 0 rgba(0, 0, 0, 0.05);
}

.buttonText {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  //font-size: 14px;
  //font-family: Muli;
  //font-weight: 700;
}

.isWhiteText {
  color: #ffffff;
}

.isSecondaryText {
  color: #3e74dd;
}

.isTertiaryText {
  font-size: 10px;
  line-height: 1.4;
  color: #004393;
  //@include ipoTypography(subTitle2, map-get($colors, DBlue));
}

.isRed {
  color: #d23d50;
}

.isTextOnly {
  background-color: transparent;
  padding: 0;
  width: auto;
  color: #004393;
  border: none;
}
.isPrimaryText {
  background-color: transparent;
  padding: 0;
  width: auto;
  color: #3e74dd;
  border: none;
}
.isDisabledText {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  color: #ffffff;
}

.image {
  width: 16px;
  height: 15px;
  object-fit: contain;
  margin-right: 5px;
}
.isPrimary{
  background-color: #3e74dd;
  color: #ffffff;
}
.isDisabled {
  background-color: #6f6f6f !important;
  opacity: 0.3 !important;
  cursor: not-allowed;
  pointer-events: none;
}
