import React from 'react';
import ReactDOM from 'react-dom';
import renderer from 'react-test-renderer';
import { render, cleanup } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';

import Button from './Button';

describe('Button Component Testing', () => {
  afterEach(cleanup);
  it('But<PERSON> renders correctly', () => {
    const div = document.createElement('div');
    ReactDOM.render(<Button />, div);
  });

  it('Button Style Class ', () => {
    const button = document.createElement('div');
    const { container } = render(<Button />, {
      container: document.body.appendChild(button),
    });

    expect(container.firstChild).toHaveClass('button');
  });

  it('Button renders with proper text ', () => {
    const button = document.createElement('div');
    const { container } = render(<Button buttonText="Submit Button" />, {
      container: document.body.appendChild(button),
    });

    expect(container.firstChild).toHaveTextContent('Submit Button');
  });

  it('Renders disabled button ', () => {
    const button = document.createElement('div');
    const { container } = render(
      <Button buttonText="Submit Button" isDisabled />,
      {
        container: document.body.appendChild(button),
      },
    );

    expect(container.firstChild).toHaveAttribute('disabled');
  });

  it('Renders primary button ', () => {
    const { getByTestId } = render(
      <Button testId="Primary-button" buttonText="Submit Button" isPrimary />,
    );

    expect(getByTestId('Primary-button')).toHaveClass('isPrimary');
  });

  it('Renders secondary button ', () => {
    const { getByTestId } = render(
      <Button
        testId="Secondary-button"
        buttonText="Submit Button"
        isSecondary
      />,
    );
    expect(getByTestId('Secondary-button')).toHaveClass('isSecondary');
  });

  it('Button captures onClickHandler', () => {
    const onClickHandler = jest.fn();

    const { getByTestId } = render(
      <Button
        testId="Primary-button"
        buttonText="Submit Button"
        isPrimary
        onClickHandler={onClickHandler}
      />,
    );

    getByTestId('Primary-button').click();
    expect(onClickHandler).toHaveBeenCalled();
  });

  it('Renders primary button with loading content ', () => {
    const { getByTestId } = render(
      <Button
        testId="Primary-button"
        loadingText="Loading Data"
        buttonText="Submit Button"
        isLoading
        isPrimary
      />,
    );
    expect(getByTestId('Primary-button')).toHaveTextContent('Loading Data');
  });

  it('Renders primary button with multiple classes ', () => {
    const { getByTestId } = render(
      <Button
        testId="Primary-button"
        buttonText="Submit Button"
        isPrimary
        isPrimaryBlue
        isRed
        isTertiary
      />,
    );

    expect(getByTestId('Primary-button')).toHaveClass('isPrimary');
    expect(getByTestId('Primary-button')).toHaveClass('isPrimaryBlue');
    expect(getByTestId('Primary-button')).toHaveClass('isRedButton');
    expect(getByTestId('Primary-button')).toHaveClass('isTertiary');
  });

  test('Button snapshot test', () => {
    const component = renderer.create(<Button />);
    const tree = component.toJSON();

    expect(tree).toMatchSnapshot();
  });
});
