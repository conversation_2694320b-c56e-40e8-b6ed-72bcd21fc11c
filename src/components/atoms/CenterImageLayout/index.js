import { API_ERROR } from '../../../utils/constants';
import If from '../If';
import styles from './index.scss';

const CenterImageLayout = ({
  img,
  heading,
  headingCss,
  body,
  bodyCss,
  description,
  descriptionCss,
  errorCode,
  rootCss,
  imageCss,
}) => (
  <div className={rootCss || styles.root}>
    <div className={imageCss || styles.image}>{img}</div>
    <div className={headingCss || styles.heading}>{heading}</div>
    <If test={description}>
      <div className={descriptionCss || styles.description}>{description}</div>
    </If>
    <If test={errorCode}>
      <div className={styles.error}>{API_ERROR.ERROR_CODE(errorCode)}</div>
    </If>
    <If test={body}>
      <div className={bodyCss || styles.body}>{body}</div>
    </If>
  </div>
);

export default CenterImageLayout;
