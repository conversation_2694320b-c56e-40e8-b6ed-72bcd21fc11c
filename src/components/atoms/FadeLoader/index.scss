@mixin fadeLoader($width: 2px, $height:5px, $radius:7px, $bgColor: #fff) {

  $quarter: ($radius / 2) + ($radius / 5.5);

  position: relative;
  width: $radius + $height;
  height: $radius + $height;
  margin: $radius 0 0 $radius;

  >div {
    width: $width;
    height: $height;
    background-color: $bgColor;
    position: absolute;
    animation: spinner 1.6s infinite ease-in-out;
  }

  > div:nth-child(1) {
    top: $radius;
    left: 0;
    animation-delay: calc(8*0.2s);
  }

  > div:nth-child(2) {
    top: $quarter;
    left: $quarter;
    transform: rotate(-45deg);
    animation-delay: calc(7*0.2s);
  }

  > div:nth-child(3) {
    top: 0;
    left: $radius;
    transform: rotate(90deg);
    animation-delay: calc(6*0.2s);
  }

  > div:nth-child(4) {
    top: -$quarter;
    left: $quarter;
    transform: rotate(45deg);
    animation-delay: calc(5*0.2s);
  }

  > div:nth-child(5) {
    top: -$radius;
    left: 0;
    animation-delay: calc(4*0.2s);
  }

  > div:nth-child(6) {
    top: -$quarter;
    left: -$quarter;
    transform: rotate(-45deg);
    animation-delay: calc(3*0.2s);
  }

  > div:nth-child(7) {
    top: 0;
    left: -$radius;
    transform: rotate(90deg);
    animation-delay: calc(2*0.2s);
  }

  > div:nth-child(8) {
    top: $quarter;
    left: -$quarter;
    transform: rotate(45deg);
    animation-delay: calc(1*0.2s);
  }
}

@keyframes spinner {
  0% {
    opacity: 1;
  }

  100% {
    opacity: .2;
  }
}

.spinner {
  @include fadeLoader();
}
.greySpinner {
  @include fadeLoader(2px,5px,7px,#8ba6c1);
}
