// @import '~commonStyles/commoncss';

.loaderBox {
  display: flex;
  width: 100vw;
  height: 200px;
  background: transparent;
  z-index: 21;
  top: 51px;
  &.headerTabsVisible {
    top: 120px;
  }
  left: 0;

  .loaderInner {
    margin: auto;
    display: flex;
    position: relative;
    &.headerTabsVisible {
      top: -120px;
    }
  }
}

@-webkit-keyframes scale {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }

  45% {
    -webkit-transform: scale(0.35);
    transform: scale(0.35);
    opacity: 0.7;
  }

  80% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scale {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }

  45% {
    -webkit-transform: scale(0.35);
    transform: scale(0.35);
    opacity: 0.7;
  }

  80% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}

.ballPulse > div:nth-child(1) {
  background-color: #012b72;
  -webkit-animation: scale 0.75s -0.24s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: scale 0.75s -0.24s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  margin: 2px;
}

.ballPulse > div:nth-child(2) {
  background-color: #012b72;
  -webkit-animation: scale 0.75s -0.12s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: scale 0.75s -0.12s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  margin: 2px;
}

.ballPulse > div:nth-child(3) {
  background-color: #012b72;
  -webkit-animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  margin: 2px;
}

.ballPulse > div:nth-child(4) {
  background-color: #00b9f5;
  -webkit-animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  margin: 2px;
}

.ballPulse > div:nth-child(5) {
  background-color: #00b9f5;
  -webkit-animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  margin: 2px;
}

.ballPulse > div:nth-child(6) {
  background-color: #00b9f5;
  -webkit-animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  margin: 2px;
}

.ballPulse > div {
  width: 12px;
  height: 12px;
  border-radius: 100%;
}

// .modal {
//     position: fixed;
//     top: 50%;
//     left: 50%;
//     height: auto;
//     z-index: 2000;
//     backface-visibility: hidden;
//     -webkit-transform: translateX(-50%) translateY(-50%);
//     -moz-transform: translateX(-50%) translateY(-50%);
//     -ms-transform: translateX(-50%) translateY(-50%);
//     transform: translateX(-50%) translateY(-50%);
// }

// .backdrop {
//     position: fixed;
//     width: 100%;
//     height: 100%;
//     visibility: visible;
//     top: 0;
//     left: 0;
//     z-index: 1000;
//     opacity: 1;
//     background: rgba(0, 0, 0, 0.8);
//     -webkit-transition: all 0.3s;
//     -moz-transition: all 0.3s;
//     transition: all 0.3s;
// }

// /*LOADER EFFECT START*/

// .loader {
//     background: none;
//     position: relative;
//     width: 200px;
//     height: 200px;
//     transform: scale(0.4);
// }

// .loader > div {
//     position: absolute;
//     display: block;
//     width: 160px;
//     height: 160px;
//     top: 20px;
//     left: 20px;
//     border-radius: 80px;
//     box-shadow: 0 6px 0 0 #00baf2;
//     -ms-animation: uil-ring-anim 1s linear infinite;
//     -moz-animation: uil-ring-anim 1s linear infinite;
//     -webkit-animation: uil-ring-anim 1s linear infinite;
//     -o-animation: uil-ring-anim 1s linear infinite;
//     animation: uil-ring-anim 1s linear infinite;
// }

// @-webkit-keyframes uil-ring-anim {
//     0% {
//         -ms-transform: rotate(0deg);
//         -moz-transform: rotate(0deg);
//         -webkit-transform: rotate(0deg);
//         -o-transform: rotate(0deg);
//         transform: rotate(0deg);
//     }

//     100% {
//         -ms-transform: rotate(360deg);
//         -moz-transform: rotate(360deg);
//         -webkit-transform: rotate(360deg);
//         -o-transform: rotate(360deg);
//         transform: rotate(360deg);
//     }
// }

// @-webkit-keyframes uil-ring-anim {
//     0% {
//         -ms-transform: rotate(0deg);
//         -moz-transform: rotate(0deg);
//         -webkit-transform: rotate(0deg);
//         -o-transform: rotate(0deg);
//         transform: rotate(0deg);
//     }

//     100% {
//         -ms-transform: rotate(360deg);
//         -moz-transform: rotate(360deg);
//         -webkit-transform: rotate(360deg);
//         -o-transform: rotate(360deg);
//         transform: rotate(360deg);
//     }
// }

// @-moz-keyframes uil-ring-anim {
//     0% {
//         -ms-transform: rotate(0deg);
//         -moz-transform: rotate(0deg);
//         -webkit-transform: rotate(0deg);
//         -o-transform: rotate(0deg);
//         transform: rotate(0deg);
//     }

//     100% {
//         -ms-transform: rotate(360deg);
//         -moz-transform: rotate(360deg);
//         -webkit-transform: rotate(360deg);
//         -o-transform: rotate(360deg);
//         transform: rotate(360deg);
//     }
// }

// @-ms-keyframes uil-ring-anim {
//     0% {
//         -ms-transform: rotate(0deg);
//         -moz-transform: rotate(0deg);
//         -webkit-transform: rotate(0deg);
//         -o-transform: rotate(0deg);
//         transform: rotate(0deg);
//     }

//     100% {
//         -ms-transform: rotate(360deg);
//         -moz-transform: rotate(360deg);
//         -webkit-transform: rotate(360deg);
//         -o-transform: rotate(360deg);
//         transform: rotate(360deg);
//     }
// }

// @-moz-keyframes uil-ring-anim {
//     0% {
//         -ms-transform: rotate(0deg);
//         -moz-transform: rotate(0deg);
//         -webkit-transform: rotate(0deg);
//         -o-transform: rotate(0deg);
//         transform: rotate(0deg);
//     }

//     100% {
//         -ms-transform: rotate(360deg);
//         -moz-transform: rotate(360deg);
//         -webkit-transform: rotate(360deg);
//         -o-transform: rotate(360deg);
//         transform: rotate(360deg);
//     }
// }

// @-webkit-keyframes uil-ring-anim {
//     0% {
//         -ms-transform: rotate(0deg);
//         -moz-transform: rotate(0deg);
//         -webkit-transform: rotate(0deg);
//         -o-transform: rotate(0deg);
//         transform: rotate(0deg);
//     }

//     100% {
//         -ms-transform: rotate(360deg);
//         -moz-transform: rotate(360deg);
//         -webkit-transform: rotate(360deg);
//         -o-transform: rotate(360deg);
//         transform: rotate(360deg);
//     }
// }

// @-o-keyframes uil-ring-anim {
//     0% {
//         -ms-transform: rotate(0deg);
//         -moz-transform: rotate(0deg);
//         -webkit-transform: rotate(0deg);
//         -o-transform: rotate(0deg);
//         transform: rotate(0deg);
//     }

//     100% {
//         -ms-transform: rotate(360deg);
//         -moz-transform: rotate(360deg);
//         -webkit-transform: rotate(360deg);
//         -o-transform: rotate(360deg);
//         transform: rotate(360deg);
//     }
// }

// @keyframes uil-ring-anim {
//     0% {
//         -ms-transform: rotate(0deg);
//         -moz-transform: rotate(0deg);
//         -webkit-transform: rotate(0deg);
//         -o-transform: rotate(0deg);
//         transform: rotate(0deg);
//     }

//     100% {
//         -ms-transform: rotate(360deg);
//         -moz-transform: rotate(360deg);
//         -webkit-transform: rotate(360deg);
//         -o-transform: rotate(360deg);
//         transform: rotate(360deg);
//     }
// }

// /*LOADER EFFECT END*/
