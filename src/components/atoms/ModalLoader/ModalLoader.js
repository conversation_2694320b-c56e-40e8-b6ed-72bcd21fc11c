import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import s from './ModalLoader.scss';

const ModalLoader = props => {
  const { showHeader } = props;
  return (
    <div
      className={cx(s.loaderBox, {
        [s.headerTabsVisible]: showHeader,
      })}
    >
      <div
        className={cx({
          [s.loaderInner]: true,
          [s.ballPulse]: true,
          [s.headerTabsVisible]: showHeader,
        })}
      >
        <div />
        <div />
        <div />
        <div />
        <div />
      </div>
    </div>
  );
};

ModalLoader.propTypes = {
  showHeader: PropTypes.bool,
};

ModalLoader.defaultProps = {
  showHeader: false,
};

export default ModalLoader;
