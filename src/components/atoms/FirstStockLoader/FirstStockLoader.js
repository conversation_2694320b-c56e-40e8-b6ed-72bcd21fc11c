import styles from './FirstStockLoader.scss';
import Skeleton from '../Skeleton/Skeleton';
import { isDarkMode } from '../../../utils/commonUtil';
import DEFAULT_CHART_LIGHT from '../../../assets/icons/default-light-chart.png';
import DEFAULT_CHART_LIGHT_DARK from '../../../assets/icons/default-light-chart-dark.png';

const StockCardLoader = () => (
  <div className={styles.cardContainer}>
    <div className={styles.card}>
      <div className={styles.headerWrapper}>
        <div className={styles.companyDetails}>
          <Skeleton width="32px" height="32px" borderRadius="50%" />
          <Skeleton width="120px" height="20px" />
        </div>
      </div>
      <div className={styles.chartContainer}>
        <div className={styles.wrapper}>
          <div className={styles.stockPriceWrapper}>
            <Skeleton width="80px" height="24px" />
            <Skeleton width="50px" height="24px" />
          </div>
          <Skeleton width="60px" height="24px" />
        </div>
        <div className={styles.imgWrapper}>
          <img
            className={styles.img}
            src={isDarkMode() ? DEFAULT_CHART_LIGHT_DARK : DEFAULT_CHART_LIGHT}
            alt="fallbackChart"
          />
        </div>
      </div>
      <div className={styles.statsContainer}>
        <Skeleton width="100%" height="16px" />
      </div>
      <div className={styles.ctaContainer}>
        <div className={styles.ctaRound}>
          <Skeleton width="24px" height="24px" borderRadius="50%" />
        </div>
        <Skeleton width="100%" height="44px" />
      </div>
    </div>
  </div>
);

const FirstStockLoader = () => (
  <div className={styles.mainContainer}>
    <div className={styles.cardsWrapper}>
      <StockCardLoader />
      <StockCardLoader />
    </div>
  </div>
);

export default FirstStockLoader;
