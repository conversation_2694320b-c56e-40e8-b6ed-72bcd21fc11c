.mainContainer {
  display: flex;
  flex-direction: column;
  margin: 16px;

  .cardsWrapper {
    display: flex;
    width: 100%;
    overflow-x: scroll;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
    padding-right: 40px;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.cardContainer {
  scroll-snap-align: center;
  scroll-snap-stop: normal;
  display: flex;
  flex-direction: column;
  min-width: 272px;
  margin-right: 16px;
  justify-content: space-between;
  position: relative;

  .card {
    border-radius: 16px;
    background-color: var(--surface-level-1);
    z-index: 4;
    
    .headerWrapper {
      margin: 16px 16px 0;
      
      .companyDetails {
        display: flex;
        padding: 4px;
        border-radius: 20px;
        align-items: center;
        margin-bottom: 12px;
        gap:10px;
        border: 1px solid var(--border-neutral-variant);
      }
    }
    
    .chartContainer {
      .wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 16px;
      }
      .stockPriceWrapper {
        display: flex;
        gap:10px;
      }
      
      .imgWrapper {
        display: flex;
        img {
          width: 100%;
          height: 100px;
        }
      }
    }

    .statsContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 16px;
      border-top: 1px solid var(--border-neutral-weak);
      border-bottom: 1px solid var(--border-neutral-weak);
    }

    .ctaContainer {
      display: flex;
      gap:20px;
      padding: 12px 16px;
      
      .ctaRound {
        border: 1px solid var(--border-neutral-variant);
        border-radius: 12px;
        padding: 10px;
      }
    }
  }
}
