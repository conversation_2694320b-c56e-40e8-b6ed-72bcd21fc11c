.row {
  display: flex;

  > div {
    margin-right: 1rem;
  }

  div:first-child {
    margin-left: 0;
  }

  div:last-child {
    margin-right: 0;
  }
}

.shimmerAnimation {
  animation: shimmer 2s infinite linear;
  background: var(
    --shimmer,
    linear-gradient(
      to right,
      rgba(239, 241, 243, 30%) 4%,
      rgba(226, 226, 226, 30%) 25%,
      rgba(239, 241, 243, 30%) 36%
    )
  );
  background-size: 100rem 100%;
}

@keyframes shimmer {
  0% {
    background-position: -100rem 0;
  }

  100% {
    background-position: 100rem 0;
  }
}

.itemContainer {
  display: flex;

  .mainSection {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  .mainContent {
    flex-grow: 1;
    padding: 16px 20px 15px 0;
  }

  .rowSection {
    display: flex;

    align-items: center;
    flex-grow: 1;
    padding-right: 16px;
  }

  .rowItem {
    flex-grow: 1;
  }

  .rightIcon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
