import { Switch } from '@paytm-h5-common/paytm_common_ui';
import ArrowRightIcon from '@paytm-h5-common/paytm_common_ui/icons/system/nav/ArrowRight';
import { Icon } from '@paytm-money/utils-frontend';
import { LineShimmer } from '..';
import styles from './index.scss';

function ListItemShimmer({
  height = '20px',
  margin,
  isRightIconVisible,
  isSwitchVisible,
  hideLeadingShimmer,
}) {
  return (
    <div className={styles.itemContainer}>
      {!hideLeadingShimmer && (
        <div className={styles.icon}>
          <LineShimmer height="36px" width="36px" margin="16px 16px" />
        </div>
      )}
      <div className={styles.mainSection}>
        <div className={styles.rowSection}>
          <div className={styles.mainContent}>
            <LineShimmer
              width="40%"
              height={height}
              margin={margin}
              className={styles.rowItem}
            />
            <LineShimmer
              width="70%"
              height="16px"
              margin="4px 0 0"
              className={styles.rowItem}
            />
          </div>
          {isRightIconVisible && <Icon srcLight={ArrowRightIcon} size={1.6} />}
          {isSwitchVisible && <Switch />}
        </div>
      </div>
    </div>
  );
}

export default ListItemShimmer;
