import { Separator } from '@paytm-h5-common/paytm_common_ui';
import ArrowRightIcon from '@paytm-h5-common/paytm_common_ui/icons/system/nav/ArrowRight';
import { Icon } from '@paytm-money/utils-frontend';
import cx from 'classnames';
import styles from './index.scss';

export const LineShimmer = ({
  className,
  height = '20px',
  width = '100%',
  margin,
}) => (
  <div
    className={cx(styles.shimmerAnimation, {
      [className]: className,
    })}
    style={{ height, width, margin: `${margin || 0}` }}
  />
);

const Shimmer = ({
  type = 'line',
  height = '20px',
  width = '100%',
  cardArray = [
    {
      height: '20px',
      row: ['20%', '30%'], // percentage
      margin: '0 0 10px 0',
    },
  ],
  margin,
  className,
  listProps = {
    listSize: 5,
    showArrowIcon: true,
    hideSecondRow: false,
  },
}) => {
  switch (type) {
    case 'line':
      return (
        <LineShimmer
          className={className}
          height={height}
          width={width}
          margin={margin}
        />
      );
    case 'card':
      return cardArray.map((shimmerItem, index) => (
        <div
          key={`shimmer-${index}`}
          className={cx(styles.row, {
            [className]: className,
          })}
          style={{
            margin: `${shimmerItem.margin || 0}`,
            justifyContent: `${shimmerItem.justifyContent || 'unset'}`,
          }}
        >
          {shimmerItem.row.map((shimmerWidth, i) => (
            <div
              key={`shimmerItem-${i}`}
              className={styles.shimmerAnimation}
              style={{
                height: `${shimmerItem.height || '20px'}`,
                width: `${shimmerWidth || '20%'}`,
              }}
            />
          ))}
        </div>
      ));
    case 'circle':
      return (
        <div
          className={cx(styles.shimmerAnimation, {
            [className]: className,
          })}
          style={{ height: width, width, borderRadius: width, margin }}
        />
      );

    case 'list':
      return [...Array(listProps.listSize)].map((_, index) => (
        <div key={index} className={styles.itemContainer}>
          <div className={styles.icon}>
            <LineShimmer height="36px" width="36px" margin="20px 16px" />
          </div>
          <div className={styles.mainSection}>
            <div className={styles.rowSection}>
              <div className={styles.mainContent}>
                <LineShimmer
                  width={width}
                  height={height}
                  className={styles.rowItem}
                />
                {!listProps?.hideSecondRow && (
                  <LineShimmer
                    width={width}
                    height="16px"
                    margin="4px 0 0"
                    className={styles.rowItem}
                  />
                )}
              </div>
              {listProps?.showArrowIcon && (
                <Icon srcLight={ArrowRightIcon} size={1.6} />
              )}
            </div>
            <Separator hairline />
          </div>
        </div>
      ));
    default:
      return <> </>;
  }
};

export default Shimmer;
