import { useRef, useEffect, useState } from 'react';

import PropTypes from 'prop-types';
import cx from 'classnames';
import Icon, { ICONS_NAME } from '../Icon';
import ModalLoader from '../../atoms/ModalLoader/ModalLoader';
import { useBackPress } from '../../../hooks/useNativeBackPress';
import { exitApp } from '../../../utils/bridgeUtils';

import { useDrawer } from './useDrawer';

import styles from './index.scss';

const ALIGNMENTS = {
  CENTER: 'CENTER',
  LEFT: 'LEFT',
};

function Drawer({
  toastMessage = false,
  showToast = false,
  customSnackbar = false,
  isLoading = false,
  children,
  isOpen,
  onClose,
  className,
  popup,
  title,
  align,
  showCross,
  headerStyles,
  crossIconZIndex,
  showHandleBar,
  updateScroll,
  customContentClass,
  customCrossIconName,
  topDividerCustomClass,
  closeAllowed,
  noAnimation,
  customTitle,
  otpExit = false,
  backDropClass,
}) {
  const drawerRef = useRef(null);
  const containerRef = useRef(null);
  const [animateDrawer, setAnimateDrawerState] = useState(false);

  const [showModal, setShowModal] = useState(false);
  const { popStack, pushStack } = useBackPress();

  useEffect(() => {
    if (isOpen && closeAllowed) {
      pushStack(onClose);
    }
  }, [isOpen]);

  useEffect(() => {
    // to prevent body scroll
    if (isOpen) {
      const body = document.getElementsByTagName('BODY')[0];
      body.style.overflow = 'hidden';
      body.style.setProperty('background', 'transparent', 'important');
    }
    return () => {
      if (isOpen) {
        const body = document.getElementsByTagName('BODY')[0];
        body.style.overflow = 'initial';
        body.style.setProperty('background', 'inherit', 'important');
      }
    };
  }, [isOpen]);

  const listenScrollEvent = (e) => {
    updateScroll(
      e.target.scrollTop,
      e.target.scrollHeight,
      e.target.clientHeight,
    );
  };

  /* onOpen - start */
  useEffect(() => {
    if (isOpen) {
      setShowModal(true);
      setAnimateDrawerState(true);
    }
  }, [isOpen]);

  /* onOpen - onEnd */

  /* onClose - start */
  useEffect(() => {
    if (!isOpen) {
      setAnimateDrawerState(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (!noAnimation) {
      if (!isOpen && showModal) {
        const timeout = setTimeout(() => {
          setShowModal(false);
        }, 350);
        window.addEventListener('animationend', function closeModal() {
          setShowModal(false);
          clearTimeout(timeout);
          window.removeEventListener('animationend', closeModal);
        });
      }
    }
  }, [animateDrawer, noAnimation]);
  /* onClose - onEnd */

  function handlePopStack(e) {
    if (otpExit) {
      onClose(!isOpen);
      exitApp();
    }
    // onClose(!isOpen);
    e.stopPropagation();
    popStack();
  }

  if (noAnimation ? !isOpen : !showModal) {
    return null;
  }

  return (
    <div className={styles.wrapper}>
      <span
        className={cx(styles.backDrop, {
          [styles.hideBackDrop]: !animateDrawer,
          [styles.showBackDrop]: animateDrawer,
          [backDropClass]: backDropClass,
        })}
        onClick={closeAllowed ? handlePopStack : undefined}
        ref={containerRef}
      />
      <div
        className={cx('', {
          [styles.hideDrawer]: !animateDrawer && !popup && !noAnimation,
          [styles.showDrawer]: animateDrawer && !popup && !noAnimation,
          [styles.hidePopup]: !animateDrawer && popup && !noAnimation,
          [styles.showPopup]: animateDrawer && popup && !noAnimation,
          [styles.drawer]: true,
          [styles.popup]: popup,
          [className]: className,
        })}
        ref={drawerRef}
      >
        <div className={styles.drawerWrapper}>
          {showToast && (
            <div
              className={
                customSnackbar ? styles.successSnackbar : styles.errorSnackbar
              }
            >
              <span>{toastMessage}</span>
            </div>
          )}
          {showHandleBar ? (
            <div
              className={styles.dividerWrapper}
              onClick={closeAllowed ? handlePopStack : undefined}
            >
              <div
                className={cx(styles.topDivider, {
                  [topDividerCustomClass]: topDividerCustomClass,
                })}
              />
            </div>
          ) : (
            <div
              className={cx(styles.header, {
                [headerStyles]: headerStyles,
              })}
            >
              {!customTitle && align === ALIGNMENTS.CENTER && <span />}
              <p className={customTitle || styles.title}>
                {!isLoading ? title : ''}
              </p>
              {showCross && (
                <Icon
                  name={customCrossIconName || ICONS_NAME.CLOSE}
                  className={styles.icon}
                  size={customCrossIconName ? 6 : 5}
                  onClick={closeAllowed ? handlePopStack : undefined}
                  zIndex={crossIconZIndex}
                />
              )}
            </div>
          )}

          {isLoading ? (
            <ModalLoader />
          ) : (
            <div
              className={cx(styles.content, {
                [customContentClass]: customContentClass,
              })}
              onScroll={listenScrollEvent}
            >
              {children}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

Drawer.propTypes = {
  children: PropTypes.node,
  onClose: PropTypes.func,
  updateScroll: PropTypes.func,
  className: PropTypes.string,
  customContentClass: PropTypes.string,
  customCrossIconName: PropTypes.string,
  title: PropTypes.string,
  align: PropTypes.string,
  headerStyles: PropTypes.string,
  crossIconZIndex: PropTypes.number,
  isOpen: PropTypes.bool,
  popup: PropTypes.bool,
  showCross: PropTypes.bool,
  showHandleBar: PropTypes.bool,
  closeAllowed: PropTypes.bool,
  noAnimation: PropTypes.bool,
};

Drawer.defaultProps = {
  children: <span />,
  onClose: () => {},
  updateScroll: () => {},
  className: '',
  customContentClass: '',
  customCrossIconName: '',
  title: '',
  align: ALIGNMENTS.CENTER,
  headerStyles: '',
  crossIconZIndex: 0,
  isOpen: false,
  popup: false,
  showHandleBar: false,
  noAnimation: false,
  showCross: true,
  closeAllowed: true,
};

export { Drawer as default, useDrawer, ALIGNMENTS };
