$timing: 0.25s;

$popupTiming: 0.1s;

@keyframes bubbleUp {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes bubbleDown {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(100%);
  }
}

@keyframes popupIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes popupOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.wrapper {
  position: relative;
  z-index: 99999;
}

.successSnackbar{
  font-size: 14px;
    font-weight: 500;
    line-height: 1.43;
    color: #ffffff;
  z-index: 999999;
  background-color: #21C179;
  text-align: center;
  padding: 2px 0 2px 0;
}

.errorSnackbar{
  font-size: 14px;
  font-weight: 500;
  line-height: 1.43;
  color: #ffffff;
  z-index: 999999;
  background-color: #FD5154;
  text-align: center;
  padding: 2px 0 2px 0;
}

.backDrop {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-color: RGBA((0,0,0), 0.5);
  z-index: 1;
  transition: opacity $popupTiming ease-out;
}

.drawerWrapper {
  //fix for safari
  // Only in Safari - position:fixed child cut off when parent is position:fixed and overflow:hidden
  overflow: hidden;
}

.drawer {
  background-color: #ffffff;
  position: fixed;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2;
  border-radius: 20px 20px 0 0;
  transition: all 0.2s;
  //transform: translateY(100%);
}

.hideBackDrop {
  opacity: 0;
}

.showBackDrop {
  opacity: 1;
}

.showDrawer {
  animation: bubbleUp $timing ease-out;
}

.hideDrawer {
  animation: bubbleDown $timing ease-in;
}

.showPopup {
  animation: popupIn $popupTiming ease-out;
}

.hidePopup {
  animation: popupOut $popupTiming ease-in;
}

.content {
  max-height: 80vh;
  overflow: auto;
}

.icon {
  display: flex;
  justify-content: flex-end;
  right: 0;
}

.popup {
  bottom: initial;
  top: 50%;
  width: 80%;
  margin: auto;
  border-radius: 10px;
  transform: translateY(-50%);
}

.dividerWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 20px 16px 0 16px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #494949;
  margin: 0;
}

.topDivider {
  width: 50px;
  height: 6px;
  border-radius: 20px;
  background-color: #e8edf3;
  margin-bottom: 10px;
  align-self: center;
}
