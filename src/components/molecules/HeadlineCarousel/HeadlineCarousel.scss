.headerLeft {
  display: flex;
  align-items: center;
  gap: 8px;

}

.icon {
  width: 20px;
  height: 20px;
  color: rgba(28, 28, 28, 0.95);
  flex-shrink: 0;
}

.sliderContainer {
  height: 20px;
  overflow: hidden;
  position: relative;
}

.sliderWrapper {
  height: 20px;
  display: flex;
  flex-direction: column;
  transition: transform 0.5s ease-in-out;
}

.sliderItem {
  width: 100%;
  color: var(--icon-neutral-strong);
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
}
