import { useState, useEffect } from 'react';
import styles from './HeadlineCarousel.scss';

const HeadlineCarousel = ({
  headlines,
  icon,
  className = '',
  interval = 2000,
}) => {
  const [index, setIndex] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setIndex((prev) => (prev + 1) % headlines.length);
    }, interval);
    return () => clearInterval(timer);
  }, [headlines, interval]);

  return (
    <div className={`${styles.headerLeft} ${className}`}>
      {icon && <img src={icon} alt="Icon" className={styles.icon} />}
      <div className={styles.sliderContainer}>
        <div
          className={styles.sliderWrapper}
          style={{ transform: `translateY(-${index * 100}%)` }}
        >
          {headlines.map((text, i) => (
            <div className={styles.sliderItem} key={i}>
              {text}
            </div>
          ))}
        </div>{' '}
      </div>
    </div>
  );
};

export default HeadlineCarousel;
