import cx from 'classnames';

import { useStockAndInstrumentFeed } from '../../../utils/Equities/hooks';
import { roundValue } from '../../../utils/commonUtil';
import Shimmer from '../../atoms/Shimmer';
import { Percent, formatPrice } from '../Prices';
import styles from './index.scss';

const StockPriceChange = ({
  exchange,
  securityId,
  segment,
  change,
  fromComponent,
  customClassName = '',
  isSymbol = false,
  isnoLTP = false,
  withRupee = true,
  customPCloseRange = '',
  customMessage = '',
}) => {
  const { ltp, percentageChange } = useStockAndInstrumentFeed({
    segment,
    exchange,
    securityId,
  });

  const getPercentChangeClassName = (value) =>
    roundValue(value) < 0
      ? cx(styles.ltpLoss, {
          [styles.reminderPercentChangeText]: fromComponent === 'reminder',
        })
      : roundValue(value) > 0
        ? cx(styles.ltpProfit, {
            [styles.reminderPercentChangeText]: fromComponent === 'reminder',
          })
        : cx(styles.ltpZero, {
            [styles.reminderPercentChangeText]: fromComponent === 'reminder',
          });

  return (
    <span
      className={
        ltp === undefined
          ? cx(styles.stockPrice, styles.priceShimmerWrapper, {
              [customClassName]: customClassName,
              [styles.reminderStockPrice]: fromComponent === 'reminder',
            })
          : cx(styles.stockPrice, {
              [customClassName]: customClassName,
              [styles.reminderStockPrice]: fromComponent === 'reminder',
            })
      }
    >
      {ltp === undefined ? (
        <Shimmer
          type="line"
          width="70px"
          height="24px"
          margin="0px 5px 0px 0px"
        />
      ) : (
        <span className={styles.buyPrice} data-testid="ltp" id="ltp">
          {!isSymbol && withRupee && <span> ₹ </span>}
          {!isnoLTP && formatPrice(ltp)}
        </span>
      )}
      {ltp === undefined ? (
        <Shimmer type="line" width="50px" height="24px" />
      ) : (
        <span className={styles.ltp}>
          <span
            className={cx(styles.percentChange, {
              [styles.reminderPercentChange]: fromComponent === 'reminder',
            })}
          >
            <Percent
              withSign
              value={change || percentageChange}
              className={getPercentChangeClassName(change || percentageChange)}
              isnoLTP={isnoLTP}
              isSymbol={isSymbol}
              customPCloseRange={customPCloseRange}
              customMessage={customMessage}
            />
          </span>
        </span>
      )}
    </span>
  );
};

export default StockPriceChange;
