import cx from 'classnames';
import Icon, { ICONS_NAME } from '../Icon';
import {
  roundValue,
  isValidNumber,
  getAbsoluteValue,
} from '../../../utils/commonUtil';

import styles from './index.scss';
import IndianNumberingSystem from '../../atoms/IndianNumberingSystem/IndianNumberingSystem';

function priceClassName(value, baseClass = styles.base, neutral = false) {
  return cx(baseClass, {
    [styles.positive]: !neutral && roundValue(value) > 0,
    [styles.negative]: !neutral && roundValue(value) < 0,
  });
}

function formatPrice(value, decimals = 2, format = true, getAbsolute = true) {
  if (!isValidNumber(value)) {
    return `0${decimals ? `.${'0'.repeat(decimals)}` : ''}`;
  }
  if (format) {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: decimals,
    }).format(getAbsoluteValue(value, decimals, getAbsolute));
  }
  return getAbsoluteValue(value, decimals, getAbsolute);
}

const Sign = ({ value }) => {
  if (roundValue(value) > 0) {
    return '+';
  }
  if (roundValue(value) < 0) {
    return '-';
  }
  return '';
};

function ChangeIcon({ value, className, ...props }) {
  if (roundValue(value) > 0) {
    return (
      <Icon
        className={className}
        name={ICONS_NAME.UP_ARROW}
        {...props}
        byPass
      />
    );
  }
  if (roundValue(value) < 0) {
    return (
      <Icon
        className={className}
        name={ICONS_NAME.DOWN_ARROW}
        {...props}
        byPass
      />
    );
  }
  if (Number(roundValue(value)) === 0) {
    return (
      <Icon
        className={className}
        name={ICONS_NAME.UP_AND_DOWN_ARROW}
        {...props}
        byPass
      />
    );
  }
  return null;
}

const getIcon = (value) => {
  const roundedValue = roundValue(value);
  if (roundedValue > 0) return ICONS_NAME.UP_ARROW;
  if (roundedValue < 0) return ICONS_NAME.DOWN_ARROW;
  return ICONS_NAME.UP_AND_DOWN_ARROW;
};

function Change({
  value,
  withSign,
  bothSign,
  signClassName,
  withRupee,
  className,
  neutral,
  withFraction = true,
  withArrow = false,
}) {
  return (
    <div className={priceClassName(value, className, neutral)}>
      {((withSign && !(withRupee && roundValue(value) > 0)) || bothSign) && (
        <span className={signClassName}>
          <Sign value={value} />
        </span>
      )}
      {withArrow && (
        <Icon
          name={getIcon(value)}
          className={styles.withArrow}
          size={2}
          bypass
        />
      )}
      {withRupee && '₹'}
      <span>{formatPrice(value).toString().split('.')[0]}</span>
      {withFraction && (
        <>
          .<span>{formatPrice(value).toString().split('.')[1]}</span>
        </>
      )}
    </div>
  );
}
function Rupee({ value, withRupee, className, neutral }) {
  return (
    <div className={priceClassName(value, className, neutral)}>
      {withRupee && '₹'}
      <span>{value}</span>
    </div>
  );
}

function Percent({
  value,
  withSign,
  className,
  isnoLTP = false,
  isSymbol = false,
  customPCloseRange = '',
  customMessage = '',
}) {
  const getText = () => {
    if (customMessage) return customMessage;
    return 'Avg. SIP Returns: ';
  };

  return (
    <div className={priceClassName(value, className)} id="percentChange">
      {isnoLTP && <span className={styles.sipReturnsLabel}>{getText()}</span>}
      {withSign && <Sign value={value} />}
      {getAbsoluteValue(value)}%
      {!isnoLTP && isSymbol && !customPCloseRange && (
        <span className={styles.returnText}>(1 Day)</span>
      )}
    </div>
  );
}

function ChangeWithPercent({
  value,
  percent,
  withRupee,
  className,
  withArrow,
  changeWithSign,
  bothSign,
  signClassName,
  textClassName,
  percentWithSign,
  preChild,
  withFraction = true,
}) {
  const containerClass = `${className || styles.base} ${
    styles.changeWithPercentContainer
  }`;
  return (
    <div
      className={priceClassName(
        percent === Infinity ? 0 : value,
        containerClass,
      )}
    >
      {preChild && preChild}
      <Change
        bothSign={bothSign}
        withFraction={withFraction}
        value={percent === Infinity ? 0 : value}
        withSign={changeWithSign}
        withRupee={withRupee}
        withArrow={withArrow}
        className={textClassName}
        signClassName={signClassName}
      />
      <div
        className={priceClassName(
          percent === Infinity ? 0 : percent,
          containerClass,
        )}
      >
        (
      </div>
      <Percent
        value={percent === Infinity ? 0 : percent}
        withSign={percentWithSign}
        className={textClassName}
      />
      <div
        className={priceClassName(
          percent === Infinity ? 0 : percent,
          containerClass,
        )}
      >
        )
      </div>
    </div>
  );
}

function ChangeWithPercentIndianNumberingSystem({
  value,
  percent,
  withRupee,
  className,
  withArrow,
  changeWithSign,
  bothSign,
  signClassName,
  percentWithSign,
  preChild,
  withFraction = true,
}) {
  const containerClass = `${className || styles.base} ${
    styles.changeWithPercentContainer
  }`;
  return (
    <div
      className={priceClassName(
        percent === Infinity ? 0 : value,
        containerClass,
      )}
    >
      {preChild && preChild}
      <ChangesIndianNumberingSystem
        bothSign={bothSign}
        withFraction={withFraction}
        value={percent === Infinity ? 0 : value}
        withSign={changeWithSign}
        withRupee={withRupee}
        withArrow={withArrow}
        signClassName={signClassName}
      />
      <div
        className={priceClassName(
          percent === Infinity ? 0 : percent,
          containerClass,
        )}
      >
        (
      </div>
      <PercentIndianNumberingSystem
        value={percent === Infinity ? 0 : percent}
        withSign={percentWithSign}
      />
      <div
        className={priceClassName(
          percent === Infinity ? 0 : percent,
          containerClass,
        )}
      >
        )
      </div>
    </div>
  );
}

function ChangesIndianNumberingSystem({
  value,
  className,
  neutral,
  withRupee,
}) {
  return (
    <div className={priceClassName(value, className, neutral)}>
      <IndianNumberingSystem
        className={styles.numberSystem}
        number={getAbsoluteValue(value, 2, true)}
        withRupeeSymbol={withRupee}
      />
    </div>
  );
}

function PercentIndianNumberingSystem({ value, withSign, className }) {
  return (
    <div className={priceClassName(value, className)} id="percentChange">
      {withSign && <Sign value={value} />}
      <IndianNumberingSystem
        withRupeeSymbol={false}
        className={styles.numberSystemWithoutMargin}
        number={getAbsoluteValue(value, 2, true)}
      />
      %
    </div>
  );
}

export {
  ChangeWithPercent,
  Change,
  Percent,
  ChangeIcon,
  Rupee,
  formatPrice,
  ChangeWithPercentIndianNumberingSystem,
};
