.changeWithPercentContainer {
  display: flex;
}

.withArrow {
  display: inline-flex !important;
  margin-right: 5px;
}

.base {
  color: var(--text-neutral-strong);
  font-size: 12px;
  line-height: 16px;
}

.positive {
  color: var(--text-positive-strong);
}

.negative {
  color: var(--text-negative-strong);
}

.numberSystem {
  display: inline-block;
  margin-left: 2px;
}

.numberSystemWithoutMargin {
  display: inline-block;
  font-size: 12px;
  font-weight: 500;
}

.sipReturnsLabel {
  color: var(--text-neutral-strong);
  font-weight: 400;
  margin-right: 2px;
  font-size: 14px;
  line-height: 20px;
}

.returnText {
  color: var(--text-neutral-medium);
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin-left: 5px;

  @media only screen and (max-width: 375px) {
    font-size: 12px;
  }
}