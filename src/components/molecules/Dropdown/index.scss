@import '../../../commonStyles/commoncss';
@import '../../../commonStyles/variables.scss';
.dropdownHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: solid 1px  map-get($colors, Grey17);
  padding: 0 3px 9px;

  &.dropdown {
    border-bottom: solid 1px map-get($colors, Grey11);
  }
}

.selectedVal {
  @include typography(heading3, map-get($colors, DBlue2));
}

.dropdownContainer {
  position: relative;
}

.optionsList {
  padding: 20px 16px 5px 16px;
  border-radius: 10px;
  box-shadow: 0 4px 13px 0 map-get($colors, ShadowColor6);
  border: solid 1px map-get($colors, Grey12);
  background-color: map-get($colors, PureWhite);
  position: absolute;
  width: 90%;
  z-index: 999;
  overflow: auto;
  max-height: 180px;

  > div:last-child {
    margin: 0 !important;
  }
}

.option {
  @include typography(body1R, map-get($colors, DBlue2));
  margin: 0 0 20px;
}

.title {
  @include typography(body2, map-get($colors, Grey11));
  margin-bottom: 15px;
}

.noSelectedVal {
  @include typography(heading3, map-get($colors, Grey11));
}
