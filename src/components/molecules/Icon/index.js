import { useRef } from 'react';
import cx from 'classnames';

// components
import LazyImg from '../LazyImg';

// utils, constants, enums, config
import { isDarkMode } from '../../../utils/commonUtil';
import { ICONS, ICONS_NAME, STATICS, THEME, LOGO_SIZE } from './enum';

import styles from './index.scss';

const Icon = ({
  name = ICONS_NAME.PLAY,
  size = 1,
  width,
  className,
  onClick = () => {},
  iconStyles = '',
  showDarkModeIcon = false,
  inLineStyles,
  zIndex = 0,
  url = '',
  onImgLoad,
  shimmerType,
  byPass = false,
}) => {
  // prop showDarkModeIcon is to forcibly show darkModeIcon (UseCase: Custom Header)
  const activeTheme = useRef(isDarkMode() ? THEME.DARK : THEME.LIGHT);

  return (
    <div
      className={cx(styles.wrapper, {
        [className]: className,
      })}
      onClick={onClick}
    >
      <LazyImg
        className={cx('', {
          [iconStyles]: iconStyles,
        })}
        src={
          url ||
          (showDarkModeIcon
            ? ICONS[THEME.DARK][name]
            : ICONS[activeTheme.current][name])
        }
        style={
          inLineStyles || {
            width: width || size * STATICS.SIZE_MULTIPLIER,
            objectFit: 'contain',
            zIndex,
          }
        }
        alt=""
        onLoad={onImgLoad}
        byPass={byPass}
        shimmerType={shimmerType}
      />
    </div>
  );
};

export default Icon;

export { ICONS_NAME, LOGO_SIZE };
