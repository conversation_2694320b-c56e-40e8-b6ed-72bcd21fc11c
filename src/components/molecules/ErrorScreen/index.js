import cx from 'classnames';

import { Button } from '@paytm-h5-common/paytm_common_ui';

import CenterImageLayout from '../../atoms/CenterImageLayout';

import { EMPTY_SCREEN } from '../../../utils/constants';
import ErrorScreenIcon from '../../../assets/errorScreenIcon.png';

import styles from './index.scss';

const ErrorScreen = ({
  customRootStyle,
  errorImg = null,
  errorImgStyle = null,
  onRefreshClick,
  buttonText = null,
  buttonClassName = null,
  heading,
  message,
  errorCode,
  retryCallback,
}) => (
  <div className={cx(customRootStyle, styles.root)}>
    <CenterImageLayout
      img={
        <img
          className={cx(errorImgStyle, styles.icon)}
          src={errorImg || ErrorScreenIcon}
          alt=""
        />
      }
      heading={heading || EMPTY_SCREEN.ERROR.MESSAGE}
      description={message || EMPTY_SCREEN.ERROR.SUB_MESSAGE}
      errorCode={errorCode}
      body={
        retryCallback ? (
          <Button
            label={buttonText || EMPTY_SCREEN.ERROR.RETRY}
            customClass={buttonClassName || styles.buttonStyle}
            emphasis="medium"
            size="small"
            onClick={() => onRefreshClick()}
          />
        ) : null
      }
      bodyCss={styles.body}
    />
  </div>
);

export default ErrorScreen;
