import cx from 'classnames';
import { Card } from '@paytm-h5-common/paytm_common_ui';
import { isDarkMode } from '../../../utils/commonUtil';
import styles from './FundTileView.module.scss';
import { FUND_TITLE_CONST } from './FundTileViewData';

const FundTileView = ({
  logoUrl,
  name,
  category,
  subCategory,
  returnsLabel,
  returnsValue,
  minAmount,
  aum,
  onClick,
}) => (
  <Card
    customClass={cx(styles.fundTileViewCard, {
      [styles.darkMode]: isDarkMode(),
    })}
    onClick={onClick}
  >
    <div
      className={cx(styles.fundNameContainer, {
        [styles.darkMode]: isDarkMode(),
      })}
    >
      <div
        className={cx(styles.logoWrapper, { [styles.darkMode]: isDarkMode() })}
      >
        <div
          className={cx(styles.logoInnerWrapper, {
            [styles.darkMode]: isDarkMode(),
          })}
        >
          {logoUrl ? (
            <img
              src={logoUrl}
              alt={`${name} Logo`}
              className={styles.logoImage}
            />
          ) : (
            <div className={styles.logoImage} />
          )}
        </div>
      </div>
      {/* Text */}
      <div className={styles.textContainer}>
        <p
          className={cx(styles.fundTitle, { [styles.darkMode]: isDarkMode() })}
        >
          {name}
        </p>
        <div className={styles.fundSubtitleContainer}>
          <p
            className={cx(styles.fundSubtitle, {
              [styles.darkMode]: isDarkMode(),
            })}
          >
            {category}
          </p>
          {subCategory && (
            <p
              className={cx(styles.fundSubtitleSeparator, {
                [styles.darkMode]: isDarkMode(),
              })}
            >
              ∙
            </p>
          )}
          {subCategory && (
            <p
              className={cx(styles.fundSubtitle, {
                [styles.darkMode]: isDarkMode(),
              })}
            >
              {subCategory}
            </p>
          )}
        </div>
      </div>
    </div>
    {/* Data */}
    <div className={styles.dataContainer}>
      {/* 01 */}
      <div className={styles.dataItem}>
        <p
          className={cx(styles.dataLabel, { [styles.darkMode]: isDarkMode() })}
        >
          {FUND_TITLE_CONST.MIN_SIP}
        </p>
        <div className={styles.dataValueContainer}>
          <div className={styles.dataValueInnerContainer}>
            <p
              className={cx(styles.dataValue, {
                [styles.darkMode]: isDarkMode(),
              })}
            >
              ₹{minAmount}
            </p>
          </div>
        </div>
      </div>
      <div className={styles.dataItem}>
        <p
          className={cx(styles.dataLabel, { [styles.darkMode]: isDarkMode() })}
        >
          {FUND_TITLE_CONST.AUM}
        </p>
        <div className={styles.dataValueContainer}>
          <div className={styles.dataValueInnerContainer}>
            <p
              className={cx(styles.dataValue, {
                [styles.darkMode]: isDarkMode(),
              })}
            >
              ₹{Math.floor(aum)} Cr
            </p>
          </div>
        </div>
      </div>
      <div
        className={cx(`${styles.dataItem} ${styles.returns}`, {
          [styles.darkMode]: isDarkMode(),
        })}
      >
        <p
          className={cx(styles.dataLabel, { [styles.darkMode]: isDarkMode() })}
        >
          {FUND_TITLE_CONST.RETURN} ({returnsLabel})
        </p>
        <p
          className={cx(styles.dataValue, {
            [styles.dataValuePositive]:
              returnsValue && returnsValue.startsWith('+'),
            [styles.dataValueNegative]:
              returnsValue && returnsValue.startsWith('-'),
            [styles.darkMode]: isDarkMode(),
          })}
        >
          {returnsValue}
        </p>
      </div>
    </div>
  </Card>
);

export default FundTileView;
