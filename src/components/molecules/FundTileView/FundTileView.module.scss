.fundTileViewCard {
  height: 100%;
  padding: 16px;
  background-color: var(--plain);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  &.darkMode {
    background-color: var(--plain);
  }
}

.fundNameContainer {
  align-self: stretch;
  display: flex;
  gap: 12px;
  border-bottom: 1px solid var(--background-offset-medium);
  padding-bottom: 16px;

  &.darkMode {
    border-bottom: 1px solid var(--grey200);
  }
}

.logoWrapper,
.logoInnerWrapper {
  width: 32px;
  height: 32px;
  background-color: var(--plain);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;

  &.darkMode {
    background-color: var(--grey900);
  }
}

.logoImage {
  width: 100%;
  height: 100%;
  position: relative;
  object-fit: contain;
}

.textContainer {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;

  p {
    margin: 0;
  }
}

.fundTitle {
  align-self: stretch;
  color: var(--text-neutral-strong);
  font-family: Inter;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0px;

  // &.darkMode {
  //   color: var(--text-neutral-strong-dark);
  // }
}

.fundSubtitleContainer {
  display: inline-flex;
  gap: 4px;

  p {
    margin: 0;
  }
}

.fundSubtitle {
  color: var(--text-neutral-medium);
  font-weight: normal;
  font-size: 12px;
  line-height: 16px;

  // &.darkMode {
  //   color: var(--text-neutral-weak-dark);
  // }
}

.fundSubtitleSeparator {
  color: var(--background-offset-weak);
  font-size: 12px;
  font-weight: normal;

  &.darkMode {
    color: var(--grey200);
  }
}

.dataContainer {
  align-self: stretch;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
}

.dataItem {
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 0;
  border: none;
  margin: 0;
  flex-shrink: 0;
  flex-grow: 1;
  flex-basis: 0;
  align-items: flex-start;
  text-align: left;

  p {
    margin: 0;
  }

  &:first-child {
    align-items: flex-start;
    text-align: left;
  }

  &:nth-child(2) {
    align-items: center;
    text-align: center;

    .dataLabel,
    .dataValueContainer {
      align-self: center;
    }
  }

  &.returns {
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: 0;
    align-items: flex-end;
    text-align: right;
    padding: 0;
    border: none;
    margin: 0;
  }
}

.dataLabel {
  align-self: auto;
  color: var(--text-neutral-medium);
  font-size: 12px;
  font-weight: normal;
  text-align: inherit;

  &.darkMode {
    color: var(--grey600);
  }
}

.dataValueContainer {
  display: flex;
  gap: 4px;
  align-self: auto;
  text-align: inherit;
  justify-content: center;
}

.dataValueInnerContainer {
  display: flex;
  gap: 4px;
}

.dataValue {
  color: var(--text-neutral-strong);
  font-size: 14px;
  font-weight: 500;
  text-align: inherit;

  // &.darkMode {
  //   color: var(--text-neutral-strong-dark);
  // }

  &.dataValuePositive {
    color: var(--text-positive-strong);

    &.darkMode {
      color: var(--text-positive-strong);
    }
  }

  &.dataValueNegative {
    color: var(--text-negative-strong);

    &.darkMode {
      color: var(--text-negative-strong);
    }
  }
}
