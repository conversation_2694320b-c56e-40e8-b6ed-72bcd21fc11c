@import '~@paytm-h5-common/paytm_common_ui/styles/colors';
@import '~@paytm-money/utils-frontend/styles/pml-typography';
@import '~@paytm-money/utils-frontend/styles/pml-mixins';

.genericWidget {
  width: 100%;
  border-radius: 12px;
  box-sizing: border-box;
  box-shadow: var(--shadow-xs);

  .content {
    display: flex;
    flex-direction: column;
    align-self: stretch;
    padding: 12px 12px 0;
    background: var(--plain);
    border-radius: 12px;
  }

  .fundInfo {
    min-height: 60px;
    display: inline-flex;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    padding-bottom: 12px;
  }

  .logoWrapper {
    width: 32px !important;
    height: 32px !important;

    img {
      width: 28px !important;
      height: 28px !important;
      border-radius: 50% !important;
      object-fit: cover !important;
    }
  }

  .details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1 1 0;
    min-width: 0;
  }

  .title {
    align-self: stretch;
    color: var(--grey900);
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    margin: 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;

    // &.darkMode {
    //   color: var(--text-neutral-strong-dark);
    // }
  }

  .category {
    display: inline-flex;
    gap: 4px;
    align-items: center;
  }

  .categoryText {
    // color: var(--categoryText);
    color: var(--grey500);
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0px;
    margin: 0;
  }

  .categorySeparator {
    color: var(--text-neutral-weak);
    @include fontStyle(
      $pml-typography-scale-subtext2-font-size,
      var(--text-neutral-weak),
      $pml-typography-weight-regular,
      $pml-typography-scale-subtext2-line-height
    );
    font-size: 12px;
    margin: 0;
  }

  .metrics {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
  }

  .duration {
    @include fontStyle(
      $pml-typography-scale-subtext2-font-size,
      var(--grey500),
      $pml-typography-weight-regular,
      $pml-typography-scale-subtext2-line-height
    );
    font-size: 12px;
    text-align: right;
    margin: 0;
  }

  .return {
    text-align: right;
    @include fontStyle(
      $pml-typography-scale-subtext2-font-size,
      var(--text-neutral-strong),
      $pml-typography-weight-semibold,
      $pml-typography-scale-subtext2-line-height
    );
    font-size: 12px;
    margin: 0;

    &__positive {
      color: var(--text-positive-strong);
    }

    &__negative {
      color: var(--text-negative-strong);
    }
  }
}

.stats {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px 8px 22px;
  background: var(--background-widget-blue);
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;

  &.darkMode {
    background: var(--grey100);

    svg {
      color: var(--grey900);
    }

    .stats__text {
      color: var(--grey900);
    }
  }

  svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    color: var(--text-neutral-strong);
  }

  img {
    width: 10px;
    height: 10px;
    object-fit: contain;
    display: block;
  }

  &__text {
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0px;
    color: var(--text-neutral-strong);
  }
}
