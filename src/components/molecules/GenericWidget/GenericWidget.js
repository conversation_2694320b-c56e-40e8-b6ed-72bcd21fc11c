import cx from 'classnames';
import upsideArrowGreen from '../../../assets/icons/upside-arrow-green.png';
import { isDarkMode } from '../../../utils/commonUtil';
import styles from './GenericWidget.module.scss';

const GenericWidget = ({
  logo,
  name,
  category,
  type,
  duration,
  returns,
  tickerTexts,
  onClick,
}) => {
  const isPositive = returns.startsWith('+');
  const isNegative = returns.startsWith('-');

  return (
    <div
      className={cx(styles.genericWidget, { [styles.darkMode]: isDarkMode() })}
      onClick={onClick}
    >
      <div className={cx(styles.content, { [styles.darkMode]: isDarkMode() })}>
        <div className={styles.fundInfo}>
          <div className={styles.logoWrapper}>
            <div className={styles.logo}>
              <img src={logo} alt={`${name} Logo`} className={styles.logoImg} />
            </div>
          </div>

          <div className={styles.details}>
            <p
              className={cx(styles.title, { [styles.darkMode]: isDarkMode() })}
            >
              {name}
            </p>
            <div className={styles.category}>
              <span
                className={cx(styles.categoryText, {
                  [styles.darkMode]: isDarkMode(),
                })}
              >
                {category}
              </span>
              <span
                className={cx(styles.categorySeparator, {
                  [styles.darkMode]: isDarkMode(),
                })}
              >
                ∙
              </span>
              <span
                className={cx(styles.categoryText, {
                  [styles.darkMode]: isDarkMode(),
                })}
              >
                {type}
              </span>
            </div>
          </div>

          <div className={styles.metrics}>
            <p
              className={cx(styles.duration, {
                [styles.darkMode]: isDarkMode(),
              })}
            >
              {duration}
            </p>
            <p
              className={cx(styles.return, {
                [styles.return__positive]: isPositive,
                [styles.return__negative]: isNegative,
                [styles.darkMode]: isDarkMode(),
              })}
            >
              {returns}
            </p>
          </div>
        </div>
      </div>

      {tickerTexts && tickerTexts.length > 0 && (
        <div className={cx(styles.stats, { [styles.darkMode]: isDarkMode() })}>
          <img
            src={upsideArrowGreen}
            alt="Upward Arrow"
            width={16}
            height={16}
            className={cx({ [styles.darkMode]: isDarkMode() })}
          />
          <span
            className={cx(styles.stats__text, {
              [styles.darkMode]: isDarkMode(),
            })}
          >
            {tickerTexts[0]}
          </span>
        </div>
      )}
    </div>
  );
};

export default GenericWidget;
