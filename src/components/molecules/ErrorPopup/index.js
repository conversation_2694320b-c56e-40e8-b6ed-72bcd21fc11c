
import { Button } from '@paytm-h5-common/paytm_common_ui';
import { DismissPopup } from '@paytm-money/utils-frontend';

import { useEffect } from 'react';
import { useAppStore } from '../../../contexts/AppContextProvider';
import { emptyObj } from '../../../utils/commonUtil';
import { API_ERROR } from '../../../utils/constants';
import { useBackPress } from '../../../hooks/useNativeBackPress';
import styles from './index.scss';

const ErrorPopup = (errorData) => {
  const { setRootError } = useAppStore() || emptyObj;

  const { popStack, pushStack } = useBackPress();

  function handleCta() {
    if (errorData?.callback) {
      const { callback } = errorData;
      callback();
    }
    setRootError(false);
  }

  useEffect(() => {
    if (errorData) {
      pushStack(() => {
        setRootError(false);
      });
    }
  }, [errorData]);

  return (
    <DismissPopup isOpen={errorData} enableBackDropClick onClose={popStack}>
      <div className={styles.ErrorWrapper}>
        {errorData?.mainMsg && <h3>{errorData?.mainMsg}</h3>}
        <p>
          {errorData?.message ||
            errorData?.Message ||
            errorData?.error ||
            errorData?.meta?.displayMessage ||
            API_ERROR.MESSAGE}
        </p>

        {/* {errorData?.code || mapErrorCode(errorData?.meta) ? (
          <p className={styles.code}>
            {API_ERROR.ERROR_CODE(
              errorData?.code || mapErrorCode(errorData?.meta),
            )}
          </p>
        ) : null} */}

        {!errorData?.hideBtn && (
          <div className={styles.btn}>
            <Button
              label={errorData?.btnTxt || API_ERROR.OK}
              onClick={handleCta}
            />
          </div>
        )}
      </div>
    </DismissPopup>
  );
};

export default ErrorPopup;
