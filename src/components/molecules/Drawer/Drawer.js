import { BottomSheet } from '@paytm-h5-common/paytm_common_ui';
import { useEffect } from 'react';
import { useBackPress } from '../../../hooks/useNativeBackPress';
import { useDrawer } from './useDrawer';
import Icon, { ICONS_NAME } from '../Icon';
import styles from './index.scss';
import { log } from '../../../utils/commonUtil';

const Drawer = ({
  children,
  active,
  triggerClose,
  showCustomCloseIcon,
  ...props
}) => {
  const { pushStack, popStack } = useBackPress();

  useEffect(() => {
    log('drawer-state, triggerClose', active, triggerClose);
    if (active) {
      document.body.style.overflow = 'hidden';
      pushStack(triggerClose);
    } else {
      document.body.style.overflow = 'initial';
    }
    return () => {
      document.body.style.overflow = 'initial';
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [active]);

  return (
    <BottomSheet active={active} triggerClose={popStack} {...props}>
      {showCustomCloseIcon && (
        <Icon
          className={styles.customCloseIcon}
          name={ICONS_NAME.DRAWER_CLOSE_ICON}
          width="24px"
          onClick={triggerClose}
        />
      )}
      {children}
    </BottomSheet>
  );
};

export default Drawer;
export { useDrawer };
