import { useRef } from 'react';
import Slider from 'react-slick';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import styles from './index.scss';
import useSwipeDetector from '../../../hooks/useSwipeDetector';

let slider;
// const IndicatorList = (dots) => (
//   <div>
//     <ul className={styles.IndicatorList}>{dots}</ul>
//   </div>
// );

const Indicator = (e) => (
  <div className={styles.indicatorContainer}>
    <div
      className={styles.indicator}
      role="button"
      aria-label={`go to slide ${e + 1}`}
    />
  </div>
);

const Carousel = ({ children, customSetting = {}, onSwipeCallback }) => {
  slider = useRef();

  const handleSwipe = () => {
    onSwipeCallback();
  };
  const swipeRef = useSwipeDetector(handleSwipe);

  const settings = {
    dots: false,
    // eslint-disable-next-line react/jsx-no-useless-fragment
    nextArrow: <></>,
    // eslint-disable-next-line react/jsx-no-useless-fragment
    prevArrow: <></>,
    infinite: false,
    speed: 500,
    slidesToShow: 2,
    slidesToScroll: 2,
    // dotsClass: `slick-dots ${styles.dots} ${customStyle}`,
    // appendDots: IndicatorList,
    customPaging: (e) => Indicator(e),
    ...customSetting,
  };

  return (
    <div ref={swipeRef} className={styles.banner}>
      <Slider ref={slider} {...settings}>
        {children}
      </Slider>
    </div>
  );
};

const resetSlick = () => {
  slider.current.slickGoTo(0);
};

export default Carousel;
export { resetSlick };
