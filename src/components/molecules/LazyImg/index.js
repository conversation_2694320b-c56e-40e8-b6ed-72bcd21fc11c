import { useRef, useEffect, useState } from 'react';
import cx from 'classnames';

import { SHIMMER_TYPE } from '../../../utils/constants';
import Shimmer from '../../atoms/Shimmer';

import styles from './index.scss';

const LazyImg = ({
  className = '',
  src = '',
  alt = '',
  style = {},
  onLoad = () => {},
  onError = () => {},
  shimmerType,
  containerClassName = '',
  byPass = false,
  onClick = () => {},
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const imgRef = useRef();

  useEffect(() => {
    if (imgRef.current && !byPass) {
      const observerT = new IntersectionObserver(
        (entries, observer) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const imageDiv = entry.target;
              const image = imageDiv.getElementsByTagName('img')[0];
              image.src = image.dataset.src;
              image.onload = () => {
                setIsLoading(false);
                onLoad();
              };
              image.onerror = () => {
                setIsError(true);
                onError();
              };
              observer.unobserve(image);
            }
          });
        },
        {
          root: null,
          rootMargin: '0px 0px 0px 0px',
          threshold: 0,
        },
      );
      observerT.observe(imgRef.current);
    }
  }, [imgRef, imgRef.current, byPass, src]);

  if (byPass) {
    return (
      <img
        src={src}
        alt={alt}
        className={className}
        style={style}
        onLoad={onLoad}
      />
    );
  }

  const getShimmer = () => {
    switch (shimmerType) {
      case SHIMMER_TYPE.STOCK_SHIMMER: {
        return <Shimmer height="46px" width="50%" />;
      }
      case SHIMMER_TYPE.EMPTY_SCREEN: {
        return <Shimmer type="circle" width="193px" />;
      }
      case SHIMMER_TYPE.ROUND_BUTTONS: {
        return <Shimmer type="circle" width="45px" />;
      }
      case SHIMMER_TYPE.STOCK_THEME: {
        return <Shimmer width="75px" type="circle" />;
      }
      case SHIMMER_TYPE.ORDER_CONFIRMATION: {
        return <Shimmer type="line" width="270px" height="200px" />;
      }
      default:
    }
    <div>loading</div>;
  };
  return (
    <div
      className={cx(styles.lazyImg, containerClassName)}
      ref={imgRef}
      onClick={onClick}
    >
      <img
        data-src={src}
        alt={alt}
        className={cx(className, {
          [styles.displayNone]: isLoading,
          [styles.displayBlock]: !isLoading,
        })}
        style={style}
      />
      {isLoading || isError ? getShimmer() : null}
    </div>
  );
};

export default LazyImg;
