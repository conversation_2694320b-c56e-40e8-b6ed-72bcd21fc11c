import React, { useEffect, useState } from 'react';
import Drawer from '../Drawer/Drawer';
import Button from '../../atoms/Button/Button';
import styles from './DateSelectionPopup.module.scss';
import { getOrdinalSuffix } from '../../../utils/numbers';
import Icon, { ICONS_NAME } from '../Icon';
import checkIconLight from '../../../assets/icons/checklight.svg';
import checkIconDark from '../../../assets/icons/checkdark.svg';
import { isDarkMode } from '../../../utils/commonUtil';
const DateSelectionPopup = ({
  isOpen,
  onConfirmDate,
  onDateChange,
  onClose,
  defaultDate,
}) => {
  const [selectedDate, setSelectedDate] = useState();
  const handleClose = () => {
    onClose();
  };

  const handleDateChange = (date) => {
    setSelectedDate(date);
    onDateChange(date);
  };

  useEffect(() => {
    if (!isOpen) {
      setSelectedDate(null);
    } else {
      setSelectedDate(defaultDate);
    }
  }, [isOpen]);

  const dates = Array.from({ length: 28 }, (_, i) => i + 1);

  return (
    <Drawer
      active={isOpen}
      triggerClose={handleClose}
      showGrabber
      showCloseIcon={false}
      customClass={styles.drawer}
    >
      <div className={styles['content-container']}>
        <div className={styles['header-container']}>
          <h4 className={styles.title}>Select SIP Date</h4>
          <Icon
            name={ICONS_NAME.CLOSE_CIRCLE}
            className={styles.closeIcon}
            size="32px"
            onClick={handleClose}
          />
        </div>

        <div className={styles['selected-date']}>
          <span className={styles.label}>Invest Monthly:</span>
          <span className={styles.date}>
            {selectedDate}
            {getOrdinalSuffix(selectedDate)}
          </span>
        </div>
        <div className={styles['dates-container']}>
          {dates.map((date) => (
            <button
              key={date}
              className={`${styles['date-button']} ${styles[date === selectedDate ? 'selected' : '']}`}
              onClick={() => handleDateChange(date)}
            >
              {date}
            </button>
          ))}
        </div>
        <Button
          className={styles.confirmButton}
          onClickHandler={() => {
            onConfirmDate(selectedDate);
          }}
          size="large"
          buttonText="Confirm"
          image={isDarkMode() ? checkIconDark : checkIconLight}
        />
      </div>
    </Drawer>
  );
};

export default DateSelectionPopup;
