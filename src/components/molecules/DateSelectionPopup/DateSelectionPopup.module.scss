@import '/src/commonStyles/variables.scss';
@import '/src/commonStyles//mixins.scss';

.content-container {
  & * {
    margin: 0px;
    padding: 0px;
  }
}

.drawer {
  background-color: var(--popup) !important;
}

.header-container {
  @include flex($justifyContent: space-between, $alignitems: center);
  margin: 16px 0px 16px 0px;
}

.title {
  @include fontStyle(
    $size: 24px,
    $weight: 700,
    $lineheight: 32px,
    $letterSpacing: -0.01px
  );
  color: var(--grey900);
}

.selected-date {
  background-color: var(--primaryOffsetVariant);
  margin-bottom: 24px;
  padding: 8px 0px;
  text-align: center;
  border-radius: 8px;

  .label {
    @include fontStyle(
      $size: 14px,
      $weight: 400,
      $lineheight: 20px,
      $letterSpacing: 0px
    );
    margin-right: 4px;
    color: var(--grey500);
  }

  .date {
    @include fontStyle(
      $size: 14px,
      $weight: 500,
      $lineheight: 20px,
      $letterSpacing: 0px
    );
    color: var(--grey900);
  }
}

.dates-container {
  @include flex($justifyContent: center, $wrap: wrap, $gap: 8px);
  margin-bottom: 24px;
}

.date-button {
  background: none;
  border: none;
  color: var(--grey900);
  width: calc(100% / 8.5);
  @include fontStyle(
    $size: 16px,
    $weight: 400,
    $lineheight: 22px,
    $letterSpacing: 0.01px
  );
  text-align: center;
  padding: 7px;
  border-radius: 18px;

  &.selected {
    background-color: var(--linksSelections);
    color: var(--silverForever);
  }
}

.confirmButton {
  background: var(--primary);
  color: var(--silver900);
  border-radius: 48px;
  padding: 15px 12px;
  & * {
    margin-right: 4px;
  }
}
