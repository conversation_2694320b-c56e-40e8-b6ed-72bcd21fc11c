import { Button } from '@paytm-h5-common/paytm_common_ui';

import CenterImageLayout from '../../atoms/CenterImageLayout';

import { EMPTY_SCREEN } from '../../../utils/constants';
import NoInternetMiniIcon from '../../../assets/noInternetMini.png';

import styles from './index.scss';

const NoInternetScreen = ({ onRefreshClick }) => (
  <div className={styles.root}>
    <CenterImageLayout
      img={<img className={styles.icon} src={NoInternetMiniIcon} alt="" />}
      heading={EMPTY_SCREEN.NO_INTERNET.MESSAGE}
      description={EMPTY_SCREEN.NO_INTERNET.SUB_MESSAGE}
      body={
        <Button
          label={EMPTY_SCREEN.NO_INTERNET.RETRY}
          size="small"
          emphasis="medium"
          onClick={onRefreshClick}
        />
      }
      bodyCss={styles.body}
    />
  </div>
);

export default NoInternetScreen;
