import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import s from './InputBox.scss';

function InputBox(props) {
  const {
    type,
    value,
    readOnly,
    required,
    maxLength,
    onClickHandler,
    onKeyDownHandler,
    placeholderString,
    onChangeHandler,
    customStyles,
    label,
    showFocusBar,
    inputMode,
    customInputBoxStyle,
    onBlurHandler,
    autoCapitalize,
    inputErr,
    showCustomColors,
    pattern,
    optionalField,
    reference = null,
    ...rest
  } = props;

  const inputRef = reference || useRef(null);
  const customClass =
    value === '' && !optionalField
      ? s.emptyFieldColor
      : inputErr
      ? s.errorFieldColor
      : s.normalColor;

  return (
    <div
      className={cx(s.group, customStyles, { [customClass]: showCustomColors })}
    >
      {label && <label className={s.labelStyles}>{label}</label>}
      <input
        ref={inputRef}
        type={type}
        value={value}
        readOnly={readOnly}
        required={required}
        maxLength={maxLength}
        onClick={onClickHandler}
        onKeyDown={onKeyDownHandler}
        onChange={onChangeHandler}
        placeholder={placeholderString}
        inputMode={inputMode}
        style={customInputBoxStyle}
        onBlur={onBlurHandler}
        autoCapitalize={autoCapitalize}
        pattern={pattern}
        {...rest}
      />
      {showFocusBar && <i className={cx(s.bar, { [s.errorBar]: inputErr })} />}
    </div>
  );
}
InputBox.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  type: PropTypes.string,
  maxLength: PropTypes.string,
  placeholderString: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  label: PropTypes.string,
  customStyles: PropTypes.string,
  inputMode: PropTypes.string,
  pattern: PropTypes.string,
  customInputBoxStyle: PropTypes.objectOf(PropTypes.any),
  required: PropTypes.bool,
  readOnly: PropTypes.bool,
  showFocusBar: PropTypes.bool,
  optionalField: PropTypes.bool,
  autoCapitalize: PropTypes.string,
  onClickHandler: PropTypes.func,
  onKeyDownHandler: PropTypes.func,
  onChangeHandler: PropTypes.func,
  onBlurHandler: PropTypes.func,
};

InputBox.defaultProps = {
  value: '',
  type: 'text',
  maxLength: '30',
  customStyles: '',
  label: null,
  required: false,
  readOnly: false,
  showFocusBar: false,
  optionalField: false,
  autoCapitalize: 'on',
  placeholderString: '',
  inputMode: 'text ',
  pattern: 'none',
  onClickHandler: () => {},
  onKeyDownHandler: event => {
    if (event && event.keyCode === 13) {
      event.preventDefault();
      if (event?.target?.blur) {
        event.target.blur();
      }
    }
  },
  onChangeHandler: () => {},
  onBlurHandler: () => {},
  customInputBoxStyle: {},
};

export default InputBox;
