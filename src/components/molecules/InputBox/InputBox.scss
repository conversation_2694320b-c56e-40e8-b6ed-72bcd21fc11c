::placeholder {
  font-size: 16px;
  color: #adafb6;
}

.group {
  position: relative;
  input {
    box-shadow: none;
    background-color: transparent;
    &:focus ~ .bar::before {
      width: 100%;
      left: 0;
    }
  }
}
.group input[type="number"],
.group input[type="password"],
.group input[type="tel"],
.group input[type="text"],
.group select {
  width: 100%;
  font-size: 16px;
  font-weight: bold;
  color: #494949;
  padding: 0 0 7px 0;
  border: none;
  border-bottom: 1px solid #eeeef0;
  box-sizing: border-box;
  outline-style: none;
  .normalColor {
    color: #1d2f54; // This is for KYC module
  }
  .emptyFieldColor {
    color: #8ba6c1; // This is for KYC module
  }
  .errorFieldColor {
    color: #fb5252; // This is for KYC module
  }
}
.labelStyles {
  display: block;
  margin-bottom: 10px;
}
.bar {
  display: block;
  position: relative;

  &:before {
    -moz-transition: 0.2s ease all;
    -webkit-transition: 0.2s ease all;
    position: absolute;
    background: #00c1f2;
    bottom: 0;
    content: "";
    height: 2px;
    left: 50%;
    transition: 0.2s ease all;
    width: 0;
  }

  &:after {
    -moz-transition: 0.2s ease all;
    -webkit-transition: 0.2s ease all;
    position: absolute;
    background: #00c1f2;
    bottom: 1px;
    content: "";
    height: 2px;
    right: 50%;
    transition: 0.2s ease all;
    width: 0;
  }
}
.errorBar {
  &:before, &:after {
    background: #fb5252;
  }

}
