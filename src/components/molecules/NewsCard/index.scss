.newsFeedContent {
  width: calc(100vw - 28px);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 24px;
  font-weight: bold;

  .carouselContainer {
    display: flex;
    width: 100%;

    .carouselSlide {
      display: flex;
      flex-direction: column;
      gap: 8px;
      width: 100%;
      padding-right: 0;

      .cardWrapper {
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(0, 116, 255, 0.2);
        user-select: none;
        // margin: 0 16px;
        // width: calc(100vw - 32px);
        width: 100%;
      }

      .newsCard {
        padding: 16px;
        background: var(--surface-level-1);
        border-radius: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 8px;
        transition: all 0.3s ease-in-out;
        cursor: pointer;
        transform: translateZ(0);
        height: fit-content;
        margin: 0;

        .newsCardHeader {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          height: 24px;

          .companyInfo {
            display: flex;
            align-items: center;
            gap: 6px;
            min-width: 0;
            flex: 1;
            max-width: 80%;

            .companyLogo {
              padding: 1px;
              // background: rgba(255, 255, 255, 0.95);
              border-radius: 2px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              width: 18px;
              height: 18px;

              img {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }
            }

            .companyName {
              color: var(--text-neutral-strong);
              font-size: 14px;
              font-weight: 500;
              line-height: 20px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              min-width: 0;
            }
          }

          :global(.badge) {
            padding: 2px 6px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            line-height: 16px;
            height: 20px;
            display: flex;
            align-items: center;
            margin-left: 8px;
          }

          .positiveBadge {
            background: var(
              --BG-profitAndSuccessMedium,
              rgba(227, 246, 236, 1)
            );
            color: var(--Text-profitAndSuccess, rgba(44, 176, 121, 1));
            height: 20px;
            padding: 2px 6px;
          }

          .negativeBadge {
            background: rgba(254, 226, 226, 0.95);
            color: var(--Text-lossAndError, rgba(235, 75, 75, 1));
            height: 20px;
            padding: 2px 6px;
          }

          .positiveBadgeDark {
            background: #0c3620;
            color: #02a85d;
            height: 20px;
            padding: 2px 6px;
          }

          .negativeBadgeDark {
            background: #40111b;
            color: rgba(235, 75, 75, 1);
            height: 20px;
            padding: 2px 6px;
          }
        }

        .newsContent {
          display: flex;
          flex-direction: column;
          gap: 6px;

          .newsText {
            color: var(--text-neutral-medium);
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            height: 40px;
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .timestamp {
            color: var(--text-neutral-weak);
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
          }
        }
      }
    }
  }
}

.fullNewsFeedContent {
  width: calc(100vw - 16px);
}
