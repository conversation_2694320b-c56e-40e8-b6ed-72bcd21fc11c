import cx from 'classnames';
import { Card } from '@paytm-h5-common/paytm_common_ui';
import FallbackCompanyIcon from '@assets/icons/company_fallback_icon.svg';
import CompanyIcon from '../../atoms/CompanyIcon/CompanyIcon';
import StockChange from '../../organisms/StockChange/StockChange';
import { isDarkMode } from '../../../utils/commonUtil';
import styles from './index.scss';
import Shimmer from '../../atoms/Shimmer';

const NewsCard = ({
  item,
  eventTrigger = () => {},
  handleCardClick = () => {},
  isFullPage = false,
}) => (
  <div
    className={cx(styles.newsFeedContent, {
      [styles.fullNewsFeedContent]: isFullPage,
    })}
  >
    <div className={styles.carouselContainer}>
      <div key={item.id} className={styles.carouselSlide}>
        <div key={item.id} className={styles.cardWrapper}>
          <Card
            customClass={styles.newsCard}
            onClick={(e) => {
              eventTrigger({ label: item?.pml_id || 'NA' });
              handleCardClick(item, e);
            }}
          >
            <div className={styles.newsCardHeader}>
              <div className={styles.companyInfo}>
                <div className={styles.companyLogo}>
                  <CompanyIcon
                    name={item.pml_id}
                    className={styles.companyLogo}
                    type="stocks"
                    url={item.companyLogo}
                    fallbackImg={FallbackCompanyIcon}
                  />
                </div>
                <span className={styles.companyName}>{item.companyName}</span>
              </div>
              <StockChange
                exchange={item.exchange}
                segment={item.segment}
                securityId={item.security_id}
                instrumentType={item.instrument}
                id={item.pml_id}
                isBadge
                showLtp
                stylesObj={{
                  positiveBadge: isDarkMode()
                    ? styles.positiveBadgeDark
                    : styles.positiveBadge,
                  negativeBadge: isDarkMode()
                    ? styles.negativeBadgeDark
                    : styles.negativeBadge,
                }}
              />
            </div>
            <div className={styles.newsContent}>
              <div className={styles.newsText}>{item.news}</div>
              <div className={styles.timestamp}>{item.timestamp}</div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  </div>
);

const NewsCardLoading = ({ isFullPage = false }) => (
  <div
    className={cx(styles.newsFeedContent, {
      [styles.fullNewsFeedContent]: isFullPage,
    })}
  >
    <div className={styles.carouselContainer}>
      <div className={styles.carouselSlide}>
        <div className={styles.cardWrapper}>
          <Card customClass={styles.newsCard}>
            <div className={styles.newsCardHeader}>
              <Shimmer type="line" width="40%" height="20px" />
              <Shimmer type="line" width="15%" height="20px" />
            </div>
            <div className={styles.newsContent}>
              <Shimmer type="line" width="100%" height="20px" />
              <Shimmer type="line" width="20%" height="20px" />
            </div>
          </Card>
        </div>
      </div>
    </div>
  </div>
);

export default NewsCard;
export { NewsCardLoading };
