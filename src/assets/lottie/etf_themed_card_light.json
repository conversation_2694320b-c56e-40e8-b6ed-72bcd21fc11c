{"v": "5.12.1", "fr": 30, "ip": 45, "op": 121, "w": 344, "h": 340, "nm": "Time remap", "ddd": 0, "assets": [{"id": "comp_0", "nm": "Add funds", "fr": 30, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Coin 14", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 95, "s": [100]}, {"t": 108, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [0]}, {"t": 184, "s": [540]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [327]}, {"t": 167, "s": [327]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.728]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [-36]}, {"t": 167, "s": [328]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1, 1, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [0]}, {"t": 109, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 61, "op": 78, "st": 61, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Coin 13", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 92, "s": [100]}, {"t": 105, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [0]}, {"t": 172, "s": [540]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [28]}, {"t": 148, "s": [28]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.731]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [-36]}, {"t": 148, "s": [308]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1, 1, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [0]}, {"t": 97, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 49, "op": 78, "st": 49, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "Coin 12", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 88, "s": [100]}, {"t": 101, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [0]}, {"t": 205, "s": [360]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [252]}, {"t": 115, "s": [252]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.844]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [-91]}, {"t": 115, "s": [268]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [2, 2, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [0]}, {"t": 103, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 55, "op": 78, "st": 55, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "Coin 11", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 82, "s": [100]}, {"t": 95, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 41, "s": [0]}, {"t": 191, "s": [360]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 41, "s": [172]}, {"t": 115, "s": [172]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.807]}, "o": {"x": [0.167], "y": [0.167]}, "t": 41, "s": [-91]}, {"t": 115, "s": [268]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1.5, 1.5, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 54, "s": [0]}, {"t": 102, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 41, "op": 78, "st": 41, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "Coin 10", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 94, "s": [100]}, {"t": 107, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66, "s": [0]}, {"t": 189, "s": [720]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66, "s": [118]}, {"t": 137, "s": [118]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.813]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66, "s": [-36]}, {"t": 137, "s": [318]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-1.5, -1.5, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66, "s": [0]}, {"t": 114, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 66, "op": 78, "st": 66, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "Coin 9", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 74, "s": [100]}, {"t": 87, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"t": 173, "s": [720]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [88]}, {"t": 110, "s": [88]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.842]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [-36]}, {"t": 110, "s": [318]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-2, -2, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"t": 98, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 50, "op": 78, "st": 50, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "Coin 8", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [100]}, {"t": 73, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [0]}, {"t": 182, "s": [360]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [267]}, {"t": 108, "s": [267]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.815]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [-76]}, {"t": 108, "s": [308]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-1.5, -1.5, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [0]}, {"t": 80, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 32, "op": 78, "st": 32, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "Coin 5", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 63, "s": [100]}, {"t": 76, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 29, "s": [0]}, {"t": 152, "s": [540]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 29, "s": [327]}, {"t": 135, "s": [327]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.728]}, "o": {"x": [0.167], "y": [0.167]}, "t": 29, "s": [-36]}, {"t": 135, "s": [328]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1, 1, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 29, "s": [0]}, {"t": 77, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 29, "op": 4250, "st": 29, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "Coin 4", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [100]}, {"t": 73, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 17, "s": [0]}, {"t": 140, "s": [540]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 17, "s": [28]}, {"t": 116, "s": [28]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.731]}, "o": {"x": [0.167], "y": [0.167]}, "t": 17, "s": [-36]}, {"t": 116, "s": [308]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1, 1, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 17, "s": [0]}, {"t": 65, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 17, "op": 4238, "st": 17, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "Coin 7", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [100]}, {"t": 69, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"t": 173, "s": [360]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [252]}, {"t": 83, "s": [252]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.844]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [-91]}, {"t": 83, "s": [268]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [2, 2, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"t": 71, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 23, "op": 4244, "st": 23, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "Coin", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [100]}, {"t": 63, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [0]}, {"t": 159, "s": [360]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [172]}, {"t": 83, "s": [172]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.807]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [-91]}, {"t": 83, "s": [268]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1.5, 1.5, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"t": 70, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 9, "op": 4230, "st": 9, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "Coin 6", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 62, "s": [100]}, {"t": 75, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [0]}, {"t": 157, "s": [720]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [118]}, {"t": 105, "s": [118]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.813]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [-36]}, {"t": 105, "s": [318]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-1.5, -1.5, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [0]}, {"t": 82, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 34, "op": 4255, "st": 34, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 0, "nm": "Coin 3", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 55, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [0]}, {"t": 141, "s": [720]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [88]}, {"t": 78, "s": [88]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.842]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [-36]}, {"t": 78, "s": [318]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-2, -2, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [0]}, {"t": 66, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 18, "op": 4239, "st": 18, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 0, "nm": "Coin 2", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [100]}, {"t": 41, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 150, "s": [360]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [267]}, {"t": 76, "s": [267]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.991], "y": [0.815]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [-76]}, {"t": 76, "s": [308]}], "ix": 4}}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-1.5, -1.5, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 48, "s": [1.6]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}, "w": 1000, "h": 1000, "ip": 0, "op": 4221, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Star 13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [79.416, 47.646, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [244.472, 60.409, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.42, 0.42, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.636, 0.636, 0.333], "y": [0, 0, 0]}, "t": 71, "s": [150, 150, 100]}, {"t": 76, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.512, -0.201], [0, 0], [0.274, -1.421], [0, 0], [0, 0], [1.377, 0.273], [0, 0], [0, 0], [-0.257, 1.439]], "o": [[0, 0], [0.284, 1.593], [0, 0], [-1.319, 0.348], [0, 0], [0, 0], [-0.31, -1.449], [0, 0], [0, 0], [1.336, -0.347], [0, 0]], "v": [[-0.044, -3.18], [-0.019, -3.063], [2.992, -0.052], [2.696, 0.028], [0.095, 2.919], [0.044, 3.18], [-0.006, 2.928], [-2.789, 0.088], [-2.992, 0.052], [-2.696, -0.029], [-0.086, -2.964]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [244.472, 60.409], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 66, "op": 77, "st": 66, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Star 11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [195.541, 88.771, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [244.472, 60.409, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.42, 0.42, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 62, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.636, 0.636, 0.333], "y": [0, 0, 0]}, "t": 67, "s": [180, 180, 100]}, {"t": 72, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.512, -0.201], [0, 0], [0.274, -1.421], [0, 0], [0, 0], [1.377, 0.273], [0, 0], [0, 0], [-0.257, 1.439]], "o": [[0, 0], [0.284, 1.593], [0, 0], [-1.319, 0.348], [0, 0], [0, 0], [-0.31, -1.449], [0, 0], [0, 0], [1.336, -0.347], [0, 0]], "v": [[-0.044, -3.18], [-0.019, -3.063], [2.992, -0.052], [2.696, 0.028], [0.095, 2.919], [0.044, 3.18], [-0.006, 2.928], [-2.789, 0.088], [-2.992, 0.052], [-2.696, -0.029], [-0.086, -2.964]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [244.472, 60.409], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 62, "op": 73, "st": 62, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Star 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [299.416, 23.896, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [244.472, 60.409, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.42, 0.42, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 56, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.636, 0.636, 0.333], "y": [0, 0, 0]}, "t": 61, "s": [75, 75, 100]}, {"t": 66, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.512, -0.201], [0, 0], [0.274, -1.421], [0, 0], [0, 0], [1.377, 0.273], [0, 0], [0, 0], [-0.257, 1.439]], "o": [[0, 0], [0.284, 1.593], [0, 0], [-1.319, 0.348], [0, 0], [0, 0], [-0.31, -1.449], [0, 0], [0, 0], [1.336, -0.347], [0, 0]], "v": [[-0.044, -3.18], [-0.019, -3.063], [2.992, -0.052], [2.696, 0.028], [0.095, 2.919], [0.044, 3.18], [-0.006, 2.928], [-2.789, 0.088], [-2.992, 0.052], [-2.696, -0.029], [-0.086, -2.964]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [244.472, 60.409], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 56, "op": 67, "st": 56, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Star 12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [149.041, 22.771, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [244.472, 60.409, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.42, 0.42, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 76, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.636, 0.636, 0.333], "y": [0, 0, 0]}, "t": 81, "s": [180, 180, 100]}, {"t": 86, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.512, -0.201], [0, 0], [0.274, -1.421], [0, 0], [0, 0], [1.377, 0.273], [0, 0], [0, 0], [-0.257, 1.439]], "o": [[0, 0], [0.284, 1.593], [0, 0], [-1.319, 0.348], [0, 0], [0, 0], [-0.31, -1.449], [0, 0], [0, 0], [1.336, -0.347], [0, 0]], "v": [[-0.044, -3.18], [-0.019, -3.063], [2.992, -0.052], [2.696, 0.028], [0.095, 2.919], [0.044, 3.18], [-0.006, 2.928], [-2.789, 0.088], [-2.992, 0.052], [-2.696, -0.029], [-0.086, -2.964]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [244.472, 60.409], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 76, "op": 78, "st": 76, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Star 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [279.416, 76.396, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [244.472, 60.409, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.42, 0.42, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 69, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.636, 0.636, 0.333], "y": [0, 0, 0]}, "t": 74, "s": [150, 150, 100]}, {"t": 79, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.512, -0.201], [0, 0], [0.274, -1.421], [0, 0], [0, 0], [1.377, 0.273], [0, 0], [0, 0], [-0.257, 1.439]], "o": [[0, 0], [0.284, 1.593], [0, 0], [-1.319, 0.348], [0, 0], [0, 0], [-0.31, -1.449], [0, 0], [0, 0], [1.336, -0.347], [0, 0]], "v": [[-0.044, -3.18], [-0.019, -3.063], [2.992, -0.052], [2.696, 0.028], [0.095, 2.919], [0.044, 3.18], [-0.006, 2.928], [-2.789, 0.088], [-2.992, 0.052], [-2.696, -0.029], [-0.086, -2.964]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [244.472, 60.409], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 69, "op": 78, "st": 69, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Star 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [54.416, 84.146, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [244.472, 60.409, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.42, 0.42, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 50, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.636, 0.636, 0.333], "y": [0, 0, 0]}, "t": 55, "s": [100, 100, 100]}, {"t": 60, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.512, -0.201], [0, 0], [0.274, -1.421], [0, 0], [0, 0], [1.377, 0.273], [0, 0], [0, 0], [-0.257, 1.439]], "o": [[0, 0], [0.284, 1.593], [0, 0], [-1.319, 0.348], [0, 0], [0, 0], [-0.31, -1.449], [0, 0], [0, 0], [1.336, -0.347], [0, 0]], "v": [[-0.044, -3.18], [-0.019, -3.063], [2.992, -0.052], [2.696, 0.028], [0.095, 2.919], [0.044, 3.18], [-0.006, 2.928], [-2.789, 0.088], [-2.992, 0.052], [-2.696, -0.029], [-0.086, -2.964]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [244.472, 60.409], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 50, "op": 61, "st": 50, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Star 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [149.041, 22.771, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [244.472, 60.409, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.42, 0.42, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.636, 0.636, 0.333], "y": [0, 0, 0]}, "t": 49, "s": [180, 180, 100]}, {"t": 54, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.512, -0.201], [0, 0], [0.274, -1.421], [0, 0], [0, 0], [1.377, 0.273], [0, 0], [0, 0], [-0.257, 1.439]], "o": [[0, 0], [0.284, 1.593], [0, 0], [-1.319, 0.348], [0, 0], [0, 0], [-0.31, -1.449], [0, 0], [0, 0], [1.336, -0.347], [0, 0]], "v": [[-0.044, -3.18], [-0.019, -3.063], [2.992, -0.052], [2.696, 0.028], [0.095, 2.919], [0.044, 3.18], [-0.006, 2.928], [-2.789, 0.088], [-2.992, 0.052], [-2.696, -0.029], [-0.086, -2.964]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [244.472, 60.409], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 45, "op": 55, "st": 44, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "Star 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [279.416, 76.396, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [244.472, 60.409, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.42, 0.42, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 37, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.636, 0.636, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [150, 150, 100]}, {"t": 47, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.512, -0.201], [0, 0], [0.274, -1.421], [0, 0], [0, 0], [1.377, 0.273], [0, 0], [0, 0], [-0.257, 1.439]], "o": [[0, 0], [0.284, 1.593], [0, 0], [-1.319, 0.348], [0, 0], [0, 0], [-0.31, -1.449], [0, 0], [0, 0], [1.336, -0.347], [0, 0]], "v": [[-0.044, -3.18], [-0.019, -3.063], [2.992, -0.052], [2.696, 0.028], [0.095, 2.919], [0.044, 3.18], [-0.006, 2.928], [-2.789, 0.088], [-2.992, 0.052], [-2.696, -0.029], [-0.086, -2.964]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [244.472, 60.409], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 45, "op": 48, "st": 37, "ct": 1, "bm": 0}]}, {"id": "comp_1", "nm": "Cut", "fr": 30, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "10X", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 24, "op": 49, "st": -58, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "10X", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 25, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_2", "nm": "10X", "fr": 30, "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "Highlight 8", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 75, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [527.5, 508.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46.95, 47.55, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1000, 1000, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[6, 0.1], [5.6, 2.2], [4.3, 4.2], [2.3, 5.5], [0, 6], [-8.6, 8.7], [-12.2, 0], [-8.6, -8.6], [0, -12.2], [2.3, -5.5], [4.3, -4.2], [5.6, -2.2]], "o": [[-6, 0.1], [-5.6, -2.2], [-4.3, -4.2], [-2.3, -5.5], [0, -12.1], [8.6, -8.6], [12.2, 0], [8.6, 8.6], [0, 6], [-2.3, 5.5], [-4.3, 4.2], [-5.7, 2.2]], "v": [[44.212, 92.1], [26.712, 89], [11.812, 79.3], [1.812, 64.6], [-1.688, 47.2], [11.712, 14.7], [44.212, 1.3], [76.712, 14.7], [90.212, 47.2], [86.712, 64.6], [76.712, 79.3], [61.812, 89]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.55, -73.95], [-38.05, -103.428], [-110.45, -31.028], [-77.95, -1.55]], "c": true}]}, {"t": 92, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[85.69, 16.49], [72.39, 3.19], [-0.01, 75.59], [13.29, 88.89]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.425, 52.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 82, "op": 107, "st": 82, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Highlight 9", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 75, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [527.5, 508.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46.95, 47.55, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1000, 1000, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[6, 0.1], [5.6, 2.2], [4.3, 4.2], [2.3, 5.5], [0, 6], [-8.6, 8.7], [-12.2, 0], [-8.6, -8.6], [0, -12.2], [2.3, -5.5], [4.3, -4.2], [5.6, -2.2]], "o": [[-6, 0.1], [-5.6, -2.2], [-4.3, -4.2], [-2.3, -5.5], [0, -12.1], [8.6, -8.6], [12.2, 0], [8.6, 8.6], [0, 6], [-2.3, 5.5], [-4.3, 4.2], [-5.7, 2.2]], "v": [[44.212, 92.1], [26.712, 89], [11.812, 79.3], [1.812, 64.6], [-1.688, 47.2], [11.712, 14.7], [44.212, 1.3], [76.712, 14.7], [90.212, 47.2], [86.712, 64.6], [76.712, 79.3], [61.812, 89]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.55, -73.95], [-38.05, -103.428], [-110.45, -31.028], [-77.95, -1.55]], "c": true}]}, {"t": 104, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[85.69, 16.49], [72.39, 3.19], [-0.01, 75.59], [13.29, 88.89]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.425, 52.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 94, "op": 119, "st": 94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Highlight 7", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 75, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [527.5, 508.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46.95, 47.55, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1000, 1000, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[6, 0.1], [5.6, 2.2], [4.3, 4.2], [2.3, 5.5], [0, 6], [-8.6, 8.7], [-12.2, 0], [-8.6, -8.6], [0, -12.2], [2.3, -5.5], [4.3, -4.2], [5.6, -2.2]], "o": [[-6, 0.1], [-5.6, -2.2], [-4.3, -4.2], [-2.3, -5.5], [0, -12.1], [8.6, -8.6], [12.2, 0], [8.6, 8.6], [0, 6], [-2.3, 5.5], [-4.3, 4.2], [-5.7, 2.2]], "v": [[44.212, 92.1], [26.712, 89], [11.812, 79.3], [1.812, 64.6], [-1.688, 47.2], [11.712, 14.7], [44.212, 1.3], [76.712, 14.7], [90.212, 47.2], [86.712, 64.6], [76.712, 79.3], [61.812, 89]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.55, -73.95], [-38.05, -103.428], [-110.45, -31.028], [-77.95, -1.55]], "c": true}]}, {"t": 22, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[85.69, 16.49], [72.39, 3.19], [-0.01, 75.59], [13.29, 88.89]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.425, 52.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 12, "op": 37, "st": 12, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Highlight", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 75, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [527.5, 508.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46.95, 47.55, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1000, 1000, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[6, 0.1], [5.6, 2.2], [4.3, 4.2], [2.3, 5.5], [0, 6], [-8.6, 8.7], [-12.2, 0], [-8.6, -8.6], [0, -12.2], [2.3, -5.5], [4.3, -4.2], [5.6, -2.2]], "o": [[-6, 0.1], [-5.6, -2.2], [-4.3, -4.2], [-2.3, -5.5], [0, -12.1], [8.6, -8.6], [12.2, 0], [8.6, 8.6], [0, 6], [-2.3, 5.5], [-4.3, 4.2], [-5.7, 2.2]], "v": [[44.212, 92.1], [26.712, 89], [11.812, 79.3], [1.812, 64.6], [-1.688, 47.2], [11.712, 14.7], [44.212, 1.3], [76.712, 14.7], [90.212, 47.2], [86.712, 64.6], [76.712, 79.3], [61.812, 89]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.55, -73.95], [-38.05, -103.428], [-110.45, -31.028], [-77.95, -1.55]], "c": true}]}, {"t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[85.69, 16.49], [72.39, 3.19], [-0.01, 75.59], [13.29, 88.89]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.425, 52.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 25, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "Coin <PERSON>", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [432, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [500, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [500, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [500, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 94, "s": [569, 500, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 12, "s": [0, 98.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 24, "s": [100, 98.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 82, "s": [100, 98.9, 100]}, {"t": 94, "s": [0, 98.9, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 12, "op": 150, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "Coin Heads", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"t": 76, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [500, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [569, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [432, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 106, "s": [500, 500, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 98.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 12, "s": [0, 98.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 94, "s": [0, 98.9, 100]}, {"t": 106, "s": [100, 98.9, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 130, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Coin Side", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"t": 76, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500.027, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [11.543, -31.723, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 109.068, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 12, "s": [90.734, 109.068, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 24, "s": [0, 109.068, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 82, "s": [0, 109.068, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 94, "s": [90.734, 109.068, 100]}, {"t": 106, "s": [0, 109.068, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [151.086, 832.555], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.960784373564, 0.701960784314, 0.039215686275, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.543, -31.723], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 130, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "Coin back", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"t": 76, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [500, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [435.284, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [569, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [500, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [500, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [437.67, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [569, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 106, "s": [500, 500, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 12, "s": [0, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 24, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 82, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 94, "s": [0, 100, 100]}, {"t": 106, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 130, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_3", "nm": "Coin <PERSON>", "fr": 30, "layers": [{"ddd": 0, "ind": 3, "ty": 4, "nm": "Coin", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [500.5, 500.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46.2, 45.7, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1000, 1000, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[6, 0.1], [5.6, 2.2], [4.3, 4.2], [2.3, 5.5], [0, 6], [-8.6, 8.7], [-12.2, 0], [-8.6, -8.6], [0, -12.2], [2.3, -5.5], [4.3, -4.2], [5.6, -2.2]], "o": [[-6, 0.1], [-5.6, -2.2], [-4.3, -4.2], [-2.3, -5.5], [0, -12.1], [8.6, -8.6], [12.2, 0], [8.6, 8.6], [0, 6], [-2.3, 5.5], [-4.3, 4.2], [-5.7, 2.2]], "v": [[-0.05, 45.35], [-17.55, 42.25], [-32.45, 32.55], [-42.45, 17.85], [-45.95, 0.45], [-32.55, -32.05], [-0.05, -45.45], [32.45, -32.05], [45.95, 0.45], [42.45, 17.85], [32.45, 32.55], [17.55, 42.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.996, 0.902, 0.447, 0.5, 0.998, 0.81, 0.435, 1, 1, 0.718, 0.424], "ix": 9}}, "s": {"a": 0, "k": [46.408, 0], "ix": 5}, "e": {"a": 0, "k": [-45.433, -0.87], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.2, 45.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_4", "nm": "Coin M", "fr": 30, "layers": [{"ddd": 0, "ind": 5, "ty": 4, "nm": "Coin", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -90, "ix": 10}, "p": {"a": 0, "k": [500.5, 500.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46.2, 45.7, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1000, 1000, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[6, 0.1], [5.6, 2.2], [4.3, 4.2], [2.3, 5.5], [0, 6], [-8.6, 8.7], [-12.2, 0], [-8.6, -8.6], [0, -12.2], [2.3, -5.5], [4.3, -4.2], [5.6, -2.2]], "o": [[-6, 0.1], [-5.6, -2.2], [-4.3, -4.2], [-2.3, -5.5], [0, -12.1], [8.6, -8.6], [12.2, 0], [8.6, 8.6], [0, 6], [-2.3, 5.5], [-4.3, 4.2], [-5.7, 2.2]], "v": [[-0.05, 45.35], [-17.55, 42.25], [-32.45, 32.55], [-42.45, 17.85], [-45.95, 0.45], [-32.55, -32.05], [-0.05, -45.45], [32.45, -32.05], [45.95, 0.45], [42.45, 17.85], [32.45, 32.55], [17.55, 42.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.996, 0.902, 0.447, 0.5, 0.998, 0.81, 0.435, 1, 1, 0.718, 0.424], "ix": 9}}, "s": {"a": 0, "k": [-46.925, 0], "ix": 5}, "e": {"a": 0, "k": [44.613, 0], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.2, 45.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_5", "nm": "Coin M 2", "fr": 30, "layers": [{"ddd": 0, "ind": 6, "ty": 4, "nm": "Coin", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500.5, 500.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46.2, 45.7, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [1000, 1000, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[6, 0.1], [5.6, 2.2], [4.3, 4.2], [2.3, 5.5], [0, 6], [-8.6, 8.7], [-12.2, 0], [-8.6, -8.6], [0, -12.2], [2.3, -5.5], [4.3, -4.2], [5.6, -2.2]], "o": [[-6, 0.1], [-5.6, -2.2], [-4.3, -4.2], [-2.3, -5.5], [0, -12.1], [8.6, -8.6], [12.2, 0], [8.6, 8.6], [0, 6], [-2.3, 5.5], [-4.3, 4.2], [-5.7, 2.2]], "v": [[-0.05, 45.35], [-17.55, 42.25], [-32.45, 32.55], [-42.45, 17.85], [-45.95, 0.45], [-32.55, -32.05], [-0.05, -45.45], [32.45, -32.05], [45.95, 0.45], [42.45, 17.85], [32.45, 32.55], [17.55, 42.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.960784373564, 0.701960784314, 0.039215686275, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.2, 45.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 2, "ty": 0, "nm": "Add funds", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [172, 170, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [172, 170, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [1.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [2.567]}, {"t": 265, "s": [8.833]}], "ix": 2}, "w": 344, "h": 340, "ip": 0, "op": 265, "st": 0, "bm": 0}], "markers": [], "props": {}}