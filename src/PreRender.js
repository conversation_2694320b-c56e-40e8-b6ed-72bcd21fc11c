import {
  StaticR<PERSON>er<PERSON><PERSON><PERSON>,
  createStat<PERSON><PERSON><PERSON><PERSON>,
  createStatic<PERSON>outer,
} from 'react-router-dom/server';
import routers from './routes/routes';

export default async function init(props) {
  const { query, dataRoutes } = createStaticHandler(routers, {
    basename: '/',
  });
  const context = await query(new Request(`http://127.0.0.1:8080${props.url}`));
  const router = createStaticRouter(dataRoutes, context);
  return <StaticRouterProvider router={router} context={context} />;
}
