export const useMockPaytmReadOTPMessage = () => {
  const paytmReadOTPMessageResult = {
    start_read: {
      register: {
        success: true,
        message: "sms listening started",
      },
      send_data: {
        success: true,
        message:
          "<#> \n123456 is your One Time Password (OTP) to verify your Mobile Number for Paytm Money. Valid till 20/06/2025 17:56:26 \nID: ijsfijnkjsnskjn",
        senderId: "JD-PAYTMM-S",
      },
    },
    stop_read: {
      register: {
        success: true,
        message: "sms listening stopped",
      },
    },
  };

  const paytmRegisterOtpReceiver = {
    message: "paytmRegisterOtpReceiver success",
  };

  const paytmGetOtp = {
    message:
      "<#> \n123456 is your One Time Password (OTP) to verify your Mobile Number for Paytm Money. Valid till 20/06/2025 17:56:26 \nID: ijsfijnkjsnskjn",
  };

  const mockGetBridge = () => ({
    call: (method, ...args) => {
      if (method === "paytmReadOTPMessage") {
        if (args?.[args.length - 2]?.step === "stop_read") {
          setTimeout(() => {
            args?.[args.length - 1]({
              data: JSON.stringify(
                paytmReadOTPMessageResult.stop_read.register
              ),
            });
          }, 1000);
        }
      }
      if (method === "paytmRegisterOtpReceiver") {
        const callback = args[args.length - 1];
        setTimeout(() => {
          callback({ data: JSON.stringify(paytmRegisterOtpReceiver.message) });
        }, 1000);
      }
      if (method === "paytmGetOtp") {
        const callback = args[args.length - 1];
        setTimeout(() => {
          callback({
            data: JSON.stringify(paytmGetOtp.message),
          });
        }, 3000);
      }
    },

    subscribe: (method, payload, callback) => {
      if (method === "paytmReadOTPMessage") {
        if (payload?.step === "start_read") {
          setTimeout(() => {
            callback({
              data: JSON.stringify(
                paytmReadOTPMessageResult.start_read.register
              ),
            });

            setTimeout(() => {
              callback({
                data: JSON.stringify(
                  paytmReadOTPMessageResult.start_read.send_data
                ),
              });
            }, 3000); // Second response after 3 seconds
          }, 1000); // First response after 1 second
        }
      }
    },
  });

  return {
    mockGetBridge,
  };
};
