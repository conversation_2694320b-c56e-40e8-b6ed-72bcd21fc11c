import { useCallback, useEffect, useState } from 'react';

import {
  PAYMENT_UPI_INTENT,
  PRIORITIZED_PROCESSES_SET,
  PRIORITIZED_PROCESSES_SET_IOS,
  UPI_INTENT_APP_VERSION,
} from '../config/paymentConfig';
import { pspAppsConfigData } from '../config/upiIntentAppsConfig';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import { useGetUpiAppsIos } from '../query/paymentsQuery';
import { isEquityMiniApp } from '../utils/apiUtil';
import { getUPIAppPresentIos, getUpiIntentApps } from '../utils/bridgeUtils';
import { isIosBuild, versionCompare, log } from '../utils/commonUtil';
import { getAppVersion } from '../utils/coreUtil';

const useUpiIntentFlow = () => {
  const [upiIntentOptions, setUpiIntentOptions] = useState([]);

  // fetch ios upi apps static config
  const { data: fetchedUpiAppsIos } = useGetUpiAppsIos(isIosBuild());

  // enable/disable ios/android version
  const validUpiIntentVersion = () =>
    versionCompare(getAppVersion(), UPI_INTENT_APP_VERSION) === 1;

  /*
   * reorderIntentApps
   * Reorders the UPI apps based on priority.
   * Supports both iOS and Android, and allows for special handling (e.g., for equity mini-apps).
   * Uses a set of prioritized processes (e.g., PRIORITIZED_PROCESSES_SET or PRIORITIZED_PROCESSES_SET_IOS).
   * If the app is an equity mini-app, the ordering might be affected based on custom conditions, such as excluding certain apps or prioritizing others.
   * */
  const reorderIntentApps = useCallback((apps) => {
    const reorderedApps = [];
    const prioritizedProcessesSet = new Set(
      isIosBuild() ? PRIORITIZED_PROCESSES_SET_IOS : PRIORITIZED_PROCESSES_SET,
    );

    apps.forEach((app) => {
      const identifier = isIosBuild() ? app.appIdentifier : app.processName;
      const index = [...prioritizedProcessesSet].indexOf(identifier);

      if (index !== -1) {
        if (isEquityMiniApp()) {
          reorderedApps[index] = app;
          if (isIosBuild()) {
            if (app.appIdentifier === PAYMENT_UPI_INTENT.PAYTM_APP_IDENTIFIER) {
              reorderedApps[index] = {
                ...app,
                disabled: false,
              };
            }
          } else if (
            app.processName === PAYMENT_UPI_INTENT.PAYTM_PROCESS_NAME
          ) {
            reorderedApps[index] = {
              ...app,
              disabled: false,
            };
          } else if (validUpiIntentVersion()) {
            reorderedApps[index] = app;
          }
        } else {
          reorderedApps[index] = app;
        }
      }
    });

    apps.forEach((app) => {
      if (!prioritizedProcessesSet.has(app.processName)) {
        reorderedApps.push(app);
      }
    });
    return reorderedApps.filter((app) => app);
  }, []);

  /*
   * getUpiIntentAppsBridgeCallCallBack
   * Callback function invoked when UPI apps are fetched from the native bridge (for iOS or Android).
   * Processes the data from the native call, and combines it with static app configuration to create a list of UPI apps with logos and other relevant details.
   * The list of UPI apps is then passed to the setUpiIntentOptions state after reordering.
   * */
  const getUpiIntentAppsBridgeCallCallBack = useCallback(
    (result) => {
      log('getUpiIntentAppsBridgeCallBack', result);
      if (
        isIosBuild()
          ? fetchedUpiAppsIos && result && result?.data
          : result && result?.data
      ) {
        const data = JSON.parse(result.data);

        const pspAppOption = isIosBuild()
          ? fetchedUpiAppsIos
          : pspAppsConfigData.data;

        const pspAppsWithLogo = data.reduce((upiPspApps, app) => {
          const upiConfigApp = pspAppOption.find(
            (upiPspOption) =>
              (app.appIdentifier &&
                app.appIdentifier === upiPspOption.appIdentifier) ||
              (app.processName &&
                app.processName === upiPspOption.process_name),
          );

          // if (
          //   (app?.appIdentifier &&
          //     app?.status &&
          //     app.appIdentifier !== PAYMENT_UPI_INTENT.PAYTM_APP_IDENTIFIER) ||
          //   (!app?.appIdentifier &&
          //     app.processName !== PAYMENT_UPI_INTENT.PAYTM_PROCESS_NAME)
          // )
          if ((app?.appIdentifier && app?.status) || !app?.appIdentifier) {
            const pspApp = {
              ...app,
              appName: upiConfigApp?.name || app.appName,
              processName: app?.appIdentifier || app?.processName,
              logoUrl: upiConfigApp?.iconUrl || '',
            };
            delete pspApp.iconUrl;
            upiPspApps.push(pspApp);
          }

          return upiPspApps;
        }, []);
        setUpiIntentOptions(reorderIntentApps(pspAppsWithLogo));
      }
    },
    [fetchedUpiAppsIos, reorderIntentApps],
  );

  /*
   * getUpiIntentAppsBridgeCall
   * A function that triggers a bridge call to get the list of available UPI apps (on Android).
   * */
  const getUpiIntentAppsBridgeCall = useCallback(() => {
    if (DeviceInfoProvider.getInfo('device_type') === 'android') {
      getUpiIntentApps(getUpiIntentAppsBridgeCallCallBack);
    }
  }, [getUpiIntentAppsBridgeCallCallBack]);

  // PML initiate call payload data
  const getPspAppUpi = useCallback(
    (processName) => {
      /*
       * getPspAppUpi
       * Retrieves the UPI name for a specific process/app.
       * The function checks whether the app is on iOS or Android, and returns the UPI app name with an appropriate format.
       * For android we are using pspAppsConfigData config. For ios we are using bridge + fetchedUpiAppsIos config
       * If the app name is unavailable, it defaults to returning PAYTM_UPI.
       */
      const upiConfigApp = (
        isIosBuild() ? upiIntentOptions : pspAppsConfigData?.data || []
      ).find((data) => (data.process_name || data.processName) === processName);
      const upiName = upiConfigApp?.appName || upiConfigApp?.name;
      if (upiName) {
        return `${upiName} ${PAYMENT_UPI_INTENT.UPI.toUpperCase()}`;
      }
      return PAYMENT_UPI_INTENT.PAYTM_UPI;
    },
    [upiIntentOptions],
  );

  useEffect(() => {
    getUpiIntentAppsBridgeCall();
  }, [getUpiIntentAppsBridgeCall]);

  /*
   * When fetchedUpiAppsIos is available (iOS-specific UPI apps are fetched), this effect is triggered.
   * It formats the data and sends it to the native bridge (getUPIAppPresentIos) to fetch app data from iOS.
   * */
  useEffect(() => {
    if (fetchedUpiAppsIos?.length && isIosBuild()) {
      const postData = fetchedUpiAppsIos.map((pspApp) => ({
        appName: pspApp.name,
        appIdentifier: pspApp.appIdentifier,
      }));

      // IOS bridge call for upi apps
      getUPIAppPresentIos(postData, getUpiIntentAppsBridgeCallCallBack);
    }
  }, [fetchedUpiAppsIos, getUpiIntentAppsBridgeCallCallBack]);

  /*
   * upiIntentOptions: An array of objects containing the UPI apps available for the intent flow.
      Each object contains the app’s details (e.g., appName, appIdentifier, logoUrl).
   * getPspAppUpi: A function that can be called with a processName to retrieve the UPI app name associated with that process.
   * */

  return {
    upiIntentOptions,
    getPspAppUpi,
  };
};

export default useUpiIntentFlow;
