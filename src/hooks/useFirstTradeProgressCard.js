import { useEffect, useRef, useState } from 'react';

import {
  FUND_ADDED,
  HAS_INVESTED,
  IR_STATUS_ENUM,
  PERCENTAGES_MAP,
  CARD_IDS,
  HAS_TRADED,
} from '../config/FirstTradeProgressCard';
import myAccount from '../assets/FirstTradeProgressCard/My_Account_LT.svg';
import addFund from '../assets/FirstTradeProgressCard/Add_Funds.svg';
import stocks from '../assets/FirstTradeProgressCard/Stocks_LT.svg';
import mutualFund from '../assets/FirstTradeProgressCard/Mutual_Funds_LT.svg';

const useFirstTradeProgressCard = ({ pages }) => {
  const timeoutRef = useRef(null);
  const mainContainerRef = useRef(null);
  const { widgetId, type: widgetType } = pages;
  const subCohortId = pages?.data?.meta?.subCohortId;
  const attributes = pages?.data?.widget?.attributes;
  const userReadiness = attributes?.find(
    (item) => item.name === CARD_IDS.USER_READINESS_API,
  )?.value;

  const irStatus = userReadiness?.equity?.irStatus;
  const hasInvested = userReadiness?.equity?.hasInvested;
  const hasTraded = userReadiness?.equity?.hasTraded;

  const list = attributes
    ?.filter((item) =>
      [
        CARD_IDS.ACCOUNT_DETAILS_CARD,
        CARD_IDS.FUND_ADDITION_CARD,
        CARD_IDS.MF_PURCHASE_CARD,
        CARD_IDS.STOCK_PURCHASE_CARD,
      ].includes(item.name),
    )
    .map((item) => {
      let iconUrl = '';
      switch (item.name) {
        case CARD_IDS.ACCOUNT_DETAILS_CARD:
          iconUrl = myAccount;
          break;
        case CARD_IDS.FUND_ADDITION_CARD:
          iconUrl = addFund;
          break;
        case CARD_IDS.MF_PURCHASE_CARD:
          iconUrl = mutualFund;
          break;
        case CARD_IDS.STOCK_PURCHASE_CARD:
          iconUrl = stocks;
          break;
        default:
          break;
      }

      return {
        id: item.id,
        name: item.name,
        iconUrl,
        ...item.value,
      };
    })
    .sort((a, b) => Number(a.order) - Number(b.order));

  const percentages = attributes?.find(
    (item) => item.name === CARD_IDS.PERCENT_COMPLETE,
  )?.value;

  const percentageMap = percentages?.reduce((acc, { title, range }) => {
    acc[title] = Number(range);
    return acc;
  }, {});

  const title = attributes?.find(
    (item) => item.name === CARD_IDS.TITLE_PREIR,
  )?.value;

  const subTitle = attributes?.find(
    (item) => item.name === CARD_IDS.SUB_TITLE_PREIR,
  )?.value;

  const [percentage, setPercentage] = useState(0);
  const [isFundsUserEligible, setIsFundsUserEligible] = useState(
    IR_STATUS_ENUM.PENDING,
  );
  const [isMfIR, setIsMfIR] = useState(false);
  const [isStockIR, setIsStockIR] = useState(false);

  useEffect(() => {
    if (
      [IR_STATUS_ENUM.ACTIVE, IR_STATUS_ENUM.ADD_FUNDS_READY].includes(
        userReadiness?.equity?.irStatus,
      )
    ) {
      let newPercentage = percentageMap?.[PERCENTAGES_MAP.AC_DETAILS] || 70;
      if (
        [IR_STATUS_ENUM.ADD_FUNDS_READY].includes(
          userReadiness?.equity?.irStatus,
        )
      ) {
        setIsFundsUserEligible(true);
      }
      if (
        [FUND_ADDED.HASADDED].includes(userReadiness?.equity?.hasAddedFunds)
      ) {
        newPercentage += percentageMap?.[PERCENTAGES_MAP.ADD_FUNDS] || 10;
      }
      if (
        [HAS_INVESTED.HASINVESTED, HAS_INVESTED.INPROGRESS].includes(
          userReadiness?.equity?.hasInvested,
        ) ||
        [HAS_TRADED.HASTRADED].includes(userReadiness?.equity?.status)
      ) {
        newPercentage += percentageMap?.[PERCENTAGES_MAP.FIRST_STOCK] || 10;
      }
      if ([HAS_INVESTED.HASINVESTED].includes(userReadiness?.mf?.hasInvested)) {
        newPercentage += percentageMap?.[PERCENTAGES_MAP.FIRST_MF] || 10;
      }
      setPercentage(newPercentage);
      if ([IR_STATUS_ENUM.ACTIVE].includes(userReadiness?.equity?.irStatus))
        setIsStockIR(true);
      if ([IR_STATUS_ENUM.ACTIVE].includes(userReadiness?.mf?.irStatus))
        setIsMfIR(true);
    } else setIsFundsUserEligible(false);
  }, [userReadiness]);

  return {
    percentage,
    isMfIR,
    isStockIR,
    list,
    isFundsUserEligible,
    title,
    subTitle,
    mainContainerRef,
    timeoutRef,
    widgetId,
    widgetType,
    subCohortId,
    irStatus,
    hasInvested,
    hasTraded,
  };
};

export default useFirstTradeProgressCard;
