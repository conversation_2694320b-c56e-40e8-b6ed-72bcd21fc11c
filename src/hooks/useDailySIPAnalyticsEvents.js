import { useCombinedIr } from '../query/readinessQuery';
import { isSourceMFH5, sendAnalyticsEventDailySIP } from '../utils/coreUtil';
import { PULSE_STATICS_DAILY_SIP } from '../utils/constants';
import { getIsDailySipMF, getDailySipCohort } from '../utils/commonUtil';
import { PULSE_STATICS } from '../pages/MfDailySipSelectAmount/enums';

const useDailySIPAnalyticsEvents = () => {
  const { irData, isLoading } = useCombinedIr(true);

  if (isLoading) {
    return {
      sendAnalyticsEventDailySIP: (params) => {
        sendAnalyticsEventDailySIP({
          verticalName: PULSE_STATICS_DAILY_SIP.VERTICAL_NAME,
          screenName: PULSE_STATICS_DAILY_SIP.SCREEN_NAME,
          category: getIsDailySipMF()
            ? PULSE_STATICS_DAILY_SIP.CATEGORY_MF
            : PULSE_STATICS_DAILY_SIP.CATEGORY,
          label4: getDailySipCohort() || '',
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  const { eqCashHasInvested: hasInvested, eqCashIrStatus: irStatus } =
    irData || {
      eqCashHasInvested: null,
      eqCashIrStatus: null,
    };

  return {
    sendAnalyticsEventDailySIP: (params) => {
      sendAnalyticsEventDailySIP({
        verticalName: PULSE_STATICS_DAILY_SIP.VERTICAL_NAME,
        screenName: PULSE_STATICS_DAILY_SIP.SCREEN_NAME,
        category: isSourceMFH5()
          ? PULSE_STATICS_DAILY_SIP.CATEGORY_MF
          : PULSE_STATICS_DAILY_SIP.CATEGORY,
        label4: getDailySipCohort() || '',
        label6: `irstatus:${irStatus} | isinvestedstatus:${hasInvested}`,
        ...params,
      });
    },
  };
};

const useCustomAnalyticsEvents = (customVerticalName, customScreenName) => {
  const { irData, isLoading } = useCombinedIr(true);

  if (isLoading) {
    return {
      sendCustomAnalyticsEvent: (params) => {
        sendAnalyticsEventDailySIP({
          verticalName: customVerticalName || PULSE_STATICS.VERTICAL_NAME,
          screenName: customScreenName || PULSE_STATICS.SCREEN_NAME,
          category: isSourceMFH5()
            ? PULSE_STATICS_DAILY_SIP.CATEGORY_MF
            : PULSE_STATICS_DAILY_SIP.CATEGORY,
          label: PULSE_STATICS_DAILY_SIP.OTP,
          label4: getDailySipCohort() || '',
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  const { eqCashHasInvested: hasInvested, eqCashIrStatus: irStatus } =
    irData || {
      eqCashHasInvested: null,
      eqCashIrStatus: null,
    };

  return {
    sendCustomAnalyticsEvent: (params) => {
      sendAnalyticsEventDailySIP({
        verticalName: customVerticalName || PULSE_STATICS.VERTICAL_NAME,
        screenName: customScreenName || PULSE_STATICS.SCREEN_NAME,
        category: getIsDailySipMF()
          ? PULSE_STATICS_DAILY_SIP.CATEGORY_MF
          : PULSE_STATICS_DAILY_SIP.CATEGORY,
        label: PULSE_STATICS_DAILY_SIP.OTP,
        label4: getDailySipCohort() || '',
        label6: `irstatus:${irStatus} | isinvestedstatus:${hasInvested}`,
        ...params,
      });
    },
  };
};

export { useDailySIPAnalyticsEvents, useCustomAnalyticsEvents };
