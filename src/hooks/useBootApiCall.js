import { useEffect, useState } from 'react';
import { useUserBoot, useImgAuth } from '../query/generalQuery';

function getPersonalDetails() {
  const userBootData = JSON.parse(localStorage.getItem('userBootData'));

  if (userBootData) return userBootData?.personalDetails;

  return {
    displayName: null,
    firstName: null,
  };
}

function getUserProfilePic() {
  const userBootData = JSON.parse(localStorage.getItem('userBootData'));
  if (userBootData) return userBootData?.personalDetails.displayPicLocation;

  return null;
}

export function useBootApiCall() {
  const [personalDetails, setPersonalDetails] = useState(getPersonalDetails());

  const [userProfilePicUrl, setUserProfilePicUrl] =
    useState(getUserProfilePic());

  const { isLoading, data } = useUserBoot(true);
  const { isProfilePicLoading, data: userProfilePic } =
    useImgAuth(userProfilePicUrl);

  useEffect(() => {
    if (!isLoading && data?.personalDetails) {
      const { personalDetails: personalDetailsData } = data;

      setPersonalDetails({ ...personalDetailsData });
      if (personalDetailsData.displayPicLocation) {
        setUserProfilePicUrl(personalDetailsData.displayPicLocation);
      }
    }
  }, [isLoading, data]);
  return { isLoading, personalDetails, isProfilePicLoading, userProfilePic };
}
