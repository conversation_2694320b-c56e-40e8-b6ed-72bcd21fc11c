import { sendAnalyticsEventDailySIP } from '../utils/coreUtil';
import { PULSE_STATICS_ETF_CARD } from '../utils/constants';
import { useCombinedIr } from '../query/readinessQuery';

export const useETFAnalyticsEvent = ({ widgetId, subCohortId }) => {
  const { irData, isLoading } = useCombinedIr();

  if (isLoading) {
    return {
      sendAnalyticsEventETF: (params) => {
        sendAnalyticsEventDailySIP({
          verticalName: PULSE_STATICS_ETF_CARD.VERTICAL_NAME,
          screenName: PULSE_STATICS_ETF_CARD.SCREEN_NAME,
          category: PULSE_STATICS_ETF_CARD.CATEGORY,
          label2: `${widgetId}, ${widgetId}`,
          label4: subCohortId || '',
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  return {
    sendAnalyticsEventETF: (params) => {
      sendAnalyticsEventDailySIP({
        verticalName: PULSE_STATICS_ETF_CARD.VERTICAL_NAME,
        screenName: PULSE_STATICS_ETF_CARD.SCREEN_NAME,
        category: PULSE_STATICS_ETF_CARD.CATEGORY,
        label2: `${widgetId}, ${widgetId}`,
        label4: subCohortId || '',
        label6: `irStatus=${irData?.eqCashIrStatus || ''} | isUserInvested=${
          irData?.eqCashHasInvested || 'NA'
        }`,
        ...params,
      });
    },
  };
};
