import { useEffect, useRef } from 'react';

/**
 * Detects horizontal swipes and triggers a callback.
 *
 * @param {function} onSwipe - Callback to call on swipe.
 * @param {number} threshold - Minimum horizontal distance to consider as swipe (default: 50).
 * @returns {object} - A ref to attach to the DOM element you want to observe.
 */
function useSwipeDetector(onSwipe, threshold = 50) {
  const ref = useRef(null);
  const startX = useRef(0);
  const endX = useRef(0);

  useEffect(() => {
    const el = ref.current;
    if (!el) return;

    const handleTouchStart = (e) => {
      startX.current = e.touches[0].clientX;
    };

    const handleTouchEnd = (e) => {
      endX.current = e.changedTouches[0].clientX;
      const deltaX = endX.current - startX.current;

      if (Math.abs(deltaX) > threshold) {
        onSwipe(deltaX > 0 ? 'right' : 'left');
      }
    };

    el.addEventListener('touchstart', handleTouchStart);
    el.addEventListener('touchend', handleTouchEnd);

    return () => {
      el.removeEventListener('touchstart', handleTouchStart);
      el.removeEventListener('touchend', handleTouchEnd);
    };
  }, [onSwipe, threshold]);

  return ref;
}

export default useSwipeDetector;
