import { useEffect, useRef, useState } from 'react';

import { getBridge } from '@src/utils/bridgeUtils';
import { isIosBuild } from '@src/utils/commonUtil';
import { isPaytmMoney } from '@src/utils/coreUtil';
import { BASE_URL } from '@src/config/envConfig';

// uncomment next line for testing auto read otp in local environment
// import { useMockPaytmReadOTPMessage } from './mockBridgeResponse/useMockPaytmReadOTPMessage';

const AUTO_READ_OTP_CONSTANTS = {
  TIME_LIMIT: 30, // in seconds
  SUBSCRIBE_STEP: 'start_read',
  UNSUBSCRIBE_STEP: 'stop_read',
  DURATION: 60, // in seconds
  SENDER_ID: 'PAYTMM,JM-MFCENT-S',
};

export const useAutoReadOtp = ({
  otpData = {},
  setOtpData = () => {},
  beforeOtpVerification = () => {},
}) => {
  // uncomment next line for testing auto read otp in local environment
  // const { mockGetBridge } = useMockPaytmReadOTPMessage();

  const [otpReadFromDevice, setReadOtpFromDevice] = useState('');
  const [otpAutoReadInProgress, setOtpAutoReadInProgress] = useState(false);
  const [autoReadTimer, setAutoReadTimer] = useState(
    AUTO_READ_OTP_CONSTANTS.TIME_LIMIT,
  );

  const intervalRef = useRef(null);

  const unsubscribePaytmReadOTP = ({
    setOtpAutoReadInProgressState = () => {},
  }) => {
    // uncomment next line for testing auto read otp in local environment
    // const bridgeName = mockGetBridge();

    const bridgeName = getBridge();
    const bridgePayload = {
      step: AUTO_READ_OTP_CONSTANTS.UNSUBSCRIBE_STEP,
    };

    if (bridgeName) {
      setOtpAutoReadInProgressState(false);
      bridgeName.call('paytmReadOTPMessage', bridgePayload, (result) => {
        console.log(
          'paytmReadOTPMessage unsubscribe  result---',
          JSON.stringify(result),
        );
      });
    }
  };

  const unsubscribePmlReadOTP = ({
    setOtpAutoReadInProgressState = () => {},
  }) => {
    console.log('paytmGetOtp unsubscribe---');
    setOtpAutoReadInProgressState(false);

    // const bridgeName = getBridge();
    // if (bridgeName) {
    // setOtpAutoReadInProgressState(false);
    //   bridgeName.unsubscribe("paytmGetOtp", (result) => {
    //     console.log("paytmGetOtp unsubscribe  result---", JSON.stringify(result));
    //   });
    // }
  };

  const triggerUnSubscribeAutoReadOtp = () => {
    console.log('trigger unsubscribe otp auto read bridge---');

    if (!otpAutoReadInProgress) {
      return;
    }

    if (!isIosBuild()) {
      if (isPaytmMoney()) {
        if (!BASE_URL.OTP_AUTO_READ.PML) return;

        console.log('unsubscribe otp auto read bridge for pml---');
        unsubscribePmlReadOTP({
          setOtpAutoReadInProgressState: setOtpAutoReadInProgress,
        });
      } else {
        if (!BASE_URL.OTP_AUTO_READ.PAYTM) return;

        console.log('unsubscribe otp auto read bridge for paytm---');
        unsubscribePaytmReadOTP({
          setOtpAutoReadInProgressState: setOtpAutoReadInProgress,
        });
      }
    }
  };

  const subscribePaytmReadOTP = ({
    setOtpFromDevice = () => {},
    setOtpAutoReadInProgressState = () => {},
  }) => {
    // uncomment next line for testing auto read otp in local environment
    // const bridgeName = mockGetBridge();

    const bridgeName = getBridge();
    const bridgePayload = {
      step: AUTO_READ_OTP_CONSTANTS.SUBSCRIBE_STEP,
      duration: AUTO_READ_OTP_CONSTANTS.DURATION,
      sender_id: AUTO_READ_OTP_CONSTANTS.SENDER_ID,
    };

    try {
      if (bridgeName) {
        setOtpAutoReadInProgressState(true);

        bridgeName.subscribe('paytmReadOTPMessage', bridgePayload, (result) => {
          console.log('paytmReadOTPMessage result---', result);

          if (result?.data) {
            const data = JSON.parse(result?.data);
            console.log('paytmReadOTPMessage data---', data);

            if (data?.success && data?.senderId && data?.message) {
              const autoReadOtp = data?.message.match(/\d{6}/);

              if (autoReadOtp) {
                setOtpFromDevice(autoReadOtp[0]);
                unsubscribePaytmReadOTP({ setOtpAutoReadInProgressState });
              }
            }
          } else if (result?.error) {
            setOtpAutoReadInProgressState(false);
          }
        });
      }
    } catch (err) {
      console.log('getOtpInPaytm err---', err);
      setOtpAutoReadInProgressState(false);
    }
  };

  const subscribePmlReadOTP = ({
    setOtpFromDevice = () => {},
    setOtpAutoReadInProgressState = () => {},
    digits = 6,
  }) => {
    // uncomment next line for testing auto read otp in local environment
    // const bridgeName = mockGetBridge();

    const bridgeName = getBridge();

    try {
      if (bridgeName) {
        bridgeName.call('paytmGetOtp', (result) => {
          console.log('paytmGetOtp result---', JSON.stringify(result));

          const regexPattern = new RegExp(`\\d{${digits}}`);
          const autoReadOtp = result?.data?.match(regexPattern);

          if (autoReadOtp) {
            setOtpFromDevice(autoReadOtp[0]);
            unsubscribePmlReadOTP({ setOtpAutoReadInProgressState });
          } else if (result?.error) {
            setOtpAutoReadInProgressState(false);
          }
        });
      }
    } catch (err) {
      console.log('getOtpInPaytm err---', err);
      setOtpAutoReadInProgressState(false);
    }
  };

  const registerPmlOtpReceiver = () => {
    if (!BASE_URL.OTP_AUTO_READ.PML) return;

    // uncomment next line for testing auto read otp in local environment
    // const bridgeName = mockGetBridge();

    const bridgeName = getBridge();

    if (isPaytmMoney()) {
      if (otpAutoReadInProgress) {
        return;
      }

      try {
        setOtpAutoReadInProgress(true);

        if (bridgeName) {
          bridgeName.call('paytmRegisterOtpReceiver', (result) => {
            console.log(
              'paytmRegisterOtpReceiver  result---',
              JSON.stringify(result),
            );
          });
        }
      } catch (err) {
        console.log('paytmRegisterOtpReceiver err---', err);
      }
    }
  };

  const triggerSubscribeAutoReadOtp = () => {
    console.log('trigger subscribe otp auto read bridge---');

    if (!isIosBuild()) {
      if (isPaytmMoney()) {
        if (!BASE_URL.OTP_AUTO_READ.PML) return;

        console.log('subscribe otp auto read bridge for pml---');
        setTimeout(() => {
          subscribePmlReadOTP({
            setOtpFromDevice: setReadOtpFromDevice,
            setOtpAutoReadInProgressState: setOtpAutoReadInProgress,
          });
        }, 2000);
      } else {
        if (!BASE_URL.OTP_AUTO_READ.PAYTM) return;

        if (otpAutoReadInProgress) {
          return;
        }

        console.log('subscribe otp auto read bridge for paytm---');
        subscribePaytmReadOTP({
          setOtpFromDevice: setReadOtpFromDevice,
          setOtpAutoReadInProgressState: setOtpAutoReadInProgress,
        });
      }
    }
  };

  // otpAutoReadInProgress interval
  useEffect(() => {
    if (otpAutoReadInProgress) {
      setAutoReadTimer(AUTO_READ_OTP_CONSTANTS.TIME_LIMIT); // Reset timer

      intervalRef.current = setInterval(() => {
        setAutoReadTimer((prev) => {
          if (prev <= 1) {
            clearInterval(intervalRef.current);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
      setAutoReadTimer(0);
    }

    return () => clearInterval(intervalRef.current);
  }, [otpAutoReadInProgress]);

  // setting auto read otp
  useEffect(() => {
    if (otpReadFromDevice?.length > 0) {
      setOtpData({ ...otpData, otpReadFromDevice });
      beforeOtpVerification();
    }
  }, [otpReadFromDevice, otpAutoReadInProgress]);

  return {
    otpAutoReadInProgress,
    autoReadTimer,
    registerPmlOtpReceiver,
    triggerUnSubscribeAutoReadOtp,
    triggerSubscribeAutoReadOtp,
  };
};
