import { useCombinedIr } from '../query/readinessQuery';
import { isSourceMFH5, sendAnalyticsEventDailySIP } from '../utils/coreUtil';
import { PULSE_STATICS_GENERIC_WIDGET } from '../utils/constants';
import { getCollectionName, getGenericWidgetCohort } from '../utils/commonUtil';

const useGenericWidgetAnalyticsEvents = () => {
  const { irData, isLoading } = useCombinedIr(true);

  if (isLoading) {
    return {
      sendAnalyticsEventGenWidget: (params) => {
        sendAnalyticsEventDailySIP({
          verticalName: PULSE_STATICS_GENERIC_WIDGET.VERTICAL_NAME,
          screenName: PULSE_STATICS_GENERIC_WIDGET.SCREEN_NAME,
          category: isSourceMFH5()
            ? PULSE_STATICS_GENERIC_WIDGET.CATEGORY_MF
            : PULSE_STATICS_GENERIC_WIDGET.CATEGORY,
          label2: getCollectionName() || '',
          label4: getGenericWidgetCohort() || '',
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
      isLoading,
    };
  }

  const { eqCashHasInvested: hasInvested, eqCashIrStatus: irStatus } =
    irData || {
      eqCashHasInvested: null,
      eqCashIrStatus: null,
    };

  return {
    sendAnalyticsEventGenWidget: (params) => {
      sendAnalyticsEventDailySIP({
        verticalName: PULSE_STATICS_GENERIC_WIDGET.VERTICAL_NAME,
        screenName: PULSE_STATICS_GENERIC_WIDGET.SCREEN_NAME,
        category: isSourceMFH5()
          ? PULSE_STATICS_GENERIC_WIDGET.CATEGORY_MF
          : PULSE_STATICS_GENERIC_WIDGET.CATEGORY,

        label2: getCollectionName() || '',
        label4: getGenericWidgetCohort() || '',
        label6: `irstatus:${irStatus} | isinvestedstatus:${hasInvested}`,
        ...params,
      });
    },
    isLoading,
  };
};

export { useGenericWidgetAnalyticsEvents };
