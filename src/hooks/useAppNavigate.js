import { useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

const useAppNavigate = () => {
  const navigate = useNavigate();

  const appNavigate = useCallback(
    (path, navigateOptions) => {
      navigate(path, navigateOptions);
    },
    [navigate],
  );

  return useMemo(
    () => ({
      appNavigate,
    }),
    [appNavigate],
  );
};

export default useAppNavigate;
