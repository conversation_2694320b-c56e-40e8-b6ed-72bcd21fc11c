import { useEffect, useState } from 'react';
import queryString from 'query-string';

import { PAYMENT_UPI_INTENT } from '../config/paymentConfig';
import { useMandateRegOption } from '../query/autopayQuery';
// import { ROUTES } from '../routes/routes';
import { removeDuplicates, sanitizeAppName } from '../utils/commonUtil';
// import { useAutoPayEvents } from './useAutopayEvents';
import useUpiIntentFlow from './useUpiIntentFlow';

const useAutopayCardDetails = (amount) => {
  // const navigate = useNavigate();
  const [showBlockerPage, setShowBlockerPage] = useState(false);
  const [isRecentlyUsedSelected, setIsRecentlyUsedSelected] = useState(false);
  const [quickRecommendedPaymentMethods, setQuickRecommendedPaymentMethods] =
    useState([]);
  const [otherUpiPaymentMethods, setOtherUpiPaymentMethods] = useState([]);
  const emptyPspApp = { processName: '', source: '', appName: '' };
  const [selectedPspApp, setSelectedPspApp] = useState(emptyPspApp);
  const [selectedVpa, setSelectedVpa] = useState('');
  const [selectedBank, setSelectedBank] = useState();
  const [recentlyUsedVpa, setRecentlyUsedVpa] = useState([]);
  const [showRecentlyUsed, setShowRecentlyUsed] = useState(false);
  const [validQnRAvailable, setValidQnRAvailable] = useState(false);
  const [isMandateRegistrationFlow, setIsMandateRegistrationFlow] = useState(false);


  const searchParams = queryString.parse(window.location.search);

  // useEffect(() => {
  //   console.log("showBlockerPage=", showBlockerPage);
  //   console.log("isRecentlyUsedSelected=", isRecentlyUsedSelected);
  //   console.log("quickRecommendedPaymentMethods=", quickRecommendedPaymentMethods);
  //   console.log("otherUpiPaymentMethods=", otherUpiPaymentMethods);
  //   console.log("selectedPspApp=", selectedPspApp);
  //   console.log("selectedVpa=", selectedVpa);
  //   console.log("selectedBank=", selectedBank);
  //   console.log("recentlyUsedVpa=", recentlyUsedVpa);
  //   console.log("showRecentlyUsed=", showRecentlyUsed);
  //   console.log("validQnRAvailable=", validQnRAvailable);
  // }, [showBlockerPage, isRecentlyUsedSelected,
  //   quickRecommendedPaymentMethods, otherUpiPaymentMethods, selectedPspApp,
  //   selectedVpa, selectedBank, recentlyUsedVpa, showRecentlyUsed, validQnRAvailable
  // ]);


  const {
    data: bankAccountsList,
    isLoading: isbankAccountsListLoading,
    isFetching: isbankAccountsListFetching,
  } = useMandateRegOption(amount);

  // const autoPayEvents = useAutoPayEvents();

  const eventLabel3 = `${
    selectedPspApp?.processName
      ? 'UPI Intent'
      : selectedVpa
        ? 'UPI Collect'
        : 'false'
  } | ${showRecentlyUsed ? 'true' : 'false'}`;
  const eventLabel4 = `${
    selectedPspApp?.processName ||
    selectedVpa?.toString()?.split('@')[1] ||
    otherUpiPaymentMethods
      .find(({ id }) => id === selectedBank)
      ?.upiDetails?.collect?.lastUsedVpa?.toString()
      ?.split('@')[1] ||
    otherUpiPaymentMethods.find(({ id }) => id === selectedBank)?.upiDetails
      ?.intent?.name ||
    'false'
  } | ${selectedBank}`;

  const { upiIntentOptions } = useUpiIntentFlow();

  const handleUpiAppClick = (upiDetails) => {
    if (upiDetails?.isVpa) {
      setSelectedVpa(upiDetails.vpa);
      setSelectedPspApp(emptyPspApp);
    } else if (upiDetails?.intentName) {
      const selecteIntentApp = upiIntentOptions.find((option) =>
        upiDetails.intentName
          ?.toLowerCase()
          .includes(option.appName.toLowerCase()),
      );
      setSelectedPspApp({
        processName: selecteIntentApp?.processName,
        appName: selecteIntentApp?.appName,
        source: PAYMENT_UPI_INTENT.UPI,
        logo: selecteIntentApp?.logoUrl,
      });
      setSelectedVpa('');
    } else if (upiDetails?.processName) {
      const selecteIntentApp = upiIntentOptions.find(
        (option) => upiDetails?.processName === option.processName,
      );
      setSelectedPspApp({
        processName: selecteIntentApp?.processName,
        appName: selecteIntentApp?.appName,
        source: PAYMENT_UPI_INTENT.UPI,
        logo: selecteIntentApp?.logoUrl,
      });
      setSelectedVpa('');
    }
    if (upiDetails?.isRecentlyUsedSelected) {
      setIsRecentlyUsedSelected(true);
    } else {
      setIsRecentlyUsedSelected(false);
    }
    setSelectedBank(
      upiDetails?.bankId ||
        otherUpiPaymentMethods.find(({ id }) => id === selectedBank)?.id ||
        otherUpiPaymentMethods[0].id,
    );
  };

  const onRadioClick = (bankId) => {
    // autoPayEvents.onAutopayBankSelectedAddUpiSection(eventLabel3, eventLabel4);
    setSelectedBank(bankId);
  };

  const onAutoPayClick = (vpa, paymentsTransactionId, transactionId) => {
    // autoPayEvents.onAutopayProceedClicked(eventLabel3, eventLabel4);
    const state = {
      selectedBank: otherUpiPaymentMethods.find(
        ({ id }) => id === selectedBank,
      ),
      selectedPspApp: selectedVpa ? emptyPspApp : selectedPspApp,
      selectedVpa: selectedVpa,
      eventLabel3,
      eventLabel4,
      amount,
      paymentsTransactionId,
      transactionId,
      isMandateRegistrationFlow,
    };
    const encodedState = encodeURI(JSON.stringify(state));
    // navigate(`${ROUTES.PG_REDIRECTION}?stateData=${encodedState}`, {
      // state: {
      //   selectedBank: otherUpiPaymentMethods.find(
      //     ({ id }) => id === selectedBank,
      //   ),
      //   selectedPspApp: vpa ? emptyPspApp : selectedPspApp,
      //   selectedVpa: vpa || selectedVpa,
      //   eventLabel3,
      //   eventLabel4,
      //   amount,
      //   paymentsTransactionId,
      //   transactionId,
      //   isMandateRegistrationFlow,
      // },
    // });
  };

  useEffect(() => {
    setIsMandateRegistrationFlow(bankAccountsList?.data?.isMandateRegistrationFlow);

    let isRecentlyUsedSelectedLoc = false;
    if (!bankAccountsList?.data?.paymentMethods?.length) return;

    const quickRecommendedPayment = bankAccountsList.data.paymentMethods.find(
      ({ paymentMethodId }) => paymentMethodId === 10,
    );

    const quickRecommendedPaymentOptions = quickRecommendedPayment?.paymentMethodOptionList;

    setQuickRecommendedPaymentMethods(
      quickRecommendedPaymentOptions?.length
        ? quickRecommendedPaymentOptions
        : [],
    );

    const otherPayment = bankAccountsList.data.paymentMethods.find(
      ({ paymentMethodId }) => paymentMethodId === 5,
    );

    const otherPaymentOptions = otherPayment?.paymentMethodOptionList;

    setOtherUpiPaymentMethods(
      otherPaymentOptions?.length ? otherPaymentOptions : [],
    );

    if (
      quickRecommendedPaymentOptions?.[0]?.id &&
      quickRecommendedPaymentOptions?.[0]?.paymentAllowed
    ) {
      if (quickRecommendedPaymentOptions[0].upiDetails?.intent?.name) {
        const selectedIntentApp = upiIntentOptions.find((option) =>
          sanitizeAppName(quickRecommendedPaymentOptions[0].upiDetails.intent.name)
            ?.toLowerCase()
            .includes(option.appName.toLowerCase()),
        );

        const isDisabled =
          otherPayment?.intentAppConfig?.intentDisabledApps?.find(
            (option) =>
              selectedIntentApp?.appName
                ?.toLowerCase()
                .includes(option.toLowerCase()) ||
              option
                .toLowerCase()
                .includes(selectedIntentApp?.appName?.toLowerCase()),
          );
        if (!isDisabled && selectedIntentApp) {
          setSelectedPspApp({
            processName: selectedIntentApp?.processName,
            appName: selectedIntentApp?.appName,
            source: PAYMENT_UPI_INTENT.UPI,
            logo: selectedIntentApp?.logoUrl,
          });
          setSelectedBank(quickRecommendedPaymentOptions[0].id);
          isRecentlyUsedSelectedLoc = true;
          setValidQnRAvailable(true);
        }
      } else if (
        quickRecommendedPaymentOptions[0]?.upiDetails?.collect?.lastUsedVpa
      ) {
        setSelectedVpa(
          quickRecommendedPaymentOptions[0].upiDetails.collect.lastUsedVpa,
        );
        setSelectedBank(quickRecommendedPaymentOptions[0].id);
        isRecentlyUsedSelectedLoc = true;
        setValidQnRAvailable(true);
      }
    }
    let collectedRecentlyUsedVpa = [];
    if (quickRecommendedPaymentOptions?.[0]?.upiDetails?.collect?.lastUsedVpa) {
      collectedRecentlyUsedVpa.push(
        quickRecommendedPaymentOptions[0].upiDetails.collect.lastUsedVpa,
      );
    }
    if (otherPaymentOptions?.length) {
      otherPaymentOptions.forEach((option) => {
        collectedRecentlyUsedVpa = option?.recentlyUsedVpa?.length
          ? collectedRecentlyUsedVpa.concat(option.recentlyUsedVpa)
          : collectedRecentlyUsedVpa;
      });
    }

    collectedRecentlyUsedVpa = removeDuplicates(collectedRecentlyUsedVpa);

    if (collectedRecentlyUsedVpa?.length) {
      setRecentlyUsedVpa(collectedRecentlyUsedVpa);
    }
    if (otherPaymentOptions?.length && !isRecentlyUsedSelectedLoc) {
      const firstRegistrationEnabledBank = otherPaymentOptions.find(
        ({ paymentAllowed }) => paymentAllowed,
      );
      if (firstRegistrationEnabledBank?.id)
        setSelectedBank(firstRegistrationEnabledBank.id);
      let firstEnabledIntentApp = null;
      if (upiIntentOptions?.length) {
        firstEnabledIntentApp = upiIntentOptions.find(
          ({ appName }) =>
            !otherPayment?.intentAppConfig?.intentDisabledApps?.find(
              (option) =>
                appName?.toLowerCase().includes(option.toLowerCase()) ||
                option.toLowerCase().includes(appName?.toLowerCase()),
            ),
        );

        if (firstRegistrationEnabledBank?.id && firstEnabledIntentApp) {
          setSelectedPspApp({
            ...firstEnabledIntentApp,
            appName: firstEnabledIntentApp?.appName,
            source: PAYMENT_UPI_INTENT.UPI,
            logo: firstEnabledIntentApp?.logoUrl,
          });
        }
      }
      if (
        collectedRecentlyUsedVpa?.length &&
        firstRegistrationEnabledBank?.id &&
        !firstEnabledIntentApp
      ) {
        setSelectedVpa(collectedRecentlyUsedVpa[0]);
      }
    }
    setIsRecentlyUsedSelected(isRecentlyUsedSelectedLoc);
  }, [bankAccountsList, upiIntentOptions]);

  useEffect(() => {
    if (!isbankAccountsListLoading && !isbankAccountsListFetching) {
      if (
        (!quickRecommendedPaymentMethods?.length &&
          !otherUpiPaymentMethods?.length) ||
        !selectedBank // this will take care the case when all banks available are disbaled.
      ) {
        setShowBlockerPage(true);
      } else {
        setShowBlockerPage(false);
      }
    }
  }, [
    quickRecommendedPaymentMethods,
    otherUpiPaymentMethods,
    isbankAccountsListLoading,
    isbankAccountsListFetching,
    selectedBank,
  ]);

  // useEffect(() => {
  //   if (!selectedBank) return;
  //   let details;
  //
  //   if (isRecentlyUsedSelected) {
  //     details = quickRecommendedPaymentMethods[0];
  //   } else {
  //     details = otherUpiPaymentMethods.find(({ id }) => id === selectedBank);
  //   }
  // }, [
  //   selectedBank,
  //   isRecentlyUsedSelected,
  //   quickRecommendedPaymentMethods,
  //   otherUpiPaymentMethods,
  // ]);

  return {
    isRecentlyUsedSelected,
    upiIntentOptions,
    selectedPspApp,
    onRadioClick,
    handleUpiAppClick,
    selectedBank,
    bankAccountsList,
    isbankAccountsListLoading:
      isbankAccountsListLoading || isbankAccountsListFetching,
    quickRecommendedPaymentMethods,
    otherUpiPaymentMethods,
    onAutoPayClick,
    selectedVpa,
    recentlyUsedVpa,
    showBlockerPage,
    showRecentlyUsed,
    setShowRecentlyUsed,
    validQnRAvailable,
    eventLabel3,
    eventLabel4,
    isMandateRegistrationFlow,
    setSelectedVpa,
  };
};

export default useAutopayCardDetails;
