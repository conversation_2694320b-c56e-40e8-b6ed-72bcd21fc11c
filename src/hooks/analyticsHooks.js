import { useCallback, useEffect, useRef } from 'react';
import { useCombinedIr } from '../query/readinessQuery';
import { PULSE_STATICS_FIRST_CARDS, VERTICAL_NAME } from '../utils/constants';
import {
  sendAnalyticsEventFirstCard,
  sendAnalyticsEvent,
  sendAnalyticsEventFirstTradeProgressCard,
  sendAnalyticsEventWidget,
  sendAnalyticsEventOrderPlacedWidget
} from '../utils/coreUtil';

const useAnalyticsEventForFirstCards = () => {
  const { irData, isLoading } = useCombinedIr();

  if (isLoading) {
    return {
      sendAnalyticsEventFirstCard: (params) => {
        sendAnalyticsEventFirstCard({
          verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
          screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
          category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  return {
    sendAnalyticsEventFirstCard: (params) => {
      sendAnalyticsEventFirstCard({
        verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
        screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
        category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
        label6: `irStatus=${irData?.eqCashIrStatus || ''} | isUserInvested=${irData?.eqCashHasInvested || 'NA'}`,
        ...params,
      });
    },
  };
};

const useAnalyticsEvent = (firstTrade = false) => {
  const { irData, isLoading } = useCombinedIr();

  if (firstTrade) {
    return {
      sendAnalyticsEvent: () => {},
    };
  }

  if (isLoading)
    return {
      sendAnalyticsEvent: (...params) => {
        sendAnalyticsEvent(
          VERTICAL_NAME,
          ...params,
          `irStatus=NA | isUserInvested=NA`,
        );
      },
    };

  return {
    sendAnalyticsEvent: (...params) => {
      sendAnalyticsEvent(
        VERTICAL_NAME,
        ...params,
        `irStatus=${irData?.eqCashIrStatus || ''} | isUserInvested=${
          irData?.eqCashHasInvested || 'NA'
        }`,
      );
    },
  };
};

const useAnalyticsEventForFirstTradeProgressCard = () => ({
  sendAnalyticsEvent: (...params) => {
    sendAnalyticsEventFirstTradeProgressCard(...params);
  },
});

const useAnalyticsEventForWidget = () => {
  const { irData, isLoading } = useCombinedIr();
  if (isLoading) {
    return {
      sendAnalyticsEventWidget: (params) => {
        sendAnalyticsEventWidget({
          verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
          screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
          category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
          label6: `irStatus=${irData?.eqCashIrStatus || ''} | isUserInvested=${
            irData?.eqCashHasInvested || 'NA'
          }`,
          ...params,
        });
      },
    };
  }

  return {
    sendAnalyticsEventWidget: (params) => {
      sendAnalyticsEventWidget({
        verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
        screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
        category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
        label6: `irStatus=${irData?.eqCashIrStatus || ''} | isUserInvested=${
          irData?.eqCashHasInvested || 'NA'
        }`,
        ...params,
      });
    },
  };
};
const useAnalyticsEventForOrderPlacedWidget = () => {
  const { irData, isLoading } = useCombinedIr();
  if (isLoading) {
    return {
      sendAnalyticsEventWidget: (params) => {
        const payload = {
          verticalName: params?.verticalName,
          screenName: params?.screenName,
          category: params?.category,
          label2: params?.label2,
          event: params?.event,
          action: params?.action,
          label6: `irStatus=${irData?.eqCashIrStatus || ''} | isUserInvested=${irData?.eqCashHasInvested || 'NA'}`,
        };
        sendAnalyticsEventOrderPlacedWidget(payload);
      },
    };
  }

  return {
    sendAnalyticsEventWidget: (params) => {
      const payload = {
        verticalName: params?.verticalName,
        screenName: params?.screenName,
        category: params?.category,
        label2: params?.label2,
        event: params?.event,
        action: params?.action,
        label6: `irStatus=${irData?.eqCashIrStatus || ''} | isUserInvested=${irData?.eqCashHasInvested || 'NA'}`,
      };
      sendAnalyticsEventOrderPlacedWidget(payload);
    },
  };
};

export {
  useAnalyticsEventForFirstCards,
  useAnalyticsEvent,
  useAnalyticsEventForFirstTradeProgressCard,
  useAnalyticsEventForWidget,
  useAnalyticsEventForOrderPlacedWidget
};
