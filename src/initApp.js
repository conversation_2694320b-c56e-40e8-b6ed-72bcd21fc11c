// import { createRoot } from 'react-dom/client';
import ReactDOM from 'react-dom';
import { v4 as uuid } from 'uuid';
import * as serviceWorkerRegistration from './serviceWorkerRegistration';
import App from './App';
import swConfig from './config/swConfig';
import DeviceInfoProvider from './provider/DeviceInfoProvider';
import { initBridges, isH5 } from './utils/bridgeUtils';
import { deviceTypeCheck, log, originCheck } from './utils/commonUtil';
import { SW } from './utils/constants';
import bridgeData from './utils/localInit';
import { setThemeColors } from './utils/DomUtils';
// import { PRE_RENDERED_ROUTES } from './config/enums';

// const hydrateApp = () => {
//   log('App hydrated');
//   const domNode = document.getElementById('root');
//   hydrateRoot(domNode, <App />, {
//     onUncaughtError: (error, errorInfo) => {
//       console.error('Uncaught error', error, errorInfo.componentStack);
//     },
//   });
// };

const createApp = () => {
  log('App created');
  const domNode = document.getElementById('root');
  // const root = createRoot(domNode);
  ReactDOM.render(<App />, domNode);
  // root.render(<App />);
};

originCheck();
deviceTypeCheck();
setThemeColors();
const getBridgeData = async () => {
  log('#### init bridge calling');
  try {
    await initBridges();
  } catch (e) {
    initBridges();
  }
  log('#### init bridge called pathname', window.location.pathname);

  // if (PRE_RENDERED_ROUTES.includes(window.location.pathname)) {
  //   hydrateApp();
  // } else {
  createApp();
  // }
};

// storing session id
const session_id = uuid();
sessionStorage.setItem('events_session_id', session_id);

// calling loaded event for the app
// logBatcher.log({
//   level: 'info',
//   key: 'hybrid_payments_loaded',
//   data: {
//     data: 'hybrid_payments_loaded',
//   },
//   page: 'hybrid_payments_loaded',
// });

if (isH5()) {
  getBridgeData();

  // document.addEventListener('JSBridgeReady', getBridgeData);
} else {
  Object.entries(bridgeData).forEach(([key, val]) => {
    DeviceInfoProvider.setInfo(key, val);
  });
  // hydrateApp();
  createApp();
}

if (SW) {
  serviceWorkerRegistration.register(swConfig);
} else {
  serviceWorkerRegistration.unregister();
}
