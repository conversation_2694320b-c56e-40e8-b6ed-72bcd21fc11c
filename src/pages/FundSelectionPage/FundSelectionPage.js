import { useEffect, useState } from 'react';

import FundSelectionPopup from '../../components/organisms/FundSelectionPopup/FundSelectionPopup';
import { exitApp } from '../../utils/bridgeUtils';

const FundSelectionPage = () => {
  const [preSelectedFund, setPreSelectedFund] = useState(null);

  useEffect(() => {
    setPreSelectedFund(JSON.parse(localStorage.getItem('preSelectedFund')));
    localStorage.removeItem('preSelectedFund');
  }, []);

  return (
    <FundSelectionPopup
      isOpen
      preSelectedFund={preSelectedFund}
      onClose={exitApp}
    />
  );
};

export default FundSelectionPage;
