import { useEffect, useState } from 'react';

import ETFSelectionPopup from '../../components/organisms/ETFSelectionPopup/ETFSelectionPopup';
import { exitApp } from '../../utils/bridgeUtils';
import { PRESELECTED_ETF } from '../ETFCard/enums';

const ETFSelectionPage = ({
  data: { data: { widget, meta } = {}, widgetId },
}) => {
  // Transform the widget data into the expected format
  const formatWidgetData = () => {
    const attributes = widget?.attributes || [];
    // Create attributes map in single iteration
    const attributesMap = attributes.reduce((acc, attr) => {
      acc[attr.name] = attr.value;
      return acc;
    }, {});

    return {
      popularEtfs: attributesMap.POPULAR_GOLD_ETF || [],
      transitionTime: attributesMap.STOCK_TRANSITION_TIME || 10,
      title: attributesMap.TITLE_GOLD_ETF,
      subTitle: attributesMap.SUB_TITLE_GOLD_ETF,
      disclaimer: attributesMap.DISCLAIMER_GOLD_ETF,
      cta: attributesMap.CTA_1_GOLD_ETF,
      returns: attributesMap.RETURNS_GOLD_ETF,
      subCohortId: meta?.subCohortId || '',
      widgetType: meta?.widgetType || '',
      widgetId,
      businessType: meta?.businessType || 'HOMESCREEN',
    };
  };
  const [preSelectedETF, setPreSelectedETF] = useState(null);

  useEffect(() => {
    setPreSelectedETF(JSON.parse(localStorage.getItem(PRESELECTED_ETF)));
    localStorage.removeItem(PRESELECTED_ETF);
  }, []);
  const etfData = formatWidgetData();

  return (
    <ETFSelectionPopup
      isOpen
      preSelectedETF={preSelectedETF}
      onClose={exitApp}
      etfData={etfData}
    />
  );
};

export default ETFSelectionPage;
