export const data = {
  type: 'h5-fragment-container',
  h5AppUrl: '',
  h5AppID: '',
  fragmentHeightAndroid: 300,
  fragmentHeightIOS: 400,
  fragmentHeightH5: 200,
  margin: true,
  widgetId: 'positions-widget / holdings-widget / watchlist-widget',
  data: {
    widgetType: 'positions-widget / holdings-widget / watchlist-widget',
    businessId: 'h5-container-1',
    businessType: 'HOMESCREEN',
    cohortId: 'C1',
    subCohortId: 'SC1',
    title: 'Buy your first Stock!',
    subtitle: 'Top gainers of Nifty200 under ₹500',
    stocks: [
      {
        id: '1000001591',
        name: 'Vodafone Idea',
        exchange: 'NSE',
        segment: 'E',
        metadata: {
          ltp: 3813.35,
          pc: 3873.2,
          prc: -1.545,
          tot_buy_qty_cf: 0,
          tot_sell_qty_cf: 0,
          buy_avg: 3873.2,
          sell_avg: 0,
        },
        inst_type: 'ES',
        sec_id: 14366,
      },
      {
        id: '1000004651',
        name: '<PERSON><PERSON><PERSON>',
        exchange: 'NSE',
        segment: 'E',
        metadata: {
          ltp: 3813.35,
          pc: 3873.2,
          prc: -1.545,
          tot_buy_qty_cf: 0,
          tot_sell_qty_cf: 0,
          buy_avg: 3873.2,
          sell_avg: 0,
        },
        inst_type: 'ES',
        sec_id: 12018,
      },
    ],
    ranges: [
      {
        title: '1 Day',
        range: '1d',
        default: false,
      },
      {
        title: '1 Week',
        range: '1w',
        default: true,
      },
      {
        title: '1 Month',
        range: '1m',
        default: false,
      },
    ],
    buttons: [
      {
        type: 'small-button',
        action: 'watchlist',
        cta: '',
        deeplink: null,
        deeplinkMini: null,
        imageUrl: '',
        imageDarkUrl: '',
        imageType: '',
      },
      {
        type: 'medium-button',
        action: 'orderPad',
        cta: 'Buy',
        deeplink: '',
        deeplinkMini: '',
        imageUrl: '',
        imageDarkUrl: '',
      },
      {
        type: 'large-button',
        action: 'orderPad',
        cta: 'Buy',
        subCta: '3.3x Margin with MTF',
        deeplink: '',
        deeplinkMini: '',
        imageUrl: '',
        imageDarkUrl: '',
      },
      {
        type: 'extra-large-button',
        action: 'orderPad',
        cta: 'Buy',
        subCta: '3.3x Margin with MTF',
        deeplink: '',
        deeplinkMini: '',
        imageUrl: '',
        imageDarkUrl: '',
      },
    ],
    tickerTexts: ['Upcoming Event'],
    closeButton: {
      isVisible: true,
    },
    viewAll: {
      ctaTitle: 'View All',
      deeplink: '',
      deeplinkMini: '',
    },
    sectionTitle: {
      text: 'Positions P&L',
      type: 'Positions P&L',
    },
  },
};
