import { useEffect, useRef, useState } from 'react';
import queryString from 'query-string';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { getStartupParamsAllCallback } from '../../utils/bridgeUtils';
import styles from './index.scss';
import FirstStockCard from '../../components/organisms/FirstStockCard';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import FirstStockLoader from '../../components/atoms/FirstStockLoader/FirstStockLoader';
import { sendErrorToBackend } from '../../actions/runtime';
import { useNotifyHeight } from '../../hooks/useNotifyHeight';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';

export const FirstStockPageContent = (props) => {
  const { data, pages, businessType } = props;
  const [aggrData, setAggrData] = useState(pages && pages.data ? pages : null);
  const firstStockCardRef = useRef(null);

  useNotifyHeight(firstStockCardRef, data?.widgetId || aggrData?.widgetId, {
    flowType:
      BUSINESS_TYPE_MAPPINGS[businessType || pages?.data?.businessType]
        ?.height || 'combinedHomeH5FragmentHeight',
  });

  useEffect(() => {
    if (pages && pages.data) {
      setAggrData(pages);
    }
  }, [pages]);

  if (!data && !aggrData) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'first-stock-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  return data || aggrData ? (
    <div className={styles.firstStockPage} ref={firstStockCardRef}>
      <FirstStockCard data={data || aggrData} />
    </div>
  ) : null;
};

const FirstStockPage = () => {
  const [nativeData, setNativeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [widgetId, setwidgetId] = useState(null);
  const query = queryString.parse(window.location?.search);
  const { businessType, aggrKey } = query || {};

  useEffect(() => {
    getStartupParamsAllCallback((result) => {
      console.log('check nativeData', result);
      if (result?.nativeData) {
        setNativeData(JSON.parse(result.nativeData));
        setwidgetId(result?.widgetId);
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }, []);

  if (isLoading) {
    return <FirstStockLoader />;
  }

  if (nativeData) {
    return (
      <FirstStockPageContent
        data={
          nativeData?.data
            ? nativeData
            : {
                widgetId: widgetId || nativeData?.widgetType,
                data: {
                  ...nativeData,
                },
              }
        }
        businessType={businessType}
      />
    );
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'FirstStock',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <FirstStockLoader />,
  })(FirstStockPageContent);

  return <WrappedComponent />;
};

export default withErrorBoundary(FirstStockPage, false);
