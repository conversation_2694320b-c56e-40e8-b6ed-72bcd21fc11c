import { exitApp, openDeepLinkPaytmMoney } from '../../utils/bridgeUtils';
import NewsWidgetPopup from '../../components/organisms/NewsWidgetPopup';
import { log } from '../../utils/commonUtil';

const NewsWidgetPopupPage = (props) => {
  const companyPageNavigation = (stock = {}) => {
    log('navigate to company page', stock);
    const { pml_id, instrument } = stock;
    const instrumentType =
      instrument === 'ES' ? 'company' : instrument.toLowerCase();
    const url = `https://paytmmoney.com/stocks/${instrumentType}/${pml_id}?shouldOpenOnCurrentScreen=true`;
    openDeepLinkPaytmMoney(url);
  };

  return (
    <NewsWidgetPopup
      isOpen
      onClose={exitApp}
      companyPageNavigation={companyPageNavigation}
      userSelectedNewsItem={props?.data?.data}
    />
  );
};

export default NewsWidgetPopupPage;
