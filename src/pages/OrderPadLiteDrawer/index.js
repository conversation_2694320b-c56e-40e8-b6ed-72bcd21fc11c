import queryString from 'query-string';
import { useNavigate } from 'react-router-dom';

import Drawer from '../../components/molecules/DrawerV2';
import VerifyUserLocationHOC from '../../HOC/VerifyUserLocationHOC';
import PMLDetailsWrapper from '../../HOC/PMLDetailsWrapper';
import OrderPadLite from '../../components/organisms/OrderPadLite';

import { useAppStore } from '../../contexts/AppContextProvider';
import { useOrders } from '../../contexts/OrdersBookContext';

import { sendAnalyticsEventFirstCard } from '../../utils/coreUtil';
import { emptyObj } from '../../utils/commonUtil';
import { APPEARANCE_TYPES } from '../../utils/constants';
import { PULSE_STATICS } from '../../components/organisms/SubsequentFundCard/enums';

const OrderPadLiteDrawer = () => {
  const navigate = useNavigate();
  const { addSnackBar } = useAppStore() || emptyObj;
  const { reFetchOrdersData } = useOrders();
  const query = queryString.parse(window.location?.search);
  const { isFirstTrade, isSameSector } = query || {};
  const sendPulseEvents = (
    action,
    event,
    label = '',
    label2 = '',
    label3 = '',
  ) => {
    sendAnalyticsEventFirstCard({
      event,
      action: isFirstTrade
        ? action
        : isSameSector
          ? `ss_${action}`
          : `ds_${action}`,
      label,
      label2,
      label3,
    });
  };

  const sendOrderStatus = (status) => {
    if (isFirstTrade) {
      sendPulseEvents(
        PULSE_STATICS.ACTION.first_trade.firsttrade_order_status,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
        status,
      );
    } else {
      sendPulseEvents(
        PULSE_STATICS.ACTION.subsequent_trade.subsequent_order_status,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
        status,
      );
    }
  };

  const onOrderPadOpen = () => {
    if (isFirstTrade) {
      sendPulseEvents(
        PULSE_STATICS.ACTION.first_trade.firsttrade_orderpad,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
      );
    } else {
      sendPulseEvents(
        PULSE_STATICS.ACTION.subsequent_trade.subsequent_orderpad,
        PULSE_STATICS.OPEN_SCREEN_EVENT,
      );
    }
  };

  const handleOrderSuccess = (data) => {
    // TODO: remove this log
    console.log('handleOrderSuccess', data);
    if (data?.message) {
      addSnackBar({
        message: data?.message,
        type:
          data?.status?.toLowerCase() === 'success'
            ? APPEARANCE_TYPES.SUCCESS
            : APPEARANCE_TYPES.FAIL,
      });
      sendOrderStatus(data?.status?.toLowerCase());
    }
    if (data?.status?.toLowerCase() === 'success') {
      //   setOrderNo(data?.data?.[0]?.order_no);
      reFetchOrdersData();
    }
    navigate(-1);
  };

  return (
    <Drawer
      isOpen
      onClose={() => {
        navigate(-1);
      }}
      showCross={false}
    >
      <VerifyUserLocationHOC>
        <PMLDetailsWrapper onError={() => navigate(-1)}>
          <OrderPadLite
            isIrOrderPadLite
            isSubsequentTrade={false}
            onOrderSuccess={handleOrderSuccess}
            onOrderPadOpen={onOrderPadOpen}
            isSameSector={isSameSector}
          />
        </PMLDetailsWrapper>
      </VerifyUserLocationHOC>
    </Drawer>
  );
};

export default OrderPadLiteDrawer;
