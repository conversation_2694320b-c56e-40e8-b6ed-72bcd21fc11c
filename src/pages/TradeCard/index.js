import { useEffect } from 'react';
import CentralLoader from '../../components/atoms/CentralLoader/CentralLoader';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { viewConfig } from './CustomCoponents';
import { notifyNativeApp } from '../../utils/bridgeUtils';
import ScrollContent from './scroll';

const TradeCard = ({
  pageComponent: PageComponent,
  pages,
  refetchAggrData,
}) => {
  useEffect(() => {
    notifyNativeApp({
      flowType: 'combinedHomeH5FragmentHeight',
      height: 430,
    });
  }, []);

  const notifyNativeAppBridge = (flowType) => {
    notifyNativeApp({
      flowType,
    });
  };
  // making changes here

  // const [showCard, setShowCard] = useState(false);
  // const handleSubmit = () => {
  //   console.log("dkndd")
  //   setShowCard(true);
  // }

  //

  return (
    <div style={{ height: '430px' }}>
      {/* making changes here */}
      {/* <button onClick={handleSubmit}>Choose Fund</button>

      {showCard && (
        <FundSelectionPopup
          setShowCard={setShowCard} />
      )} */}
      <button
        type="button"
        onClick={() => notifyNativeAppBridge('refreshCombinedHome')}
      >
        refreshCombinedHome
      </button>
      <button
        type="button"
        onClick={() => notifyNativeAppBridge('removeH5FragmentCombinedHome')}
      >
        removeH5FragmentCombinedHome
      </button>
      <ScrollContent />
      {pages?.map((page, index) => (
        <PageComponent
          key={`TradeCard-page-${index}`}
          data={{ ...page, refetchAggrData }}
          viewConfig={viewConfig}
          refetchAggrData={refetchAggrData}
        />
      ))}

    </div>
  );
};

export default AggregatorComponent({
  queryProps: {
    name: 'TradeCard',
    url: AGGREGATOR_API.COMBINED_DASHBOARD,
    fallbackUrl: AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
  },
  loader: <CentralLoader />,
})(TradeCard);
