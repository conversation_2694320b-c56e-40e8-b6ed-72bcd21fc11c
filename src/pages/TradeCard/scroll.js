import { openDeepLinkPaytmMoney } from '../../utils/bridgeUtils';
import styles from './index.scss';

const HorizontalScroll = () => {
  const items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

  return (
    <div className={styles.horizontalScroll}>
      <div className={styles.scrollContainer}>
        {items.map((item, index) => (
          <div
            onClick={() =>
              openDeepLinkPaytmMoney(
                'paytmmoney:///mini-app?aId=a3f92b7c0cba471f853172b02d1c8a4f&pageName=test&phoenixPopup=true',
              )
            }
            key={index}
            className={styles.scrollItem}
          >
            {item}
          </div>
        ))}
      </div>
    </div>
  );
};

export default HorizontalScroll;
