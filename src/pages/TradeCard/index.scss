.horizontalScroll {
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none; // Hide scrollbar for Firefox
  -ms-overflow-style: none; // Hide scrollbar for IE/Edge

  .scrollContainer {
    display: flex;
    gap: 20px; // Space between items
    padding: 10px;
  }

  .scrollItem {
    flex: 0 0 auto;
    width: 272px; // Adjust as needed
    height: 150px;
    background-color: lightblue;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    border-radius: 10px;
  }

  &::-webkit-scrollbar {
    display: none; // Hide scrollbar for Chrome, Safari
  }
}