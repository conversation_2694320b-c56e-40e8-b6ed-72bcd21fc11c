import { useEffect, useState } from 'react';

import { exitApp } from '../../utils/bridgeUtils';
import FundSelectionPopupMonthly from '../../components/organisms/FundSelectionMonthlyPopup/FundSelectionMonthlyPopup';

const FundSelectionMonthlyPage = () => {
  const [preSelectedFund, setPreSelectedFund] = useState(null);

  useEffect(() => {
    setPreSelectedFund(JSON.parse(localStorage.getItem('preSelectedFund')));
    localStorage.removeItem('preSelectedFund');
  }, []);

  return (
    <FundSelectionPopupMonthly
      isOpen
      preSelectedFund={preSelectedFund}
      onClose={exitApp}
    />
  );
};

export default FundSelectionMonthlyPage;
