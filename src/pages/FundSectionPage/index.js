import React, { useState, useEffect } from 'react';
import cx from 'classnames';

import FundListBottomSheet from '../../components/organisms/FundListBottomSheet';
import FundSection from '../../components/organisms/FundSection';
import {
  generatePaytmDeeplink,
  isDarkMode,
  setCollectionName,
  setGenericWidgetCohort,
} from '../../utils/commonUtil';
import {
  // exitApp,
  openDeepLink,
  openDeepLinkPaytmMoney,
} from '../../utils/bridgeUtils';
import {
  EVENT_ACTION_GENERIC_WIDGET,
  mfH5Deeplink,
  mfH5DeeplinkPML,
  PULSE_STATICS_GENERIC_WIDGET,
} from '../../utils/constants';

import styles from './FundSectionPage.scss';
import { useGenericWidgetAnalyticsEvents } from '../../hooks/useGenericWidgetAnalyticsEvents';
import { isPaytmMoney, isSourceMFH5 } from '../../utils/coreUtil';

const FundSectionPage = ({ data: fundInfo }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const fundData = fundInfo?.data;

  const { sendAnalyticsEventGenWidget, isLoading } =
    useGenericWidgetAnalyticsEvents();

  const collectionName = fundData?.widget?.attributes?.find(
    (attr) => attr.name === 'GENERIC_DATA_COLLECTIONNAME',
  )?.value?.collectionName;

  useEffect(() => {
    if (fundData?.meta && collectionName && !isLoading) {
      setGenericWidgetCohort(fundData?.meta?.subCohortId);
      setCollectionName(collectionName);

      sendAnalyticsEventGenWidget({
        action: EVENT_ACTION_GENERIC_WIDGET.GENERIC_WIDGET_VIEW,
        event: PULSE_STATICS_GENERIC_WIDGET.EVENT_OPEN_SCREEN,
      });
    }
  }, [fundData, collectionName, isLoading]);

  const generateOrderPadDeeplink = (fund) => {
    const { aid, params, sparams } = mfH5Deeplink;
    const { isin } = fund || {};
    const eventCategory = isSourceMFH5()
      ? PULSE_STATICS_GENERIC_WIDGET.CATEGORY_MF
      : PULSE_STATICS_GENERIC_WIDGET.CATEGORY;

    const paramsStr = `&isin=${isin}&investmentType=sip&frequency=monthly${
      collectionName
        ? `&collectionName=${collectionName}&eventCategory=${eventCategory}`
        : ''
    }`;
    const orderPadPath = 'buy-order-pad';

    if (isPaytmMoney()) {
      const pmlDeeplink = `${mfH5DeeplinkPML}${orderPadPath}${paramsStr}`;
      openDeepLinkPaytmMoney(pmlDeeplink);
      // exitApp(); commented this to fix back press issue
    } else {
      const deeplinkParam = `${params}${paramsStr}`;
      const deepLinkData = {
        params: deeplinkParam,
        path: orderPadPath,
        sparams,
      };

      const encodedData = window.btoa(JSON.stringify(deepLinkData));
      const deeplink = generatePaytmDeeplink(aid, encodedData);

      openDeepLink(deeplink);

      setTimeout(() => {
        if (isModalOpen) {
          setIsModalOpen(false);
        }
      }, 500);
    }
  };

  const perfRanges =
    fundData?.widget?.attributes?.find(
      (attr) => attr.name === 'GENERIC_PERF_RANGE',
    )?.value || [];
  const contextData = fundData?.widget?.attributes?.find(
    (attr) => attr.name === 'GENERIC_DATA_FETCH',
  )?.value;

  const defaultDuration =
    contextData?.default ||
    perfRanges.find((range) => range.default)?.range ||
    '6m';

  const [selectedDuration, setSelectedDuration] = useState(defaultDuration);

  const handleViewAllClick = () => {
    sendAnalyticsEventGenWidget({
      action: EVENT_ACTION_GENERIC_WIDGET.VIEW_MORE_CLICKED,
    });
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  return (
    <div
      className={cx(styles.root, {
        [styles.darkModeRoot]: isDarkMode(),
      })}
    >
      <FundSection
        data={fundData}
        onViewAllClick={handleViewAllClick}
        selectedDuration={selectedDuration}
        setSelectedDuration={setSelectedDuration}
        onFundClick={generateOrderPadDeeplink}
      />

      <FundListBottomSheet
        isOpen={isModalOpen}
        onClose={handleModalClose}
        fundData={fundData}
        selectedDuration={selectedDuration}
        onFundClick={generateOrderPadDeeplink}
      />
    </div>
  );
};

export default React.memo(FundSectionPage);
