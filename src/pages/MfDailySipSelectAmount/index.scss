@import '/src/commonStyles/variables.scss';

.header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
  width: 100%;
}

.titleContainer {
  margin-left: 10px;
  overflow: hidden;
}

.title {
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  @include typography(heading3B2, var(--text-neutral-strong));
}

.subtitle {
  margin: 0;

  @include typography(body2B3, var(--text-neutral-medium));
}

.sipAmountSection {
  text-align: center;
  margin: 16px 0;
  width: 100%;
}

.sipLabel {
  text-transform:uppercase;
  margin: 0 0 12px;

  @include typography(body2B3, var(--text-neutral-medium));
}

.amountOptions {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  flex-direction: row;
}

.amountButton {
  width: 77px;
  height: 33px;
  border-radius: 96px;
  border: 1px solid var(--text-neutral-medium);
  padding: 8px 12px;
  gap: 4px;
  transition: all 0.3s ease, transform 0.2s ease;
  text-align: center;
  align-items: center;
  display: flex;
  justify-content: center;

  background-color: var(--background-pop-up);
  @include typography(body1R, var(--text-neutral-medium));
}

.amountButton.selected {
  border: 1px solid var(--text-primary-strong);

  @include typography(text, var(--text-primary-strong));
}

.middleDivider {
  height: 1px;
  background-color: var(--text-offset-strong);
  margin-top: 16px;
  width: calc(100% + 32px);
  margin: 0 -16px;
}

.paymentSection {
  display: flex;
  align-items: start;
  justify-content: space-between;
  gap: 0;
  top: 1px;
  margin: 16px 0 0;
  width: 100%;
}

.paymentDetailsContainer {
  display: flex;
  flex-direction: column;
  gap: 0;
  height: auto;
  min-height: 32px;
  flex-grow: 1; /* Allows the container to grow dynamically */
  min-width: 0; /* Ensures proper text wrapping */
  overflow: hidden; /* Prevents content overflow */
}

.paymentMethod {
  margin: 0;

  @include typography(body2B2, var(--text-neutral-strong));
}

.paymentDetails {
  display: flex;
  align-items: center;
  gap: 8px;
}

.insidePayment {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.bankDetails {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;

  @include typography(body2B3, var(--text-neutral-strong));
}

.changeButton {
  margin-top: 0px;
  border: none;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
  transition: all 0.3s ease;

  @include typography(body2B2, var(--text-primary-strong));
}

.insideTitle{
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 16px;
  gap: 6px;
 
}

.insideRating{
  display: flex;
  flex-direction: row;
  align-items: center;

  @include typography(body2B3, var(--text-neutral-medium));
}

.dot{
  @include typography(body2B3, var(--text-neutral-light));
}

.bodyFixedPosition {
  position: fixed;
  width: 100%;
  height: 100%;
}
