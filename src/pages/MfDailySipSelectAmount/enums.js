export const PULSE_STATICS = {
  OPEN_SCREEN_EVENT: 'openScreen',
  VERTICAL_NAME: '/dashboard',
  SCREEN_NAME: 'mutual_fund',
  ACTION: {
    PAGE_LOAD: 'dailysip_miniorderpad',
    PAY_CTA_CLICK: 'dailysip_miniorderpad_cta_clicked',
    ON_CLOSE: 'dailysip_miniorderpad_dropoff',
    ON_OTP_CLOSE: 'dailysip_otp_dropoff',
    ON_CHANGE_CLICK: 'dailysip_miniorderpad_change_clicked',
    ORDERPAD_NOMINEE_POPUP_SCREEN_LANDED:
      'orderpad_nominee_popup_screen_landed',
    ORDERPAD_NOMINEE_POPUP_ADD_ADDITIONAL_DETAILS_CTA_CLICKED:
      'orderpad_nominee_popup_addadditionaldetails_cta_clicked',
    ORDERPAD_NOMINEE_POPUP_I_WISH_NOT_TO_NOMINATE_CTA_CLICKED:
      'orderpad_nominee_popup_iwishnottonominate_cta_clicked',
    ORDERPAD_NOMINEE_POPUP_DROPOFF: 'orderpad_nominee_popup_dropoff',
    DS_WIDGET: 'dailysipwidget_orderpad',
    DAILY_MF_SIP_SCREEN_PAYMENT_OPTIONS_OTP_LANDED:
      'daily_mf_sip_screen_payment_options_otp_landed',
  },
};
