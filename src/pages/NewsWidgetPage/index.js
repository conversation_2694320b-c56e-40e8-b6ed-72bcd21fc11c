import { useCallback, useEffect, useRef, useState } from 'react';
import queryString from 'query-string';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import {
  getStartupParamsAllCallback,
  openDeepLinkPaytmMoney,
} from '../../utils/bridgeUtils';
import NewsWidget from '../../components/organisms/NewsWidget';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import { sendErrorToBackend } from '../../actions/runtime';
import { useNotifyHeight } from '../../hooks/useNotifyHeight';
import {
  BUSINESS_TYPE_MAPPINGS,
  newsWidgetPopupDeeplink,
} from '../../utils/constants';
import { log } from '../../utils/commonUtil';
import NewsWidgetLoader from '../../components/molecules/NewsWidgetLoader';
import { useDrawer } from '../../components/molecules/Drawer/useDrawer';
import { isPaytmMoney } from '../../utils/coreUtil';
import NewsWidgetPopup from '../../components/organisms/NewsWidgetPopup';
import './index.scss';

export const NewsWidgetPageContentWrapperPML = (props) => {
  const {
    data,
    pages,
    businessType,
    aggrKey,
    navigateTo = () => {},
    isListPage = false,
    history = {},
  } = props;

  const [aggrData, setAggrData] = useState(pages && pages.data ? pages : null);
  const [userSelectedNewsItem, setUserSelectedNewsItem] = useState(null);
  const newsWidgetCardRef = useRef(null);

  const {
    isOpen: showNewsWidgetPopup,
    onOpen: onNewsWidgetPopupOpen,
    onClose: onNewsWidgetPopupClose,
  } = useDrawer();

  const showNewsWidgetPopupHandler = () => {
    if (!isPaytmMoney()) {
      onNewsWidgetPopupOpen();
    } else {
      openDeepLinkPaytmMoney(newsWidgetPopupDeeplink);
    }
  };

  useNotifyHeight(newsWidgetCardRef, data?.widgetId || aggrData?.widgetId, {
    flowType:
      BUSINESS_TYPE_MAPPINGS[businessType || pages?.data?.businessType]
        ?.height || 'combinedHomeH5FragmentHeight',
  });

  useEffect(() => {
    if (pages && pages.data) {
      setAggrData(pages);
    }
  }, [pages]);

  const companyPageNavigation = useCallback(
    (stockNewsItem = {}) => {
      log('stockNewsItem', stockNewsItem);
      const { pml_id, instrument } = stockNewsItem;
      const instrumentType =
        instrument === 'ES' ? 'company' : instrument?.toLowerCase();

      if (isPaytmMoney()) {
        const url = `https://paytmmoney.com/stocks/${instrumentType}/${pml_id}?shouldOpenOnCurrentScreen=true`;
        openDeepLinkPaytmMoney(url);
      } else {
        navigateTo(history, `/company-revamp?id=${pml_id}`, {}, 'push');
      }
    },
    [history, navigateTo],
  );

  if (!data && !aggrData) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'news-widget-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  return data || aggrData ? (
    <div ref={newsWidgetCardRef}>
      <NewsWidget
        data={data || aggrData}
        aggrKey={aggrKey}
        businessTypeFallback={businessType || pages?.data?.businessType}
        showNewsWidgetPopupHandler={showNewsWidgetPopupHandler}
        setUserSelectedNewsItem={setUserSelectedNewsItem}
        isListPage={isListPage}
      />
      <NewsWidgetPopup
        isOpen={showNewsWidgetPopup}
        onClose={onNewsWidgetPopupClose}
        userSelectedNewsItem={userSelectedNewsItem}
        companyPageNavigation={companyPageNavigation}
        history={history}
        navigateTo={navigateTo}
      />
    </div>
  ) : null;
};

const NewsWidgetPage = () => {
  const [nativeData, setNativeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [widgetId, setwidgetId] = useState(null);
  const query = queryString.parse(window.location?.search);
  const { businessType, aggrKey } = query || {};

  useEffect(() => {
    getStartupParamsAllCallback((result) => {
      log('check nativeData', result);
      if (result?.nativeData) {
        const parsedData = JSON.parse(result.nativeData);
        localStorage.setItem('newsWidgetData', JSON.stringify(parsedData));
        setNativeData(parsedData);
        setwidgetId(result?.widgetId);
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }, []);

  if (isLoading) {
    return <NewsWidgetLoader />;
  }

  if (nativeData) {
    return (
      <NewsWidgetPageContentWrapperPML
        data={
          nativeData?.data
            ? nativeData
            : {
                widgetId: widgetId || nativeData?.widgetType,
                data: {
                  ...nativeData,
                },
              }
        }
        businessType={businessType}
        aggrKey={aggrKey}
      />
    );
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'NewsWidget',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.STOCKS_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.STOCKS_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <NewsWidgetLoader />,
  })(NewsWidgetPageContentWrapperPML);

  return <WrappedComponent aggrKey={aggrKey} />;
};

export default withErrorBoundary(NewsWidgetPage, false);
