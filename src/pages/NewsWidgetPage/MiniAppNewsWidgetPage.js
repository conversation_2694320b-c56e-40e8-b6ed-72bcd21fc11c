import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { openDeepLinkPaytmMoney } from '../../utils/bridgeUtils';
import NewsWidget from '../../components/organisms/NewsWidget';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import { sendErrorToBackend } from '../../actions/runtime';
import { newsWidgetPopupDeeplink } from '../../utils/constants';
import { log } from '../../utils/commonUtil';
import { useDrawer } from '../../components/molecules/Drawer/useDrawer';
import { isPaytmMoney } from '../../utils/coreUtil';
import NewsWidgetPopup from '../../components/organisms/NewsWidgetPopup';

const NewsWidgetPageContent = (props) => {
  const {
    data,
    pages,
    businessType,
    aggrKey,
    navigateTo = () => {},
    history = {},
    isListPage = false,
  } = props;
  log('isListPage', isListPage);

  const [aggrData, setAggrData] = useState(pages && pages.data ? pages : null);
  const [userSelectedNewsItem, setUserSelectedNewsItem] = useState(null);
  const newsWidgetCardRef = useRef(null);

  const {
    isOpen: showNewsWidgetPopup,
    onOpen: onNewsWidgetPopupOpen,
    onClose: onNewsWidgetPopupClose,
  } = useDrawer();

  const showNewsWidgetPopupHandler = () => {
    if (!isPaytmMoney() || isListPage) {
      onNewsWidgetPopupOpen();
    } else {
      openDeepLinkPaytmMoney(newsWidgetPopupDeeplink);
    }
  };

  useEffect(() => {
    if (pages && pages.data) {
      setAggrData(pages);
    }
  }, [pages]);

  const companyPageNavigation = useCallback(
    (stockNewsItem = {}) => {
      log('navigate to company page', stockNewsItem);
      const { pml_id, instrument } = stockNewsItem;
      const instrumentType =
        instrument === 'ES' ? 'company' : instrument?.toLowerCase();

      if (isPaytmMoney()) {
        const url = `https://paytmmoney.com/stocks/${instrumentType}/${pml_id}?shouldOpenOnCurrentScreen=true`;
        openDeepLinkPaytmMoney(url);
      } else {
        navigateTo(history, `/company-revamp?id=${pml_id}`, {}, 'push');
      }
    },
    [history, navigateTo],
  );

  if (!data && !aggrData) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'news-widget-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  return data || aggrData ? (
    <div ref={newsWidgetCardRef}>
      <NewsWidget
        data={isListPage ? data : data || aggrData}
        aggrKey={aggrKey}
        businessTypeFallback={businessType || pages?.data?.businessType}
        showNewsWidgetPopupHandler={showNewsWidgetPopupHandler}
        setUserSelectedNewsItem={setUserSelectedNewsItem}
        isListPage={isListPage}
      />
      <NewsWidgetPopup
        isOpen={showNewsWidgetPopup}
        onClose={onNewsWidgetPopupClose}
        userSelectedNewsItem={userSelectedNewsItem}
        companyPageNavigation={companyPageNavigation}
        history={history}
        navigateTo={navigateTo}
        isListPage={isListPage}
      />
    </div>
  ) : null;
};

export const NewsWidgetPageContentComponent = memo(
  withErrorBoundary(NewsWidgetPageContent),
);
