import queryString from 'query-string';
import CentralLoader from '../../components/atoms/CentralLoader/CentralLoader';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { sendErrorToBackend } from '../../actions/runtime';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';
import ReminderWidget from '../../components/organisms/ReminderWidget';
import './index.scss';

const ReminderWidgetListWrapper = ({ pages = {} }) => {
  if (!pages.data) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'reminder-widget-list-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  return <ReminderWidget data={pages} isListPage />;
};

const ReminderWidgetListPageRoute = () => {
  const query = queryString.parse(window.location?.search);
  const { aggrKey, businessType } = query || {};

  if (localStorage.getItem('reminderWidgetData')) {
    const parsedData = JSON.parse(localStorage.getItem('reminderWidgetData'));
    return (
      <ReminderWidgetListWrapper
        pages={
          parsedData?.data
            ? parsedData
            : {
                widgetId: 'reminder-widget',
                data: parsedData,
              }
        }
      />
    );
  }

  if (!aggrKey) {
    return null;
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'ReminderWidgetList',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <CentralLoader />,
  })(ReminderWidgetListWrapper);

  return <WrappedComponent />;
};

export default ReminderWidgetListPageRoute;
