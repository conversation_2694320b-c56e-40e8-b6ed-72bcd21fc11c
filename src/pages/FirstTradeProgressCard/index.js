import React, { useEffect, useState } from 'react';

import { getStartupParamsAllCallback } from '../../utils/bridgeUtils';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import FirstTradeProgressCardLoader from '../../components/organisms/FirstTradeProgressCardLoader';
import FirstTradeProgressCard from '../../components/organisms/FirstTradeProgressCard';
import { log } from '../../utils/commonUtil';

import s from './index.scss';

const FirstTradeProgressCardWrapper = () => {
  const [nativeData, setNativeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [widgetId, setwidgetId] = useState(null);
  const [isStale, setStale] = useState(false);

  const addBodyStyles = () => {
    document.body.classList.add(s.nativeBodyMargin);
  };

  const handleVisibilityChange = () => {
    if (!isStale && !document.hidden) {
      setStale(true);
    }
  };

  useEffect(() => {
    addBodyStyles();
    getStartupParamsAllCallback((result) => {
      log('check nativeData', result);
      if (result?.nativeData) {
        setNativeData(JSON.parse(result.nativeData));
        setwidgetId(result?.widgetId);
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));

    document.addEventListener(
      'visibilitychange',
      handleVisibilityChange,
      false,
    );

    return () => {
      document.removeEventListener(
        'visibilitychange',
        handleVisibilityChange,
        false,
      );
    };
  }, []);

  if (isLoading) {
    return <FirstTradeProgressCardLoader />;
  }

  if (!isStale && nativeData) {
    const pages = nativeData?.data
      ? {
          nativeData,
        }
      : {
          widgetId: widgetId || nativeData?.widgetType,
          data: {
            ...nativeData,
          },
        };

    return <FirstTradeProgressCard pages={pages} />;
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'FirstStock',
      url: AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl: AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: 'container-1',
      },
    },
    loader: <FirstTradeProgressCardLoader />,
  })(FirstTradeProgressCard);

  return <WrappedComponent />;
};

export default withErrorBoundary(FirstTradeProgressCardWrapper, false);
