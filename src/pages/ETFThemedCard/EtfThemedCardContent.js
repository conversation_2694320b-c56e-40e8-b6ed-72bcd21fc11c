import { useEffect, useRef, useState } from 'react';
import <PERSON><PERSON> from 'lottie-react';
import cx from 'classnames';

import Shimmer from '../../components/atoms/Shimmer';
import Button from '../../components/atoms/Button/Button';

import { isPaytmMoney } from '../../utils/coreUtil';
import { notifyNativeApp } from '../../utils/bridgeUtils';
import { isDarkMode } from '../../utils/commonUtil';

import ETFDetails from '../../components/organisms/ETFDetails/ETFDetails';
import Icon, { ICONS_NAME } from '../../components/molecules/Icon';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';
import ETF_THEMED_LOTTIE from '../../assets/lottie/etf_themed_card_light.json';

import styles from './EtfThemedCard.scss';

const EtfThemedCardContent = ({
  etfData: {
    popularEtfs: data = [],
    transitionTime = 10,
    returns = [],
    widgetId,
    cta1,
    ...widgetData
  },
  userSelectedEtf,
  showETFPopupHandler,
  handleInvestClick,
  companyPageNavigation,
  onDismissWidget,
}) => {
  const cancelInterval = useRef(null);
  const sipDataIndex = useRef(0);
  const [etfCardData, setEtfCardData] = useState(
    userSelectedEtf || data?.[sipDataIndex.current] || {},
  );

  const [showAnimation, setShowAnimation] = useState(
    !(transitionTime === 0 || data.length < 2) && !userSelectedEtf,
  );

  const [hideWidget, setHideWidget] = useState(
    sessionStorage.getItem('hideGoldWidget') || false,
  );

  useEffect(() => {
    clearInterval(cancelInterval.current);
    cancelInterval.current = null;
    if (userSelectedEtf) {
      setShowAnimation(false);
      setEtfCardData(userSelectedEtf);
      clearInterval(cancelInterval.current);
    }
    if (!showAnimation) return;

    cancelInterval.current = setInterval(() => {
      sipDataIndex.current = (sipDataIndex.current + 1) % data.length;
      setEtfCardData(userSelectedEtf || data?.[sipDataIndex.current] || {});
    }, transitionTime * 1000);

    return () => {
      clearInterval(cancelInterval.current);
      cancelInterval.current = null;
    };
  }, [userSelectedEtf, showAnimation, transitionTime, data]);

  const handleETFPopup = () => {
    setShowAnimation(false);
    showETFPopupHandler(userSelectedEtf || data?.[sipDataIndex.current] || {});
  };

  if (hideWidget) return null;

  return (
    <div
      className={cx(styles.root, {
        [styles.darkModeRoot]: isDarkMode(),
      })}
    >
      <div
        className={cx(styles.stickyTopImage, {
          [styles.darkModeStickyTopImage]: isDarkMode(),
        })}
      />
      <div className={styles.header}>
        <div className={styles.lottieContainer}>
          <Lottie
            style={{
              position: 'absolute',
              top: '0',
              left: '0',
              width: '100%',
              zIndex: 10,
            }}
            className={styles.leftLottie}
            animationData={ETF_THEMED_LOTTIE}
            autoplay
            loop
          />
        </div>
        <div className={styles.dailySipContainer}>
          <div className={styles.equitySipTitle} />
          <div className={styles.equitySipSubtitle}>{widgetData?.subTitle}</div>
        </div>
        <Icon
          name={ICONS_NAME.CLOSE_ICON}
          className={styles.crossIcon}
          size={4}
          onClick={() => {
            onDismissWidget();
            setHideWidget(true);
            sessionStorage.setItem('hideGoldWidget', true);
            if (isPaytmMoney()) {
              notifyNativeApp({
                flowType:
                  BUSINESS_TYPE_MAPPINGS[widgetData.businessType]
                    ?.removeFragment || 'removeH5FragmentCombinedHome',
                widgetId,
              });
            }
          }}
        />
      </div>
      {etfCardData?.name ? (
        <ETFDetails
          etf={etfCardData}
          showPopupHandler={handleETFPopup}
          showAnimation={showAnimation}
          transitionTime={transitionTime}
          returns={returns}
          widgetData={widgetData}
          companyPageNavigation={companyPageNavigation}
          sipTitleCustomClass={styles.sipTitleCustomClass}
          sipReturnsCustomClass={styles.sipReturnsCustomClass}
          showSipLoaderBg
        />
      ) : (
        <Shimmer height="50px" width="100%" />
      )}
      <div className={styles.footer}>
        <Button
          isPrimary
          className={styles.startSipBtn}
          buttonText={cta1?.cta}
          buttonTextClassName={styles.startSipBtnText}
          onClickHandler={() => handleInvestClick(etfCardData, cta1)}
        />

        {widgetData?.disclaimer && (
          <div className={styles.footerTextContainer}>
            <div className={styles.sipDisclaimerNote}>
              {widgetData?.disclaimer}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EtfThemedCardContent;
