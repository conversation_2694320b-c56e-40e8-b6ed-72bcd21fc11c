@import '/src/commonStyles/variables.scss';

.sipCardContainer {
  border-radius: 16px;
}

.root {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 16px;
  background-image: url(../../assets/images/gold-etf-theme1.png);
  background-size: cover;
  backdrop-filter: blur(34px);
  background-color: var(--background-notice-weak);
  --etf-card-details-bg: linear-gradient(97.25deg, rgba(254, 213, 51, 0.2) 0%, rgba(245, 143, 0, 0.2) 100%);
  --etf-returns-bg: #FFFFFF99;
  --etf-title-bg: linear-gradient(92.38deg, #FED533 -0.12%, #F58F00 99.87%);
  position: relative;
  overflow: hidden;
}

.darkModeRoot {
  background-image: url(../../assets/images/gold-etf-theme1-dark.png);
}

.lottieContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.stickyTopImage {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 454px;
  height: 27px;
  background-image: url(../../assets/images/gold-etf-theme1-garland.png);
  background-size: auto;
  background-position: top center;
  background-repeat: no-repeat;
  z-index: 1;
  pointer-events: none;
}

.darkModeStickyTopImage {
  background-image: url(../../assets/images/gold-etf-theme1-garland-dark.png);
}

.header {
  display: flex;
  position: relative;
  align-self: stretch;
  align-items: center;
  justify-content: center;
}

.sipTitleCustomClass {
  background: var(--background-notice-weak) !important;
}

.sipReturnsCustomClass {
  background: var(--background-notice-weak) !important;
}

.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 90%;
  padding-bottom: 12px;

  .startSipBtn {
    position: relative;
    background-color: var(--background-primary-strong);
    border-radius: 8px;
    overflow: hidden;
    //z-index: 1;
    width: unset;
    margin: 0;
    align-self: stretch;
    margin-top: 24px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        110deg,
        rgba(255, 255, 255, 0) 10%,
        rgba(255, 255, 255, 0.35) 12%,
        rgba(255, 255, 255, 0.4) 14%,
        rgba(255, 255, 255, 0.35) 16%,
        rgba(255, 255, 255, 0) 18%
      );

      background-size: 200% 100%;
      animation: shine 5s infinite linear;
      z-index: 0;
      pointer-events: none;
    }

    .startSipBtnText {
      font-weight: 500;
      position: relative;
    }
  }
}

.footerTextContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 24px;
  margin-top: 8px;
  @include typography(body3B2, var(--text-neutral-strong));
}

.dailySipContainer{
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  padding-top: 27px;
}

.equitySipTitle {
  background-image: url(../../assets/images/gold-etf-title-theme1.png);
  height: 33px;
  width: 136px;
}
.darkModeEquitySipTitle {
  background-image: url(../../assets/images/gold-etf-title-theme1-dark.png);
}

.equitySipSubtitle {
  @include typography(body2B2, var(--text-neutral-strong));
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.crossIcon {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 11;
}
