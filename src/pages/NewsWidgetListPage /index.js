import queryString from 'query-string';
import CentralLoader from '../../components/atoms/CentralLoader/CentralLoader';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { sendErrorToBackend } from '../../actions/runtime';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';
import { NewsWidgetPageContentWrapperPML } from '../NewsWidgetPage';
import './index.scss';

const NewsWidgetListWrapper = ({ pages = {} }) => {
  if (!pages.data) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'news-widget-list-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  return <NewsWidgetPageContentWrapperPML data={pages} isListPage />;
};

const NewsWidgetListPageRoute = () => {
  const query = queryString.parse(window.location?.search);
  const { aggrKey, businessType } = query || {};

  if (localStorage.getItem('newsWidgetData')) {
    const parsedData = JSON.parse(localStorage.getItem('newsWidgetData'));
    return (
      <NewsWidgetListWrapper
        pages={
          parsedData?.data
            ? parsedData
            : {
                widgetId: 'news-widget',
                data: parsedData,
              }
        }
      />
    );
  }

  if (!aggrKey) {
    return null;
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'NewsWidgetList',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.STOCKS_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.STOCKS_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <CentralLoader />,
  })(NewsWidgetListWrapper);

  return <WrappedComponent />;
};

export default NewsWidgetListPageRoute;
