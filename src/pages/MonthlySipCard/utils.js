export const TEXT_DATA = {
  MUTUAL_FUND: 'Mutual Fund',
  SIP_FOR_WEALTH_CREATION: 'SIP for Wealth Creation',
  MUTUAL_FUND_SAHI_HAI: 'Mutual Fund Sahi Hai',
  START_SIP: 'Start SIP',
  MOST_SIPD: `Most SIP'd`,

  INVEST_DAILY: 'Invest Daily',
  SIP_DISCLAIMER_NOTE: (cagr) => `*Assuming CAGR of (${cagr}%), `,
  READ_FULL_DISCLAIMER: 'Read full disclaimer here',
  RIGHT_TIME_TO_START_INVESTMENT: '💪🏼 The right time to start a good habit is NOW!',
};

export const convertToLakhsOrCrore = (amount, sipAmount) => {
  const amountInRupees = (amount || 0) * (sipAmount || 0);
  const amountInLakhs = +((amountInRupees / 100000).toFixed(3));

  if (amountInLakhs >= 100) {
    const amountInCrore = +((amountInLakhs / 100).toFixed(3));
    return amountInCrore.toString().split('.')[0].length > 1 ? `${amountInCrore.toFixed(1)}Cr` : `${amountInCrore.toFixed(2)}Cr`;
  }

  return amountInLakhs.toString().split('.')?.[0]?.length > 1
    ? `${amountInLakhs?.toFixed(1)}L`
    : `${amountInLakhs?.toFixed(2)}L`;
};

export const ATTRIBUTES = [
  {
    targetName: 'popularMfs',
    attributeName: 'POPULAR_MF_LIST',
    defaultValue: [],
  },
  {
    targetName: 'subTitle',
    attributeName: 'SUB_TITLE_MFSIP',
    defaultValue: '',
  },
  {
    targetName: 'socialProofings',
    attributeName: 'SOCIAL_PROOFING',
    defaultValue: [],
  },
  {
    targetName: 'disclaimerText',
    attributeName: 'DISCLAIMER_TEXT',
    defaultValue: '',
  },
  {
    targetName: 'cagrs',
    attributeName: 'PRE_CAGR_DISCLAIMER',
    defaultValue: [],
  },
  {
    targetName: 'cta',
    attributeName: 'BUTTON_START_SIP_MONTHLY',
    defaultValue: {},
  },
];

