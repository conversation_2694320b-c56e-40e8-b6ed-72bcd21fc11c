import { NewsWidgetPageContentWrapperPML } from '../../NewsWidgetPage';
import FirstTradeProgressCard from '../../../components/organisms/FirstTradeProgressCard';
import SipCard from '../../SipCard';
import MonthlySipCard from '../../MonthlySipCard';
import ETFCard from '../../ETFCard';
import ReminderWidget from '../../../components/organisms/ReminderWidget';
import FOIndexAnalysisWidget from '../../../components/organisms/FOIndexAnalysisWidget';
import { FirstStockPageContent } from '../../FirstStockPage';
import FundSectionPage from '../../FundSectionPage';

const WidgetWrapper = (props) => {
  if (
    [
      'holdings-widget',
      'positions-widget',
      'watchlist-widget',
      'most-bought-widget',
    ].includes(props.data?.widgetId)
  ) {
    return <FirstStockPageContent {...props} />;
  }

  if (props.data?.widgetId === 'pre-ir-invest-widget') {
    return <FirstTradeProgressCard {...props} />;
  }

  if (props.data?.widgetId === 'gold-etf-card') {
    return <ETFCard {...props} isCrossSellingWidget />;
  }

  if (props.data?.widgetId === 'gold-etf-card-at') {
    return <ETFCard {...props} />;
  }

  if (props.data?.widgetId === 'reminder-widget') {
    return <ReminderWidget {...props} />;
  }

  if (props.data?.widgetId === 'generic-news-widget') {
    return <NewsWidgetPageContentWrapperPML {...props} />;
  }

  if (props.data?.widgetId === 'fno-index-analysis-widget') {
    return <FOIndexAnalysisWidget {...props} />;
  }
  // Below are the  MF widgets

  // DS widget
  if (props.data?.widgetId === 'mf-sip-card') {
    return <SipCard {...props} />;
  }

  // MS widget
  if (props.data?.widgetId === 'sip-completion-widget') {
    return <MonthlySipCard {...props} />;
  }

  // Generic widget
  if (props.data?.widgetId === 'generic-mf-widget') {
    return <FundSectionPage {...props} />;
  }

  return null;
};

export default WidgetWrapper;
