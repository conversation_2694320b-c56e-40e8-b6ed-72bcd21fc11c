import leftArrow from '../../assets/images/LeftArrow.svg';
import leftArrowDark from '../../assets/images/LeftArrowDark.svg';
import threeDotMenu from '../../assets/images/3DotMenu.svg';
import LightBlueLine from '../../assets/images/LightBlueLine.svg';
import DarkBlueLine from '../../assets/images/DarkBlueLine.svg';
import cardPhoneMail from '../../assets/images/cardphonemail.svg';
import checkmark from '../../assets/images/checkmark.svg';
import checkmarkDark from '../../assets/images/checkmarkDark.svg';
import ExploreEquityRightArrow from '../../assets/images/ExploreEquityRightArrow.svg';
import orderMtfReminder from '../../assets/images/order_mtf_reminder.png';
import orderMtfReminderDark from '../../assets/images/order_mtf_reminder_dark.png';
import EQBanner from '../../assets/images/EQBanner.svg';
import EQBannerDark from '../../assets/images/EQBannerDark.svg';

export const MFEQUITY_ICONS = {
  LEFT_ARROW: {
    light: leftArrow,
    dark: leftArrowDark,
  },
  MENU: {
    light: threeDotMenu,
    dark: threeDotMenu,
  },
  LIGHT_BLUE_LINE: {
    light: LightBlueLine,
    dark: LightBlueLine,
  },
  DARK_BLUE_LINE: {
    light: DarkBlueLine,
    dark: DarkBlueLine,
  },
  CARD_PHONE_MAIL: {
    light: cardPhoneMail,
    dark: cardPhoneMail,
  },
  CHECKMARK: {
    light: checkmark,
    dark: checkmarkDark,
  },
  EXPLORE_EQUITY_RIGHT_ARROW: {
    light: ExploreEquityRightArrow,
    dark: ExploreEquityRightArrow,
  },
  ORDER_MTF_REMINDER: {
    light: orderMtfReminder,
    dark: orderMtfReminderDark,
  },
  EQ_BANNER: {
    light: EQBanner,
    dark: EQBannerDark
  },
};

// Helper to get the correct icon for the current theme
export function getEquityIcon(name, theme = 'light') {
  return MFEQUITY_ICONS[name]?.[theme] || '';
}