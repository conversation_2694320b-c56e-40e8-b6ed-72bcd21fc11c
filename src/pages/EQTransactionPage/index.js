import { useEffect } from 'react';
import cx from 'classnames';
import styles from './EQTransactionPage.scss';
import { getEquityIcon } from './icons';
import { MFEQUITY_CROSS_SELLING , EQ_TRANSACTION_EVENTS} from './constants';
import Header from './Header';
import Banners from './Banners/Banners';
import WidgetWrapper from './WidgetComponents/viewConfig';
import { useWidgets } from '../../query/widgetQuery';
import {
  getDeeplinkData,
  isDarkMode,
  formatTimestamp,
} from '../../utils/commonUtil';
import { formatPrice } from '../../components/molecules/Prices';
import { isPaytmMoney } from '../../utils/coreUtil';
import { openDeepLinkPaytm, openDeepLinkPaytmMoney } from '../../utils/bridgeUtils';
import { useAnalyticsEventForOrderPlacedWidget } from '../../hooks/analyticsHooks';

const EQTransactionPage = ({ isMiniApp = false ,stockData={}}) => {
  const stockName = isMiniApp ? stockData?.stockName : getDeeplinkData('stock_name');
  const qty = isMiniApp ? stockData?.qty : getDeeplinkData('qty');
  const price =  isMiniApp ? stockData?.price :getDeeplinkData('avg_traded_price');
  const orderTime =  isMiniApp ? stockData?.orderTime :getDeeplinkData('order_time');

  const theme = isDarkMode() ? 'dark' : 'light';

  const { data } = useWidgets(!isMiniApp);
  const { sendAnalyticsEventWidget } = useAnalyticsEventForOrderPlacedWidget();

  const { data: { widget } = {} } = data || {};
  const { businessType } = widget || {};
  const buttonStyle = isDarkMode()
    ? {
        borderRadius: '40px',
        border: '1px solid var(--Border-Offset-2-Strong, #1B3C56)',
        background: 'var(--Background-Offset-2-Medium, #182732)',
        color: 'var(--Text-Primary-Variant, #45A1FF)',
        fontSize: '12px',
        fontStyle: 'normal',
        fontWeight: 600,
        lineHeight: '16px',
      }
    : {
        borderRadius: '40px',
        border: '1px solid var(--Border-Offset-2-Strong, #D8E7F7)',
        background: 'var(--Background-Offset-2-Medium, #ECF2F8)',
        color: 'var(--Text-Primary-Variant, #004299)',
        fontSize: '12px',
        fontStyle: 'normal',
        fontWeight: 600,
        lineHeight: '16px',
      };

  useEffect(() => {
    document.body.style.setProperty('overflow', 'auto', 'important');
  }, [data]);

  useEffect(() => {
    try {
      if (stockName){
    sendAnalyticsEventWidget({
      ...EQ_TRANSACTION_EVENTS.PAGE_LANDED,
      label2: stockName || '',
        });
      }
    } catch (err) {
      console.error('Analytics event error (Order Success screen):', err);
    }
  }, [sendAnalyticsEventWidget, stockName, isMiniApp]);

  const handleClickOrderDetail = () => {
    if (isPaytmMoney()) {
      const url = `https://www.paytmmoney.com/stocks/orders/successful?shouldOpenOnCurrentScreen=true`;
      openDeepLinkPaytmMoney(url);
    } else {
      openDeepLinkPaytm("paytmmp://paytmmoney/stocks/equity-dashboard?activeTab=orders&dl=true")
    }
  };

  const handleClickViewPortfolio = () => {
    if (isPaytmMoney()) {
      const url = `https://www.paytmmoney.com/stocks/portfolio?shouldOpenOnCurrentScreen=true&returns=today`;
      openDeepLinkPaytmMoney(url);
    } else {
      openDeepLinkPaytm("paytmmp://paytmmoney/stocks/equity-dashboard?activeTab=holdings&dl=true")
    }
  };

  return (
    <div
      className={cx(styles.root, {
        [styles.lightMode]: theme === 'light',
        [styles.darkMode]: theme === 'dark',
        [styles.inMainApp]: !isMiniApp,
      })}
    >
      <Header />
      {(stockName && qty && price && orderTime && (
        <>
          <div className={styles.mainContent}>
            <div className={styles.orderPlacedSuccess}>
              {MFEQUITY_CROSS_SELLING.ORDER_PLACED_TITLE}
            </div>
            <div className={styles.amount}>
              {formatPrice(parseFloat(qty) * parseFloat(price), 2)}
              <img
                src={getEquityIcon('CHECKMARK', theme)}
                alt="Checkmark"
                className={styles.checkmarkIcon}
              />
            </div>
            <div className={styles.orderDetails}>
              {MFEQUITY_CROSS_SELLING.ORDER_PLACED_DETAILS}{' '}
              <span className={styles.stockName}>{stockName}</span>{' '}
              {MFEQUITY_CROSS_SELLING.ORDER_PLACED_TIME(
                formatTimestamp(orderTime),
              )}
            </div>
            <div className={styles.buttonRow}>
              <button
                type="button"
                className={styles.orderButton}
                style={buttonStyle}
                onClick={handleClickOrderDetail}
              >
                {MFEQUITY_CROSS_SELLING.ORDER_DETAILS_BUTTON}
              </button>
              <button
                type="button"
                className={styles.orderButton}
                style={buttonStyle}
                onClick={handleClickViewPortfolio}
              >
                {MFEQUITY_CROSS_SELLING.VIEW_PORTFOLIO_BUTTON}
              </button>
            </div>
          </div>
          <img
            src={getEquityIcon('LIGHT_BLUE_LINE', theme)}
            alt="Light Blue Line"
            className={styles.blueLineLast}
          />
          <img
            src={getEquityIcon('DARK_BLUE_LINE', theme)}
            alt="Dark Blue Line"
            className={styles.blueLine}
          />
        </>
      )) ||
        null}
      <Banners stockName={stockName} />
      {!isMiniApp && (
        <div style={{ paddingTop: '8px' }}>
          <WidgetWrapper
            data={data}
            pages={data}
            businessType={businessType}
            aggrKey="container-1"
          />
        </div>
      )}
    </div>
  );
};

export default EQTransactionPage;
