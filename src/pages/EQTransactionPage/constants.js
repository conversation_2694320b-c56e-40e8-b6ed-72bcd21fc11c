export const MFEQUITY_CROSS_SELLING = {
  ORDER_PLACED_TITLE: 'Order Placed Successfully',
  ORDER_PLACED_DETAILS: 'Your order for',
  ORDER_PLACED_TIME:(time)=> `has been placed successfully on  ${time}`,
  ORDER_DETAILS_BUTTON: 'Order Details',
  VIEW_PORTFOLIO_BUTTON: 'View Portfolio',
  WIDGET_TITLE: 'What Can You Do with Your Money',
  WIDGET_SUBTITLE: 'SIPs build discipline. But equity lets you act on market moments instantly.',
  WIDGET_BUTTON: 'Explore Equity',
};

export const EQ_TRANSACTION_EVENTS = {
  PAGE_LANDED: {
    screenName: '/cross_sell',
    verticalName: 'equity',
    event: 'openScreen',
    category: 'cross_sell_payment_confirmation_equity',
    action: 'cross_sell_payment_confirmation_page_landed',
  },
  BANNER_CLICKED: {
    screenName: '/cross_sell',
    verticalName: 'equity',
    event: 'custom_event',
    category: 'cross_sell_payment_confirmation_equity',
    action: 'explore_mf_cta_clicked',
  },
};