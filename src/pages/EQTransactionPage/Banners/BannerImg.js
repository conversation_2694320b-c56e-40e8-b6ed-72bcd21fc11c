import React, { useState } from 'react';

const BannerImg = ({ alt, ...props }) => {
  const [showLoader, setShowLoader] = useState(true);

  const handleLoader = () => {
    setShowLoader(false);
  };

  return (
    <>
      <img
        // style={showLoader ? { display: 'none' } : {}}
        {...props}
        alt={alt}
        onLoad={handleLoader}
      />
    </>
  );
};

export default BannerImg;
