import cx from 'classnames';

import BannerImg from './BannerImg';
import { isDarkMode } from '../../../utils/commonUtil';
import { useBanners } from '../../../query/widgetQuery';

import { isPaytmMoney } from '../../../utils/coreUtil';
import {
  openDeepLinkPaytm,
  openDeepLinkPaytmMoney,
} from '../../../utils/bridgeUtils';
import styles from './Banner.scss';
import { useAnalyticsEventForOrderPlacedWidget } from '../../../hooks/analyticsHooks';
import { EQ_TRANSACTION_EVENTS } from '../constants';

const Banners = ({ stockName }) => {
  const { data } = useBanners();
  const bannerList = data?.data || [];
  const { sendAnalyticsEventWidget } = useAnalyticsEventForOrderPlacedWidget();

  const handleBannerClick = (banner, index) => {
    const deeplinkUrl = isPaytmMoney()
      ? banner?.deeplink
      : banner?.deeplinkMini;

    // Analytics event
    const eventPayload = {
      ...EQ_TRANSACTION_EVENTS.BANNER_CLICKED,
      label2: stockName || '',
    };
    sendAnalyticsEventWidget(eventPayload);
    if (isPaytmMoney()) {
      openDeepLinkPaytmMoney(deeplinkUrl);
    } else {
      openDeepLinkPaytm(deeplinkUrl);
    }
  };

  return bannerList.length ? (
    <div className={styles.root}>
      {bannerList.map((bannner, index) => (
        <BannerImg
          key={`banner-key-${index}`}
          className={cx({
            [styles.singleImg]: bannerList.length === 1,
            [styles.img]: bannerList.length !== 1,
          })}
          src={isDarkMode() ? bannner.imageUrlDark : bannner.imageUrl}
          alt=""
          onClick={() => handleBannerClick(bannner, index)}
        />
      ))}
      {bannerList.length > 1 && <div className={styles.emptySpace} />}
    </div>
  ) : null;
};

export default Banners;
