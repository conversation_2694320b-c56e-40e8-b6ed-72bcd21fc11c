import { getEquityIcon } from './icons';
import styles from './EQTransactionPage.scss';
import { isDarkMode } from '../../utils/commonUtil';
import { exitApp } from '../../utils/bridgeUtils';
import { isPaytmMoney } from '../../utils/coreUtil';

const Header = () => {
  const theme = isDarkMode() ? 'dark' : 'light';

  const onBackClick = () => {
      exitApp();
  };

  return (
    <div className={styles.headerContainer}>
      <div className={styles.topLeftIcon} onClick={onBackClick}>
        <img
          src={getEquityIcon('LEFT_ARROW', theme)}
          alt="Back"
          className={styles.headerIconBack}
        />
      </div>
      {/* <div className={styles.topRightIcon}>
        <img src={getEquityIcon('MENU', theme)} alt="Menu" className={styles.headerIcon} />
      </div> */}
    </div>
  );
};

export default Header;
