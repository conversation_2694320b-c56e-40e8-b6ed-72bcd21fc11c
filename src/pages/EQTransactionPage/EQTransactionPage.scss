@import "../../commonStyles/mixins.scss";

.root {
    display: flex;
    flex-direction: column;
    margin: 0;
    padding-bottom: 16px;

    @include noScrollBar()
}

html,
body {
    height: 100% !important;
    margin: 0 !important;
    background-color: var(--background-default2);
    @include noScrollBar()
}

.lightMode {
    background: linear-gradient(180deg,
            #eaf4ff 0%,
            #eaf4ff 10%,
            #fff 20%,
            #fff 100%);
}

.darkMode {
    background: linear-gradient(180deg,
            #092535 0%,
            #092535 10%,
            #101010 20%,
            #101010 100%);
}

.inMainApp {
    min-height: 100vh;
}

.headerIcon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 4px rgba(16, 16, 16, 0.06);
    padding: 0;
}

.headerIconBack {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--background-universal-strong, #fff);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 4px rgba(16, 16, 16, 0.06);
    padding: 10px;
}

.headerIconBack img {
    width: 22px;
    height: 22px;
}

.topLeftIcon .icon,
.topRightIcon .icon {
    padding: 0;
    width: 100%;
    height: 100%;
}

.icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 4px rgba(16, 16, 16, 0.06);
}

.topLeftIcon .icon {
    padding: 6px;
}

.buttonRow {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 16px;
    margin-bottom: 32px;
    @include preventSelect;
}



.orderButton {
    margin: 0;
    display: flex;
    width: 120px;
    height: 32px;
    padding: 0px 12px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 40px;
    border: 1px solid var(--Border-Offset-2-Strong, #1B3C56);
    background: var(--Background-Offset-2-Medium, #182732);
    color: var(--text-universal-strong, #fff);
    font-family: 'Inter Subset', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
    @include preventSelect;
}

.orderPlacedSuccess {
    color: var(--text-neutral-strong, #101010);
    text-align: center;
    font-family: Inter, sans-serif;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 22px;
    /* 137.5% */
    letter-spacing: 0.01px;
    margin-bottom: 12px;
    background: none;

    @include preventSelect;
}

.amount {
    color: var(--text-neutral-strong, #101010);
    font-family: Inter, sans-serif;
    font-size: 42px;
    font-style: normal;
    font-weight: 700;
    line-height: 54px;
    /* 128.571% */
    letter-spacing: -0.009px;
    display: flex;
    align-items: center;
    justify-content: center;

    @include preventSelect;
}

.orderDetails {
    color: var(--text-neutral-strong, #101010);
    text-align: center;
    font-family: Inter, sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    padding: 0 12px;

    @include preventSelect;
}

.stockName {
    color: var(--text-neutral-strong, #101010);
    font-family: "Inter Subset", sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
}

.mainContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    margin-top: 0;
}

.headerWrapper {
    width: 100%;
    padding: 0;
    position: relative;
}

.widgetContainer {
    display: flex;
    width: 360px;
    padding: 16px 8px 16px 16px;
    align-items: center;
    gap: 19px;
    background: linear-gradient(90deg, var(--background-universal-strong, #fff) 0%, var(--background-offset-strong, #e7f1f8) 100%);
    border-radius: 24px;
    box-shadow: 0 2px 12px 0 rgba(16, 16, 16, 0.06);
    font-family: Inter, sans-serif;
    margin-top: 24px;
}

.blueLine {
    width: -webkit-fill-available;
    display: block;
}

.blueLineLast {
    width: -webkit-fill-available;
    display: block;
    margin-bottom: 0;
}

.checkmarkIcon {
    margin-left: 8px;
}

.blueLine:last-child {
    margin-bottom: 24px;
}

.headerContainer {
    display: flex;
    justify-content: space-between;
    padding: 12px 8px;
}

html.dark .root {
    background: linear-gradient(180deg,
            var(--background-offset-strong, #0d222e) 0%,
            var(--background-offset-strong, #0d222e) 10%,
            var(--background-neutral-strong, #101010) 20%,
            var(--background-neutral-strong, #101010) 100%);
}

html.dark .orderPlacedSuccess,
html.dark .amount,
html.dark .orderDetails,
html.dark .tataMotors {
    color: var(--text-universal-strong, #fff);
}

html.dark .root .orderButton {
    border: 1px solid var(--Border-Offset-2-Strong, #1B3C56);
    background: var(--Background-Offset-2-Medium, #182732);
    color: var(--text-universal-strong, #fff);
}