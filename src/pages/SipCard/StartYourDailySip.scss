@import '/src/commonStyles/variables.scss';

.sipCardContainer {
  border-radius: 16px;
}

.root {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 16px;
  background: linear-gradient(180deg, #E0F5FD, #C2DAF3);
  padding-bottom: 8px;
}

.darkModeRoot {
  background: linear-gradient(180deg, #1c1157 0%, #001128 20%, #202020 100%);
}

.header {
  display: flex;
  position: relative;
  align-self: stretch;
  align-items: center;
  justify-content: center;
}

.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .startSipBtn {
    position: relative;
    background-color: var(--background-primary-strong);
    border-radius: 8px;
    overflow: hidden;
    //z-index: 1;
    width: unset;
    margin: 0 12px;
    align-self: stretch;
    margin-bottom: 8px;
    margin-top: 24px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        110deg,
        rgba(255, 255, 255, 0) 10%,
        rgba(255, 255, 255, 0.35) 12%,
        rgba(255, 255, 255, 0.4) 14%,
        rgba(255, 255, 255, 0.35) 16%,
        rgba(255, 255, 255, 0) 18%
      );
      background-size: 200% 100%;
      animation: shine 5s infinite linear;
      z-index: 0;
      pointer-events: none;
    }

    .startSipBtnText {
      font-weight: 500;
      position: relative;
    }
  }

  .sipDisclaimerNote {
    @include typography(body3R, var(--text-neutral-weak));
  }

  .readDisclaimerBtn {
    background-color: transparent;
    padding: 0px;
    height: fit-content;

    .readDisclaimerBtnText {
      @include typography(body3R, var(--text-primary-strong));
    }
  }
}

.footerTextContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 24px;
}
 
.cardRoot {
  display: flex;
  flex-direction: column;
  background-color: var(--background-offset-strong);
  border-radius: 12px;
  margin: 0 12px;
  box-shadow: 0px 0px 8px 2px #00000014;

}

.sipWealthPlanText {
  padding: 6px 12px;
  @include typography(body3R3, var(--text-neutral-weak));
}

.finalWealthAmtText {
  display: inline;
  color: map-get($colors, var(--text-neutral-strong));
}

.dailySipContainer{
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;


  // .smallCapHeading {
  //   font-family: Inter;
  //   font-weight: 700;
  //   font-size: 16px;
  //   line-height: 22px;
  //   letter-spacing: 0.01px;
  //   text-align: center;
  //   vertical-align: middle;
  // }
  
  // .smallCapHeadingDark {
  //   color: #EEEEEE;
  // }

  > p {
    margin: 0px;
  }
}

.dailyMfSipSubText {
  @include typography(body2, var(--text-neutral-weak));
}

.sipTitle {
  align-self: stretch;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  padding: 12px 20px;
  background: var(--surface-level-3);

  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;

  & > img {
    width: auto;
  }
}

.sipDetails {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  height: 32px;

  .sipName {
    @include typography(small-text1C, var(--text-neutral-strong));
    font-weight: 500;
    font-size: 12px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: calc(100vw - 168px);
  }
}

.sipReturnsData {
  display: inline-flex;
  align-items: center;
  gap: 2px;

  .sipReturnsLabel {
    font-size: 12px;
    font-weight: 400;

    @include typography(small-text1, var(--text-neutral-weak));
  }

  .sipReturnsPercentage {
    font-size: 12px;
    font-weight: 600;
  }

  .positive {
    @include typography(small-text1A, var(--background-positive-strong));
  }

  .negative {
    @include typography(small-text1A, var(--background-negative-strong));
  }
}

.dropDownIcon {
  width: 24px;
  height: 24px;
}

.sipLoader {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 0;
  height: 2px;
  animation: loading linear forwards;
  background: linear-gradient(90deg, #00B8F5 0%, #2F81ED 100%);
}

.sipLoaderDark {
  background: linear-gradient(90deg, #0A86BF 0%, #2361B2 100%);
}

.animationPause {
  animation-play-state: paused;
}

.animationResume {
  animation-play-state: running;
}

@keyframes loading {
  0% {
    width: 0%;
    background-size: 200% 100%;
  }
  40% {
    background-size: 200% 100%;
  }
  50% {
    background-size: 200% 100%;
  }
  75% {
    background-size: 200% 100%;
  }
  100% {
    width: 100%;
    background-size: 200% 100%;
  }
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

