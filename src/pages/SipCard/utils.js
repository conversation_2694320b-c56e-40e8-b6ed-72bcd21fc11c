export const TEXT_DATA = {
  START_YOUR_DAILY_SIP_BTN: 'Start Now',
  INVEST_DAILY: 'Invest Daily',
  SIP_DISCLAIMER_NOTE: (cagr) =>
    `*Using past performance CAGR (${cagr}%) & 250 business days p.a.`,
  READ_FULL_DISCLAIMER: 'Read full disclaimer here',
  INVEST_SMALL_GROW_BIG: 'Invest Small, Grow Big',
  DAILY_SIP_INVESTMENT: 'Daily Mutual Fund SIP from ₹21',
  RIGHT_TIME_TO_START_INVESTMENT: '💪🏼 The right time to start a good habit is NOW!',
  // SMALL_CAP_OPPORTUNITY_STARTS_AT_21: 'Small Cap Opportunity Starts at ₹21',
};

export const convertToLakhsOrCrore = (amount, sipAmount) => {
  const amountInRupees = (amount || 0) * (sipAmount || 0);
  const amountInLakhs = +((amountInRupees / 100000).toFixed(3));
  
  if (amountInLakhs >= 100) {
    const amountInCrore = +((amountInLakhs / 100).toFixed(3));
    return amountInCrore.toString().split('.')[0].length > 1 ? `${amountInCrore.toFixed(1)}Cr` : `${amountInCrore.toFixed(2)}Cr`;
  }

  return amountInLakhs.toString().split('.')?.[0]?.length > 1 ? `${amountInLakhs?.toFixed(1)}L` : `${amountInLakhs?.toFixed(2)}L`;
};