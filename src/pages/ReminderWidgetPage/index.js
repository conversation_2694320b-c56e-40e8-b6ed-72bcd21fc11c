import { useEffect, useRef, useState } from 'react';
import queryString from 'query-string';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { getStartupParamsAllCallback } from '../../utils/bridgeUtils';
import ReminderWidget from '../../components/organisms/ReminderWidget';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import { sendErrorToBackend } from '../../actions/runtime';
import { useNotifyHeight } from '../../hooks/useNotifyHeight';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';
import ReminderLoader from '../../components/molecules/ReminderLoader/ReminderLoader';
import { log } from '../../utils/commonUtil';
import styles from './index.scss';

const ReminderWidgetPageContent = (props) => {
  const { data, pages, businessType, aggrKey } = props;
  const [aggrData, setAggrData] = useState(pages && pages.data ? pages : null);
  const reminderWidgetCardRef = useRef(null);

  useNotifyHeight(reminderWidgetCardRef, data?.widgetId || aggrData?.widgetId, {
    flowType:
      BUSINESS_TYPE_MAPPINGS[businessType || pages?.data?.businessType]
        ?.height || 'combinedHomeH5FragmentHeight',
  });

  useEffect(() => {
    if (pages && pages.data) {
      setAggrData(pages);
    }
  }, [pages]);

  if (!data && !aggrData) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'reminder-widget-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  return data || aggrData ? (
    <div className={styles.reminderWidgetPage} ref={reminderWidgetCardRef}>
      <ReminderWidget
        data={data || aggrData}
        aggrKey={aggrKey}
        businessTypeFallback={businessType || pages?.data?.businessType}
      />
    </div>
  ) : null;
};

const ReminderWidgetPage = () => {
  const [nativeData, setNativeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [widgetId, setwidgetId] = useState(null);
  const query = queryString.parse(window.location?.search);
  const { businessType, aggrKey } = query || {};

  useEffect(() => {
    getStartupParamsAllCallback((result) => {
      log('check nativeData', result);
      if (result?.nativeData) {
        const parsedData = JSON.parse(result.nativeData);
        localStorage.setItem('reminderWidgetData', JSON.stringify(parsedData));
        setNativeData(parsedData);
        setwidgetId(result?.widgetId);
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }, []);

  if (isLoading) {
    return <ReminderLoader />;
  }

  if (nativeData) {
    return (
      <ReminderWidgetPageContent
        data={
          nativeData?.data
            ? nativeData
            : {
                widgetId: widgetId || nativeData?.widgetType,
                data: {
                  ...nativeData,
                },
              }
        }
        businessType={businessType}
        aggrKey={aggrKey}
      />
    );
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'ReminderWidget',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <ReminderLoader />,
  })(ReminderWidgetPageContent);

  return <WrappedComponent aggrKey={aggrKey} />;
};

export default withErrorBoundary(ReminderWidgetPage, false);
