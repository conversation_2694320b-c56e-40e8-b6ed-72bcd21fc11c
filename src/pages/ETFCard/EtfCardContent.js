import { useEffect, useRef, useState } from 'react';
import cx from 'classnames';

import Shimmer from '../../components/atoms/Shimmer';
import Button from '../../components/atoms/Button/Button';

import { isPaytmMoney } from '../../utils/coreUtil';
import { notifyNativeApp } from '../../utils/bridgeUtils';
import { isDarkMode } from '../../utils/commonUtil';

import styles from './StartYourDailySip.scss';
import ETFDetails from '../../components/organisms/ETFDetails/ETFDetails';
import Icon, { ICONS_NAME } from '../../components/molecules/Icon';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';

const EtfCardContent = ({
  etfData: {
    popularEtfs: data = [],
    transitionTime = 10,
    returns = [],
    widgetId,
    cta1,
    ...widgetData
  },
  userSelectedEtf,
  showETFPopupHandler,
  handleInvestClick,
  companyPageNavigation,
  onDismissWidget,
  isCrossSellingWidget = false,
}) => {
  const cancelInterval = useRef(null);
  const sipDataIndex = useRef(0);
  const [etfCardData, setEtfCardData] = useState(
    userSelectedEtf || data?.[sipDataIndex.current] || {},
  );

  const [showAnimation, setShowAnimation] = useState(
    !(transitionTime === 0 || data.length < 2) && !userSelectedEtf,
  );

  const [hideWidget, setHideWidget] = useState(
    sessionStorage.getItem('hideGoldWidget') || false,
  );

  useEffect(() => {
    clearInterval(cancelInterval.current);
    cancelInterval.current = null;
    if (userSelectedEtf) {
      setShowAnimation(false);
      setEtfCardData(userSelectedEtf);
      clearInterval(cancelInterval.current);
    }
    if (!showAnimation) return;

    cancelInterval.current = setInterval(() => {
      sipDataIndex.current = (sipDataIndex.current + 1) % data.length;
      setEtfCardData(userSelectedEtf || data?.[sipDataIndex.current] || {});
    }, transitionTime * 1000);

    return () => {
      clearInterval(cancelInterval.current);
      cancelInterval.current = null;
    };
  }, [userSelectedEtf, showAnimation, transitionTime, data]);

  const handleETFPopup = () => {
    setShowAnimation(false);
    showETFPopupHandler(userSelectedEtf || data?.[sipDataIndex.current] || {});
  };

  if (hideWidget) return null;

  return (
    <div
      className={cx(styles.root, {
        [styles.darkModeRoot]: isDarkMode(),
      })}
    >
      <div className={styles.header}>
        <div className={styles.dailySipContainer}>
          <div className={styles.equitySipTitle}>{widgetData?.title}</div>
          <div className={styles.equitySipSubtitle}>{widgetData?.subTitle}</div>
        </div>
        {/* <Icon
          name={ICONS_NAME.CLOSE_ICON}
          className={styles.crossIcon}
          size={4}
          onClick={() => {
            onDismissWidget();
            setHideWidget(true);
            sessionStorage.setItem('hideGoldWidget', true);
            if (isPaytmMoney()) {
              notifyNativeApp({
                flowType:
                  BUSINESS_TYPE_MAPPINGS[widgetData.businessType]
                    ?.removeFragment || 'removeH5FragmentCombinedHome',
                widgetId,
              });
            }
          }}
        /> */}
      </div>
      {etfCardData?.name ? (
        <ETFDetails
          etf={etfCardData}
          showPopupHandler={handleETFPopup}
          showAnimation={showAnimation}
          transitionTime={transitionTime}
          returns={returns}
          widgetData={widgetData}
          companyPageNavigation={companyPageNavigation}
        />
      ) : (
        <Shimmer height="50px" width="100%" />
      )}
      <div className={styles.footer}>
        <Button
          isPrimary
          className={styles.startSipBtn}
          buttonText={cta1?.cta}
          buttonTextClassName={styles.startSipBtnText}
          onClickHandler={() => handleInvestClick(etfCardData, cta1)}
        />

        <div className={styles.footerTextContainer}>
          <div className={styles.sipDisclaimerNote}>
            {widgetData?.disclaimer}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EtfCardContent;
