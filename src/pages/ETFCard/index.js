import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import cx from 'classnames';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import { useDrawer } from '../../components/molecules/Drawer/Drawer';
import { isPaytmMoney } from '../../utils/coreUtil';
import { etfSelectionDeeplink } from '../../utils/constants';
import { openDeepLinkPaytmMoney } from '../../utils/bridgeUtils';
import {
  setDailySipCohort,
  getDailySipCohort,
  setETFWidgetType,
  getETFWidgetType,
  generateQueryParamsString,
} from '../../utils/commonUtil';
import { ORDER_PAD, PRESELECTED_ETF, PULSE_STATICS } from './enums';

import styles from './StartYourDailySip.scss';
import ETFSelectionPopup from '../../components/organisms/ETFSelectionPopup/ETFSelectionPopup';
import { useETFAnalyticsEvent } from '../../hooks/useETFAnalyticsEvent';
import { TRANSACTION_TYPES } from '../../utils/Equities/enum';
import EtfCardContent from './EtfCardContent';

let timerId;

const ETFCardWrapper = ({
  data,
  companyPageNavigation: companyNavigationWidget,
  navigateTo = () => {},
  history = {},
  aggrKey = '',
  isCrossSellingWidget = false,
}) => {
  console.log('isCrossSellingWidget', isCrossSellingWidget);

  // Transform the widget data into the expected format
  const formatWidgetData = useCallback(() => {
    const { data: { meta, widget } = {}, widgetId = '' } = data;
    const attributes = widget?.attributes || [];
    // Create attributes map in single iteration
    const attributesMap = attributes.reduce((acc, attr) => {
      acc[attr.name] = attr.value;
      return acc;
    }, {});

    return {
      popularEtfs: attributesMap.POPULAR_GOLD_ETF || [],
      transitionTime: attributesMap.STOCK_TRANSITION_TIME || 10,
      title: attributesMap.TITLE_GOLD_ETF,
      subTitle: attributesMap.SUB_TITLE_GOLD_ETF,
      disclaimer: attributesMap.DISCLAIMER_GOLD_ETF,
      cta1: attributesMap.CTA_1_GOLD_ETF,
      cta2: attributesMap.CTA_2_GOLD_ETF,
      returns: attributesMap.RETURNS_GOLD_ETF,
      viewAll: attributesMap.VIEW_ALL_GOLD_ETF,
      subCohortId: meta?.subCohortId || '',
      widgetId,
      businessType: meta?.businessType || 'HOMESCREEN',
    };
  }, [data]);

  const etfData = useMemo(() => formatWidgetData(), [formatWidgetData]);

  const [userSelectedEtf, setUserSelectedEtf] = useState(null);
  const [preSelectedETF, setPreSelectedETF] = useState(null);
  const sipCardRef = useRef(null);

  const { sendAnalyticsEventETF } = useETFAnalyticsEvent({
    widgetId: etfData?.widgetId || '',
    subCohortId: etfData?.subCohortId || '',
  });

  const {
    isOpen: showETFSelectionPopup,
    onOpen: onETFSelectionPopupOpen,
    onClose: onETFSelectionPopupClose,
  } = useDrawer();

  const handleStorageChange = (event) => {
    if (event.key === PRESELECTED_ETF) {
      setUserSelectedEtf(JSON.parse(event.newValue));
    }
  };

  useEffect(() => {
    if (etfData?.popularEtfs?.length) {
      const { subCohortId, widgetId } = etfData;
      setDailySipCohort(subCohortId);
      setETFWidgetType(widgetId);
      if (isPaytmMoney()) {
        window.addEventListener('storage', handleStorageChange);
      }
    }
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearTimeout(timerId);
    };
  }, [etfData]);

  useEffect(() => {
    sendAnalyticsEventETF({
      action: PULSE_STATICS.ACTION.PAGE_LOAD,
      event: PULSE_STATICS.OPEN_SCREEN_EVENT,
    });
  }, []);

  const showETFPopupHandler = (etf) => {
    setPreSelectedETF(etf);
    if (isPaytmMoney() && !isCrossSellingWidget) {
      localStorage.setItem(PRESELECTED_ETF, JSON.stringify(etf));

      const deeplink = `${etfSelectionDeeplink}${getDailySipCohort() ? `&cohort=${getDailySipCohort()}` : ''}${getETFWidgetType() ? `&widgetType=${getETFWidgetType()}` : ''}&businessType=${etfData.businessType}&aggrKey=${aggrKey}&widgetId=${etfData?.widgetId}`;
      openDeepLinkPaytmMoney(deeplink);
    } else {
      onETFSelectionPopupOpen();
    }

    sendAnalyticsEventETF({
      action: PULSE_STATICS.ACTION.ON_VIEW_ALL_CLICK,
    });
  };

  function navigateToBuyScreen(isMainOrderpad, stock) {
    const queryString = generateQueryParamsString({
      transactionType: TRANSACTION_TYPES.BUY,
      id: stock.id,
      securityId: stock.security_id,
      exchange: stock.exchange,
      name: stock.name,
      segment: stock.segment,
      quantity: 1,
      instrumentType: stock.instrument_type,
      isin: stock.isin,
    });

    const route = isMainOrderpad ? ORDER_PAD : '/';

    navigateTo(history, `${route}${queryString}`, {}, 'push');
  }

  const handleCtaClick = (etf, ctaDetails = {}) => {
    sendAnalyticsEventETF({
      action: PULSE_STATICS.ACTION.INVEST_CTA_CLICK,
      label: ctaDetails?.type,
    });
    if (isPaytmMoney()) {
      const instrumentType =
        etf.instrument_type === 'ES'
          ? 'company'
          : etf.instrument_type.toLowerCase();
      const { id, product = 'C' } = etf;
      const url = `https://paytmmoney.com/stocks/company/${id}?action=place-order&txn_type=B&price=0&product=${product}&order_type=MKT`;
      openDeepLinkPaytmMoney(url);
    } else {
      navigateToBuyScreen(true, etf);
    }
  };

  const onDismissWidget = () => {
    sendAnalyticsEventETF({
      action: PULSE_STATICS.ACTION.DISMISS_WIDGET,
    });
  };

  const companyPageNavigation = (stock) => {
    if (companyNavigationWidget) {
      companyNavigationWidget(stock.id);
    } else {
      const instrumentType =
        stock.instrument_type === 'ES'
          ? 'company'
          : stock.instrument_type.toLowerCase();
      const { id } = stock;
      const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}`;
      openDeepLinkPaytmMoney(url);
    }
  };

  return (
    <div
      ref={sipCardRef}
      className={cx(styles.sipCardContainer, {
        [styles.crossSellingWidget]: isCrossSellingWidget,
      })}
    >
      <EtfCardContent
        etfData={etfData}
        userSelectedEtf={userSelectedEtf}
        showETFPopupHandler={showETFPopupHandler}
        handleInvestClick={handleCtaClick}
        companyPageNavigation={companyPageNavigation}
        onDismissWidget={onDismissWidget}
        isCrossSellingWidget={isCrossSellingWidget}
      />
      <ETFSelectionPopup
        isOpen={showETFSelectionPopup}
        setEtf={setUserSelectedEtf}
        preSelectedETF={preSelectedETF}
        onClose={onETFSelectionPopupClose}
        etfData={etfData}
        isCrossSellingWidget={isCrossSellingWidget}
      />
    </div>
  );
};

export default React.memo(withErrorBoundary(ETFCardWrapper));
