/* eslint-disable no-underscore-dangle */
import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useMutation } from '@tanstack/react-query';

import { useBackPress } from '@src/hooks/useNativeBackPress';

import { removeFromWatchList, useGetAllWatchList } from '@query/watchListQuery';

import { getGenericAppHeaders, makeApiPostCall } from '@utils/apiUtil';
import { queryClient } from '@src/provider/ReactQueryProvider';
import { FAV_API_URLS } from '@config/urlConfig';
import WatchListOptionDrawer from '../../components/organisms/WatchListOptionDrawer/WatchListOptionDrawer';
import { useToastSnackbarClient } from '../../contexts/ToastSnackbarProvider';
import { fav_message } from '../../components/organisms/FavIcon/enum';
import { APPEARANCE_TYPES } from '../../enums/ToastSnackbarEnums';
import { ICONS_NAME } from '../../components/molecules/Icon';
import { FAV_DATA } from '../../config/favConfig';
import { exitApp } from '../../utils/bridgeUtils';
import { getDeeplinkDataOrQueryParam } from '../../utils/commonUtil';
import './index.scss';

const SelectWatchlist = () => {
  const name = getDeeplinkDataOrQueryParam('stockName');
  const id = getDeeplinkDataOrQueryParam('stockId');
  const isSuccess = useRef();

  const { addToast } = useToastSnackbarClient();

  const customizeWatchListName = (watchListName) => {
    const firstPart = watchListName.slice(0, 35);
    const remainingPart = watchListName.slice(36);
    if (remainingPart.length) return `${firstPart}...`;
    return watchListName;
  };

  const companyDetails = useMemo(
    () => ({
      name,
      id,
    }),
    [name, id],
  );

  const [favStatus, setFavStatus] = useState({
    ids: [],
    status: false,
  });
  const [openWatchListDrawer, setOpenWatchListDrawer] = useState(true);
  const { popStack, pushStack, clearStack } = useBackPress();
  const { data: allWatchListData } = useGetAllWatchList();

  useEffect(() => {
    pushStack(exitApp);
    return () => {
      clearStack();
    };
  }, [clearStack, pushStack]);

  const closeScreen = () => {
    setTimeout(() => {
      if (isSuccess.current) {
        localStorage.setItem('watchlist_updated', 'true');
      }
      popStack();
    }, 2000);
  };

  const handleOnSuccessCallback = (companyName, watchlistName) => {
    addToast({
      message: FAV_DATA.REMOVED_FROM_FAV(companyName, watchlistName),
      type: APPEARANCE_TYPES.SUCCESS,
      customIcon: ICONS_NAME.WHITE_TICKET_WITHOUT_BORDER,
    });
    isSuccess.current = true;
  };

  const { mutate: removeFromWatchListMutate } = removeFromWatchList(
    handleOnSuccessCallback,
    closeScreen,
  );

  const addToWatchList = useMutation(
    (data) =>
      makeApiPostCall({
        url: FAV_API_URLS.ADD_SECURITY_TO_WATCHLIST(data.watchlist_id),
        body: {
          security_id: data.companyDetails.id,
        },
        headers: getGenericAppHeaders(),
      }),
    {
      onMutate: async (data) => {
        setFavStatus(true);
        const queryKey = ['allWatchlist'];
        await queryClient.cancelQueries(queryKey);
        const previousValue = queryClient.getQueryData(queryKey);
        queryClient.setQueryData(queryKey, (old) => ({
          ...old,
          watchlists: old.watchlists.map((watchlist) => {
            if (watchlist.id === data.watchlist_id) {
              return {
                ...watchlist,
                security_count: watchlist.security_count + 1,
                securities: watchlist.security_count
                  ? [
                      {
                        ...data.companyDetails,
                        created_at: new Date().getTime(),
                      },
                      ...watchlist.securities,
                    ]
                  : [
                      {
                        ...data.companyDetails,
                        created_at: new Date().getTime(),
                      },
                    ],
              };
            }
            return watchlist;
          }),
        }));

        addToast({
          message: fav_message.ADDED_TO_FAV(
            companyDetails.name,
            customizeWatchListName(data.watchlistName),
          ),
          type: APPEARANCE_TYPES.SUCCESS,
          customIcon: ICONS_NAME.WHITE_TICKET_WITHOUT_BORDER,
        });
        isSuccess.current = true;
        return previousValue;
      },

      onError: (err, variables, previousValue) => {
        setFavStatus(false);
        queryClient.setQueryData(['allWatchlist'], previousValue);
      },

      onSettled: () => {
        queryClient.invalidateQueries(['allWatchlist']);
        closeScreen();
      },
    },
  );

  useEffect(() => {
    if (companyDetails?.id && allWatchListData?.watchlist_count) {
      const { watchlists = [] } = allWatchListData;
      let checkFav = {
        ids: [],
        status: false,
      };
      watchlists.forEach((watchlist) => {
        if (watchlist?.security_count) {
          const isSecurityExist = watchlist?.securities?.find(
            (security) => security.id === companyDetails.id.toString(),
          );
          if (isSecurityExist) {
            checkFav = {
              ...checkFav,
              status: true,
              ids: [...checkFav.ids, watchlist.id],
            };
          }
        }
      });
      setFavStatus(checkFav);
    }
  }, [companyDetails, allWatchListData]);

  const handleFavClick = async (watchlist) => {
    if (watchlist?.security_count >= 50) {
      addToast({
        message: fav_message.STOCKS_FULL_MSG(watchlist.name),
        hideIcon: true,
      });
    } else {
      addToWatchList.mutate({
        watchlist_id: watchlist.id,
        watchlistName: watchlist.name,
        index: watchlist.index,
        companyDetails,
      });
    }
    setOpenWatchListDrawer(false);
  };

  const handleRemoveFavClick = (watchlist) => {
    removeFromWatchListMutate({
      watchlist_id: watchlist.id,
      watchlistName: watchlist.name,
      companyDetails,
    });
    setOpenWatchListDrawer(false);
  };

  return (
    <WatchListOptionDrawer
      isOpen={openWatchListDrawer}
      companyDetails={companyDetails}
      favStatus={favStatus}
      handleFavClick={handleFavClick}
      handleRemoveFavClick={handleRemoveFavClick}
      onClose={() => {
        setOpenWatchListDrawer(false);
        popStack();
      }}
      isWidget={false}
    />
  );
};

export default SelectWatchlist;
