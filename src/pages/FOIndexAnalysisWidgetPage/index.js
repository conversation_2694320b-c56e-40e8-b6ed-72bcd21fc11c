import { useEffect, useRef, useState } from 'react';
import queryString from 'query-string';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { getStartupParamsAllCallback } from '../../utils/bridgeUtils';
import FOIndexAnalysisWidget from '../../components/organisms/FOIndexAnalysisWidget';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import { sendErrorToBackend } from '../../actions/runtime';
import { useNotifyHeight } from '../../hooks/useNotifyHeight';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';
import { log } from '../../utils/commonUtil';
import FOIndexAnalysisLoader from '../../components/molecules/FOIndexAnalysisLoader';
import styles from './index.scss';

const FOIndexAnalysisWidgetPageContent = (props) => {
  const { data, pages, businessType, aggrKey } = props;
  const [aggrData, setAggrData] = useState(pages && pages.data ? pages : null);
  const foIndexAnalysisWidgetRef = useRef(null);

  useNotifyHeight(
    foIndexAnalysisWidgetRef,
    data?.widgetId || aggrData?.widgetId,
    {
      flowType:
        BUSINESS_TYPE_MAPPINGS[businessType || pages?.data?.businessType]
          ?.height || 'combinedHomeH5FragmentHeight',
    },
  );

  useEffect(() => {
    if (pages && pages.data) {
      setAggrData(pages);
    }
  }, [pages]);

  if (!data && !aggrData) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'fo-index-analysis-widget-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  return data || aggrData ? (
    <div
      className={styles.foIndexAnalysisWidgetPage}
      ref={foIndexAnalysisWidgetRef}
    >
      <FOIndexAnalysisWidget
        data={data || aggrData}
        aggrKey={aggrKey}
        businessTypeFallback={businessType || pages?.data?.businessType}
      />
    </div>
  ) : null;
};

const FOIndexAnalysisWidgetPage = () => {
  const [nativeData, setNativeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [widgetId, setWidgetId] = useState(null);
  const query = queryString.parse(window.location?.search);
  const { businessType, aggrKey } = query || {};

  useEffect(() => {
    getStartupParamsAllCallback((result) => {
      log('check nativeData', result);
      if (result?.nativeData) {
        const parsedData = JSON.parse(result.nativeData);
        setNativeData(parsedData);
        setWidgetId(result?.widgetId);
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }, []);

  if (isLoading) {
    return <FOIndexAnalysisLoader />;
  }

  if (nativeData) {
    return (
      <FOIndexAnalysisWidgetPageContent
        data={
          nativeData?.data
            ? nativeData
            : {
                widgetId: widgetId || nativeData?.widgetType,
                data: {
                  ...nativeData,
                },
              }
        }
        businessType={businessType}
        aggrKey={aggrKey}
      />
    );
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'FOIndexAnalysisWidget',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <FOIndexAnalysisLoader />,
  })(FOIndexAnalysisWidgetPageContent);

  return <WrappedComponent aggrKey={aggrKey} />;
};

export default withErrorBoundary(FOIndexAnalysisWidgetPage, false);
