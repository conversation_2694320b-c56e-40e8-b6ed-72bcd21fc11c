import PropTypes from 'prop-types';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import FirstStockCard from '../components/organisms/FirstStockCard';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import { withErrorBoundary } from '../HOC/WidgetErrorBoundary';

const FirstStockCardWrapper = (props) => {
  const { bridgeData, dataFeed, ...rest } = props;
  Object.entries(bridgeData).forEach(([key, val]) => {
    DeviceInfoProvider.setInfo(key, val);
  });
  // TODO: Remove this after testing
  console.log('dataFeed--->', dataFeed);
  const BackPress = useNativeBackPress();
  return (
    <QueryClientProvider client={ownQueryClient}>
      <NativeDocumentHideContext>
        <AppContextProvider isFeedRequired={false} externalDataFeed={dataFeed}>
          <NativeBackPressContext.Provider value={BackPress}>
            <FirstStockCard {...rest} />
          </NativeBackPressContext.Provider>
        </AppContextProvider>
      </NativeDocumentHideContext>
    </QueryClientProvider>
  );
};

FirstStockCardWrapper.propTypes = {
  bridgeData: PropTypes.shape({}),
  dataFeed: PropTypes.any,
};

FirstStockCardWrapper.defaultProps = {
  bridgeData: {},
  dataFeed: null,
};

export default withErrorBoundary(FirstStockCardWrapper);
