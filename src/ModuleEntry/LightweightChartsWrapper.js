import PropTypes from 'prop-types';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import LightChartContainer from '../components/organisms/LightChartContainer';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';
import { withErrorBoundary } from '../HOC/WidgetErrorBoundary';

const LightweightChartsWrapper = (props) => {
  const { bridgeData, dataFeed, ...rest } = props;
  Object.entries(bridgeData).forEach(([key, val]) => {
    DeviceInfoProvider.setInfo(key, val);
  });
  return (
    <QueryClientProvider client={ownQueryClient}>
      <NativeDocumentHideContext>
        <AppContextProvider isFeedRequired={false} externalDataFeed={dataFeed}>
          <LightChartContainer {...rest} />
        </AppContextProvider>
      </NativeDocumentHideContext>
    </QueryClientProvider>
  );
};

LightweightChartsWrapper.propTypes = {
  bridgeData: PropTypes.shape({}),
  dataFeed: PropTypes.any,
};

LightweightChartsWrapper.defaultProps = {
  bridgeData: {},
  dataFeed: null,
};

export default withErrorBoundary(LightweightChartsWrapper);
