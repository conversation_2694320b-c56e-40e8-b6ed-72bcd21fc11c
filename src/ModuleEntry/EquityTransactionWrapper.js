import PropTypes from 'prop-types';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import { withErrorBoundary } from '../HOC/WidgetErrorBoundary';
import EquityTransaction from '../pages/EQTransactionPage';

const EquityTransactionWrapper = (props) => {
  const { bridgeData, dataFeed, ...rest } = props;
  Object.entries(bridgeData).forEach(([key, val]) => {
    DeviceInfoProvider.setInfo(key, val);
  });
  const BackPress = useNativeBackPress();
  return (
    <QueryClientProvider client={ownQueryClient}>
      <NativeDocumentHideContext>
        <AppContextProvider isFeedRequired={false} externalDataFeed={dataFeed}>
          <NativeBackPressContext.Provider value={BackPress}>
            <EquityTransaction isMiniApp {...rest} />
          </NativeBackPressContext.Provider>
        </AppContextProvider>
      </NativeDocumentHideContext>
    </QueryClientProvider>
  );
};

EquityTransactionWrapper.propTypes = {
  bridgeData: PropTypes.shape({}),
  dataFeed: PropTypes.any,
};

EquityTransactionWrapper.defaultProps = {
  bridgeData: {},
  dataFeed: null,
};

export default withErrorBoundary(EquityTransactionWrapper);
