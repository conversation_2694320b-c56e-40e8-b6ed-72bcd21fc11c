import React from 'react';

import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import ETFCard from '../pages/ETFCard';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import AxiosProviderWrapper from '../contexts/AxiosProviderWrapper';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';

const GoldETFCardWrapper = (props) => {
  const BackPress = useNativeBackPress();

  const { bridgeData, dataFeed, ...rest } = props;
  Object.entries(bridgeData).forEach(([key, val]) => {
    DeviceInfoProvider.setInfo(key, val);
  });

  return (
    <QueryClientProvider client={ownQueryClient}>
      <AxiosProviderWrapper>
        <NativeDocumentHideContext>
          <AppContextProvider
            isFeedRequired={false}
            externalDataFeed={dataFeed}
          >
            <NativeBackPressContext.Provider value={BackPress}>
              <ETFCard {...rest} />
            </NativeBackPressContext.Provider>
          </AppContextProvider>
        </NativeDocumentHideContext>
      </AxiosProviderWrapper>
    </QueryClientProvider>
  );
};

export default GoldETFCardWrapper;
