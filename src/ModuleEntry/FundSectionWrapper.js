import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import FundSectionPage from '../pages/FundSectionPage';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import AxiosProviderWrapper from '../contexts/AxiosProviderWrapper';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import SnackbarProvider from '../contexts/SnackbarProvider';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';

const FundSectionWrapper = (props) => {
  const BackPress = useNativeBackPress();

  const { bridgeData, dataFeed, data, ...rest } = props;
  if (bridgeData) {
    Object.entries(bridgeData).forEach(([key, val]) => {
      DeviceInfoProvider.setInfo(key, val);
    });
  }

  return (
    <QueryClientProvider client={ownQueryClient}>
      <NativeDocumentHideContext>
        <AxiosProviderWrapper>
          <SnackbarProvider>
            <AppContextProvider isFeedRequired={false} externalDataFeed={null}>
              <NativeBackPressContext.Provider value={BackPress}>
                <FundSectionPage data={data} {...rest} />
              </NativeBackPressContext.Provider>
            </AppContextProvider>
          </SnackbarProvider>
        </AxiosProviderWrapper>
      </NativeDocumentHideContext>
    </QueryClientProvider>
  );
};

export default FundSectionWrapper;
