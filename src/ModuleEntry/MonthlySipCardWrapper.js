import React from 'react';

import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import MonthlySipCard from '../pages/MonthlySipCard';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import AxiosProviderWrapper from '../contexts/AxiosProviderWrapper';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import SnackbarProvider from '../contexts/SnackbarProvider';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';

const SipCardWrapper = (props) => {
  const BackPress = useNativeBackPress();

  const { bridgeData, dataFeed, ...rest } = props;
  Object.entries(bridgeData).forEach(([key, val]) => {
    DeviceInfoProvider.setInfo(key, val);
  });

  return (
    <QueryClientProvider client={ownQueryClient}>
      <NativeDocumentHideContext>
        <AxiosProviderWrapper>
          <SnackbarProvider>
            <AppContextProvider
              isFeedRequired={false}
              externalDataFeed={dataFeed}
            >
              <NativeBackPressContext.Provider value={BackPress}>
                <MonthlySipCard {...rest} />
              </NativeBackPressContext.Provider>
            </AppContextProvider>
          </SnackbarProvider>
        </AxiosProviderWrapper>
      </NativeDocumentHideContext>
    </QueryClientProvider>
  );
};

export default SipCardWrapper;
