import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import ReminderWidget from '../components/organisms/ReminderWidget';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import { withErrorBoundary } from '../HOC/WidgetErrorBoundary';
import AxiosProviderWrapper from '../contexts/AxiosProviderWrapper';
import { log } from '../utils/commonUtil';

const ReminderWidgetWrapper = (props) => {
  const { bridgeData, dataFeed, ...rest } = props;
  Object.entries(bridgeData).forEach(([key, val]) => {
    DeviceInfoProvider.setInfo(key, val);
  });

  log('dataFeed--->', dataFeed);
  const BackPress = useNativeBackPress();
  return (
    <QueryClientProvider client={ownQueryClient}>
      <AxiosProviderWrapper>
        <NativeDocumentHideContext>
          <AppContextProvider
            isFeedRequired={false}
            externalDataFeed={dataFeed}
          >
            <NativeBackPressContext.Provider value={BackPress}>
              <ReminderWidget {...rest} />
            </NativeBackPressContext.Provider>
          </AppContextProvider>
        </NativeDocumentHideContext>
      </AxiosProviderWrapper>
    </QueryClientProvider>
  );
};

export default withErrorBoundary(ReminderWidgetWrapper);
