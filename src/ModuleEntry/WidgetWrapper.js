import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import WidgetWrapper from '../pages/EQTransactionPage/WidgetComponents/viewConfig';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import AxiosProviderWrapper from '../contexts/AxiosProviderWrapper';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import SnackbarProvider from '../contexts/SnackbarProvider';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';
import { isPaytmMoney } from '../utils/coreUtil';

const WidgetWrapperModule = (props) => {
  const BackPress = useNativeBackPress();

  const { bridgeData, data } = props;
  console.log('bridgeData', bridgeData);
  Object.entries(bridgeData).forEach(([key, val]) => {
    DeviceInfoProvider.setInfo(key, val);
  });
  const widgetInfo = data?.cards;
  console.log('isPaytmMoney()', isPaytmMoney());
  return (
    <QueryClientProvider client={ownQueryClient}>
      <NativeDocumentHideContext>
        <AxiosProviderWrapper>
          <SnackbarProvider>
            <AppContextProvider>
              <NativeBackPressContext.Provider value={BackPress}>
                <WidgetWrapper data={widgetInfo?.['container-1']} />
              </NativeBackPressContext.Provider>
            </AppContextProvider>
          </SnackbarProvider>
        </AxiosProviderWrapper>
      </NativeDocumentHideContext>
    </QueryClientProvider>
  );
};

export default WidgetWrapperModule;
