html {
  -webkit-text-size-adjust: 100%;
  box-sizing: border-box;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizelegibility;
  overscroll-behavior: none;
  height: 100%;
  scroll-behavior: auto !important;

  &::-webkit-scrollbar {
    display: none !important;
  }
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
}

body {
  font-family: 'Inter', sans-serif;
  margin: 0 auto;
  font-weight: normal;
  width: 100%;
  overscroll-behavior: none;
  height: 100%;
}

a {
  text-decoration: none;
  cursor: pointer;
}

iframe {
  border: 0;
}

article,
aside,
figure,
footer,
header,
hgroup,
section {
  display: block;
}

code,
pre {
  -moz-osx-font-smoothing: auto;
  -webkit-font-smoothing: auto;
  line-height: 1.5px;
}

svg:not(:root) {
  overflow: hidden;
}

button:focus,
div[role='button']:focus,
input:focus,
select:focus,
textarea:focus {
  outline: none;
}

ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

*:not(input) {
  user-select: none;
}

*:not(input)::selection {
  background: none;
}

button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
}
