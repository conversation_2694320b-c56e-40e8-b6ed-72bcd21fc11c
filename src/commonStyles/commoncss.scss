@import './variables.scss';

.shimmerAnimation {
  animation : shimmer 2s infinite linear;
  background: map-get($colors, Shimmer);
  background-size: 1000px 100%;
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

/* flex Css Start */
.dFlex{
  display:flex;
}
.flexWrap{
  flex-wrap:wrap!important;
}
.flexColumn,.flexLgColumn,.flexMdColumn,.flexSmColumn{
  flex-direction:column;
}
.alignItemsCenter{
  align-items:center!important;
}
.justifyContentCenter{
  justify-content:center;
}
.justifyContentBetween{
  justify-content:space-between !important;
}
.w-50{
  width: 50%;
}
.w-100{
  width: 100%;
}
/* flex Css End */
