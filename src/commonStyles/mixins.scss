@mixin applyStyles($styles) {
  @each $key, $value in $styles {
    @if $value !=null {
      #{$key}: $value;
    }
  }
}

@mixin noScrollBar {
  &::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background-color: transparent;
  }
}


@mixin preventSelect {
  -webkit-user-select: none; 
  -ms-user-select: none;
  user-select: none;
}

@mixin fontStyle(
  $family: null,
  $weight: null,
  $size: null,
  $lineheight: null,
  $letterSpacing: null
) {
  $styles: (
    font-family: $family,
    font-weight: $weight,
    font-size: $size,
    line-height: $lineheight,
    letter-spacing: $letterSpacing,
  );
  @include applyStyles($styles);
}

@mixin flex($justifyContent: null, $alignitems: null, $wrap: null, $gap: null) {
  display: flex;
  $styles: (
    justify-content: $justifyContent,
    align-items: $alignitems,
    flex-wrap: $wrap,
    gap: $gap,
  );
  @include applyStyles($styles);
}

@mixin preventSelect {
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none; /* Standard syntax */
}
