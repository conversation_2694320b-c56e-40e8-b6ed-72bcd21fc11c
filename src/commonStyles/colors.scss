/* Define all colours */
// scss-lint:disable ColorVariable
// save without formatting cmd+K S, win ctrl+K S
$colors: (
  primaryBgColor: var(--primary-background-color, #fafafa), // Eg: for component overall background
  secondaryBgColor: var(--secondary-background-color, #fff), // Eg: for cards
  primaryTextColor: var(--primary-text-color, #494949), //Eg: for Top Navbar Title, amounts
  secondaryTextColor: var(--secondary-text-color, #232939), // Eg: for card titles
  tertiaryTextColor: var(--tertiary-text-color, #000),
  buttonWrapperColor: var(--button-wrapper-color, #fff),
  primaryGrey: var(--primary-grey, #adafb6), // Eg: for grey colored headings
  primaryGrey2: var(--primary-grey-2, #f6f7fa),
  secondaryGrey: var(--secondary-grey, #eaeaea), //Eg: for light grey dividers
  DGrey: var(--text-color-1, #727682),
  DGrey2: var(--text-color-2, #737682),
  LGrey: var(--text-color-3, #eeeef0),
  DGrey3: var(--text-color-4, #494949), //Eg: for Top Navbar Title, amounts
  VVLGrey: var(--text-color-5, rgba(226, 231, 237, 0.24)),
  VLGrey: var(--text-color-6, #737373),
  DBlue: var(--text-color-8 ,#004393), // eg: for dark blue headings
  Grey10: var(--text-color-9, #494949),
  LYellow2: var(--text-color-10, rgba(255, 250, 202, 0.51)),
  Blue4: var(--text-color-11, #f5f7f9),
  Blue5: var(--text-color-12, rgba(226, 231, 237, 0.24)),
  ETFBlue: #00b8f5,
  ETFBlue2: darken(#fafcff, 1%),
  ETFBlue3: #29007a,
  spWhite: var(--text-color-13, #232939), // Eg: for timer
  White1: var(--text-color-19, #494949),
  headerText: var(--text-color-15, #494949), // Eg: for header
  DateText: var(--text-color-16, #727682),
  white2: #eaf2ff,
  White3: #f6f7fa,
  White4: #e7eaf0,
  PureBlack: #000,
  Black4: #00000029,
  PureWhite: #fff,
  FloralWhite: var(--color-1, #fefaf3),
  Blue3: rgba(0, 67, 147, 0.1),
  Blue8: #e9f2fe,
  DBlue1: #012a72,
  LBlue: #00c1f2, // eg: for light blue cta's
  Blue: rgba(0, 193, 242, 0.2), // e.g: light blue cta border
  VLBlue: var(--text-color-14, #f6f7fa),
  Blue2: #6a97d7,
  LGreen: rgba(89, 170, 0, 0.1),
  MGreen: #59aa00,
  Green: #22c079,
  Green2: #619c20,
  Green3: #009b41,
  DGreen: #56ac01, //eg: for Cta's
  DRed: #a00,
  Grey3: #f9f9fa,
  LOrange: rgba(238, 156, 22, 0.05), // for info divs
  MOrange: rgba(238, 156, 22, 0.1),
  Orange: #ee9c16,
  Orange1: #f38e00,
  Orange2:#f2994a,
  Orange3: #ff9d00,
  LRed: rgba(210, 61, 80, 0.1), // for status divs
  LightRed2: rgba(251, 82, 82, 0.2),
  Red: #d23d50,
  Red2: #d03044,
  Red5: #ff3f3f,
  VLRed: rgba(210, 61, 80, 0.05),
  LGray: var(--text-color-18, rgba(243, 243, 245, 0.5)), //for tile components
  VLGray: rgba(240, 240, 240, 0.5),
  ShadowColor1: rgba(0, 0, 0, 0.05),
  ShadowColor2: rgba(194, 194, 194, 0.12),
  ShadowColor3: rgba(0, 0, 0, 0.06),
  ShadowColor4: rgba(84, 87, 92, 0.1),
  ShadowColor5: rgba(0, 0, 0, 0.15),
  ShadowColor6: rgba(170, 166, 166, 0.1),
  ShadowColor7: rgba(38, 38, 38, 0.07),
  ShadowColor8: rgba(0, 0, 0, 0.04),
  ShadowColor9: rgba(170, 166, 166, 0.3),
  BackDropGradient: linear-gradient(to bottom, #adafb6, #adafb6),
  Gray: #fafafa,
  Gray2: #888,
  Grey4: #f7f7f7,
  Grey5: rgba(173, 175, 182, 0.5),
  Grey6: rgba(0, 0, 0, 0.07),
  LightGreen1: rgba(81, 188, 131, 0.2),
  Shimmer: var(--shimmer, linear-gradient(to right, rgba(239, 241, 243, 0.3) 4%, rgba(226, 226, 226, 0.3) 25%, rgba(239, 241, 243, 0.3) 36%)),
  Shimmer1: var(--shimmer,linear-gradient(to right, rgba(239, 241, 243, 0.4) 4%, rgba(226, 226, 226, 0.4) 25%, rgba(239, 241, 243, 0.4) 36%)),
  Grey7: rgba(153, 153, 153, 0.59),
  LYellow: var(--text-color-21, #fffce4),
  Grey8: var(--text-color-17 ,#494949),
  BgBlue: #003d9f,
  BgBlueShadow: rgba(0, 57, 165, 0.25),
  PureBlackAlpha: (0,0,0),
  dividerColor: var(--text-color-20, rgba(0, 0, 0, 0.05)),
  StrongNavy: #27306a,
  SplashGrey : #979797,
  DisabledGray: #6f6f6f,
  Divider: var(--divider-color),
  Divider2: #eeeded,
  Divider3: #f1f1f5,
  Black2: #1b1f2a,
  Black3: #242930,
  DBlack4: #54575c,
  DBlue2: #1d2f54,
  DBlue3: #00b9f5,
  Grey11: #8ba6c1,
  lightGreen2:#81e1b6,
  Blue6: #3e74dd,
  Grey12: #f3f4f8,
  Grey13: #ecedf4,
  Grey14: #506d85,
  Grey15: rgba(170, 166, 166, .1),
  Blue7: #f6faff,
  Blue11: #d5deea,
  Blue9: #2979ed,
  DropDownText: var(--primary-text-color, #141b2f),
  Green4: #51bc83,
  Grey16: #909090,
  LOrange2: #fef8f5,
  DateGrey: #6d7278,
  BorderGrey: rgba(109, 114, 120, 0.3),
  Grey17: #e8edf3,
  Grey18:#d3d3d3,
  Grey19: #9796a2,
  Grey20: #f6f9fe,
  Grey21: #e7e7e7,
  Grey23: #e8edf2,
  Red3: #fb5252,
  Red4: #eb5757,
  ErrorOtpBackground: #fb52521a, //check
  DBlueAlpha: (29, 47, 84),
  LPink: #fbe7e2,
  LPink2: #fce9e5,
  Solitude: #eff4ff,
  LearnHeader: rgba(254, 249, 233, 0.85),
  VeryLightBrown: #fdf4e9,
  VLGREEN: #eefaf4,
  DGREY4: rgba(139, 166, 193, 0.5),
  DGREY5: rgba(16, 16, 16, 0.54),
  Blue10: rgba(224,245,253,0.6),
  Orange4: rgba(242, 153, 74, 0.1),
  Orange5: rgba(255, 157, 0, 0.1),
  GrayishWellow: #fcf9dc,
  LightGrayishCyan: #e8f5f8,
  LYellow3: #fef9e9,
  LightShadeCyanBlue: #d4e4f9,
  Black5: #101010,
  ETFGrey: rgba(224, 246, 250, 0.3),
  ETFGray2: #c4c4c4,
  ETFGray5: rgba(16, 16, 16, 0.8),
  ETFLightGreen: #c2e6cd,
  ETFGreen2: #32f49d,
  ETFBorder: #e0f2fa,
  ETFRed: #fd5154,
  BlackHalfOpacity: rgba(16, 16, 16, 0.5),
  BlackOpacity6: rgba(255, 255, 255, 0.6),
  BlackOpacity7: rgba(16, 16, 16, 0.7),
  BlackOpacity1: rgba(16, 16, 16, 0.1),
  LightGrayishBg: #f5f9fe,
  ETFLightBlue: #e0f6fa,
  DGREYShadow: rgba(16, 16, 16, 0.05),
  LightBlueBorder: rgba(0, 184, 245, 0.1),
  LightYellowBg: #fff8e1,
  LightYellowBorder : rgba(255, 157, 0, 0.2),
  Grey24: #dedfe2,
  Grey25: #f7f9fb,
  Grey26: #636f84,
  Grey27: #d8d8d8,
  Black6: #354156,
  CardBackground: #f9f9fc,
  CardTagBackground: #33ba71,
  PositionAndFundsCardTextColor:#020f26,
  PositionAndFundsCardDateColor:#98a0af,
  LightGrey: rgba(16, 16, 16, 0.13),
  Green5: #21c179,
  DGREYText: #91939A,
  AthenGray: #e6e9ee,
  CardHeadTextcolor: #101010,
  VividYellow: rgb(254, 213, 51),

  // Paytm colors for bbc
  ChineseBlack: #101010,
  ChineseBlackSolid: rgba(16, 16, 16, 0.7),
  ChineseBlackLow1: rgba(16, 16, 16, 0.13),
  ChineseBlackLow2: rgba(16, 16, 16, 0.22),
  ChineseBlackLow3: rgba(16, 16, 16, 0.07),
  LoaderWhite: rgba(255, 255, 255, 0.2),
  GhostWhite: #f5f9fe,
  GreenCrayola: #21C179,
  FreshAir: #A6E6FC,
  Diamond: #B3EAFC,
  BlueBolt: #00B8F5,
  SpiroDiscoBall: #1ABFF6,
  PictonBlue: #4DCDF8,
  SkyBlue: #66D4F9,
  PaleCyan: #80DBFA,
  WinterWizard: #99E3FB,
  LightCyan: #E0F5FD,
  OrangeRedCrayola: #FD5154,
  BrightGray: #e7f1f8,
  BorderGray: #1010108a,
  Shappire: #012A72,
  DeepSkyBlue: #00B8F5,
  FooterBorderGray: #10101021,
  CloudBurst: #1D2F54,
  OrangePeel: #FF9E00,
  LightRedBg: #FFEBEF,
  TaraWhite: #e3f6ec,
  LightBlue: #F1F5FA,
  BorderSilver: var(--Border-silver400, #ffffff99),
  BorderPrimary: #013DA6,
  PrimaryOffset: #ECF2F8
);

:export {
  @each $key, $value in $colors {
    #{$key}: $value;
  }
}



