import React, {
  createContext,
  useState,
  useEffect,
  useContext,
  useCallback,
} from 'react';
import { Subject } from 'rxjs';
import { throttleTime } from 'rxjs/operators';

import { useDocumentHide } from './NativeDocumentHideContext';
import { AxiosErrorHandler } from '../utils/errorUtils';
import { useCallbackForEvents } from '../utils/react';

import { useAppStore } from './AppContextProvider';

import { makeApiGetCall, getGenericAppHeaders } from '../utils/apiUtil';
import { EQUITIES } from '../config/urlConfig';

import { getFeedByReqType } from '../utils/Equities';

import { fallbackConfig } from '../config/config';
import { RESPONSE_TYPES, REQUEST_TYPES } from '../services/equities/dataConfig';

export const MarketStatusContext = createContext({
  getMsData: () => null,
});

export const marketStatusErrorSubject = new Subject();
const requiredFeedResponse = [RESPONSE_TYPES.MARKET_STATUS];

function MarketStatus({ children }) {
  const { dataFeed } = useAppStore();
  const [marketStatus, setMarketStatus] = useState(fallbackConfig);
  const [msDataFetched, setMsDataFetched] = useState(false);

  useDocumentHide({
    onPause: () => {},
    onResume: () => setMsDataFetched(false),
  });

  const fetchMarketStatus = useCallbackForEvents(() => {
    let count = 0;
    async function getMarketStatusFromAPI() {
      try {
        const marketData = await makeApiGetCall({
          url: EQUITIES.GET_MARKET_STATUS,
          headers: getGenericAppHeaders(),
        });
        const config = {};
        marketData.data.forEach(exchangeConfig => {
          if (!config[exchangeConfig.segment])
            config[exchangeConfig.segment] = {};
          config[exchangeConfig.segment][exchangeConfig.exchange] = {
            ...exchangeConfig,
            amo: exchangeConfig.status === 2,
          };
        });
        setMarketStatus(config);
      } catch (err) {
        count += 1;
        if (count < 10) {
          getMarketStatusFromAPI();
        } else {
          AxiosErrorHandler(err, false, false, false, true);
        }
      }
    }
    getMarketStatusFromAPI();
  }, []);

  useEffect(() => {
    const subscription = getFeedByReqType(
      dataFeed?.getStream(
        REQUEST_TYPES.MARKET_STATUS,
        null,
        requiredFeedResponse,
      ) || [],
      RESPONSE_TYPES.MARKET_STATUS,
    )
      .pipe(throttleTime(1000))
      .subscribe(() => {
        fetchMarketStatus();
      });
    return () => subscription.unsubscribe();
  }, [dataFeed, fetchMarketStatus]);

  const getMsData = useCallback(() => {
    if (!msDataFetched) {
      setMsDataFetched(true);
    }
  }, [msDataFetched]);

  useEffect(() => {
    if (msDataFetched) fetchMarketStatus();
  }, [fetchMarketStatus, msDataFetched]);

  useEffect(() => {
    const subscription = marketStatusErrorSubject
      .pipe(throttleTime(1000))
      .subscribe(() => {
        fetchMarketStatus(
          'Market status has been updated. Please place order now',
        );
      });
    return () => subscription.unsubscribe();
  }, [fetchMarketStatus]);

  return (
    <MarketStatusContext.Provider
      value={{ marketStatus, getMsData, msDataFetched }}
    >
      {children}
    </MarketStatusContext.Provider>
  );
}

function useMarketStatus() {
  const ctx = useContext(MarketStatusContext);
  const { getMsData, msDataFetched } = ctx;

  useEffect(() => {
    if (!msDataFetched) {
      getMsData();
    }
  }, [getMsData, msDataFetched]);

  return ctx;
}

export { MarketStatus as default, useMarketStatus };
