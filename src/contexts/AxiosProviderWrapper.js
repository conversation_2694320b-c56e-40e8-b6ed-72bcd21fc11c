import { useMemo } from 'react';
import axios from 'axios';
import { AxiosProvider } from '@paytm-money/utils-frontend/providers';

import { getGenericAppHeaders } from '../utils/apiUtil';

const AxiosProviderWrapper = ({ children }) => {
  const axiosInstance = useMemo(() => {
    const client = axios.create({
      timeout: 15000,
      headers: {
        common: { ...getGenericAppHeaders() },
      },
    });
    return client;
  }, []);

  return (
    <AxiosProvider axiosInstance={axiosInstance}>{children}</AxiosProvider>
  );
};

export default AxiosProviderWrapper;
