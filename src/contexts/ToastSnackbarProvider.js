import { Snackbar } from '@paytm-h5-common/paytm_common_ui';
// import { isH5 } from '@paytm-money/utils-frontend/utils/bridgeUtils';
// import { isMobile } from '@paytm-money/utils-frontend/utils/commonUtils';
import { isEmpty, isEqual } from 'lodash';
import {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
  useCallback,
} from 'react';
// import ToastController from '../components/organisms/ToastController/ToastController';
import { APPEARANCE_TYPES, SNACKBAR_THEME } from '../enums/ToastSnackbarEnums';
import { useAppStore } from './AppContextProvider';
import styles from './ToastSnackbarProvider.scss';

const ToastSnackbarContext = createContext(null);

const ToastSnackbarProvider = ({ children }) => {
  const [snackBarList, setSnackBarList] = useState([]);
  const snackbarItem = useRef({});

  const { updateStore } = useAppStore();

  const getSnackbarTheme = (type) => {
    switch (type) {
      case APPEARANCE_TYPES.INFO:
        return SNACKBAR_THEME.NOTICE;

      case APPEARANCE_TYPES.FAIL:
        return SNACKBAR_THEME.NEGATIVE;

      default:
        return SNACKBAR_THEME.POSITIVE;
    }
  };

  const addToast = useCallback((options) => {
    setSnackBarList((prevSnackbarList) => {
      prevSnackbarList.push({
        key: `${options?.message}${new Date().getTime()}`,
        message: options.message,
        type: options.type,
        onClick: options.onClick,
        label: options.label,
        labelAction: options.labelAction,
        closeButton: options.closeButton,
        customStyle: options.customStyle,
        autoDelete: options.autoDelete || true,
        customIcon: options.customIcon,
        hideIcon: options.hideIcon || false,
        customStyles: options.customStyles,
        toastStyle: options.toastStyle,
        component: options.component,
        dismissTime: options.dismissTime || 5000,
        reserveSpaceForStatusBar: false,
      });
      return [...prevSnackbarList];
    });
  }, []);

  const removeToast = () => {
    setSnackBarList([{}]);
  };

  useEffect(() => {
    updateStore('addToast', addToast);
    updateStore('removeToast', removeToast);
  }, []);

  useEffect(() => {
    if (snackBarList.length) {
      if (isEqual(snackbarItem.current, snackBarList[0])) return;
      snackbarItem.current = { ...snackBarList[0] };
      setTimeout(() => {
        setSnackBarList((prevSnackBarList) => {
          prevSnackBarList.shift();
          return [...prevSnackBarList];
        });
      }, snackbarItem.current.dismissTime + 300);
    }
  }, [snackBarList, snackbarItem.current]);

  const value = useMemo(() => ({ addToast }), [addToast]);

  return (
    <ToastSnackbarContext.Provider value={value}>
      {children}
      {!isEmpty(snackBarList[0]) && (
        <Snackbar
          noClamp
          key={snackBarList[0]?.key}
          text={snackBarList[0]?.message}
          theme={getSnackbarTheme(snackBarList[0]?.type)}
          autoHide={snackBarList[0]?.autoDelete}
          autoHideAfter={snackBarList[0]?.dismissTime}
          reserveSpaceForStatusBar={snackBarList[0]?.reserveSpaceForStatusBar}
          {...snackBarList[0]?.snackBarProps}
          customClass={styles.snackbarCustomClass}
        />
      )}
    </ToastSnackbarContext.Provider>
  );
};

const useToastSnackbarClient = () => useContext(ToastSnackbarContext);
export { ToastSnackbarContext, ToastSnackbarProvider, useToastSnackbarClient };
