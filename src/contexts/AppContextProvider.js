import {
  useState,
  useEffect,
  createContext,
  useContext,
  useMemo,
  useCallback,
  useRef,
} from 'react';
import {
  getUserId,
  getNormalSSOToken,
  getDeviceID,
  getDeviceName,
  getOsVersion,
  getAppVersion,
} from '../utils/coreUtil';
import { getGenericAppHeaders, getPlatformValue, makeApiGetCall } from '../utils/apiUtil';
import { emptyObj, isEmpty, log } from '../utils/commonUtil';
import { CONNECTION_MODE, IR_STATUS_ENUM } from '../utils/constants';
import { useIRApi } from '../utils/IRutils';
import { useReconnect } from '../utils/react';
import { useDocumentHide } from './NativeDocumentHideContext';
import SnackbarProvider from './SnackbarProvider';
import { getUserIrStatus } from '../utils/bridgeUtils';
import { GENERIC_API_URL } from '../config/urlConfig';
import { modifyIrData } from '../query/readinessQuery';
import { AxiosErrorHandler } from '../utils/errorUtils';

const AppContext = createContext(null);

function getAuthDetails() {
  return [
    getUserId(), // user_id
    '',
    'paytmmoney', // x-pmngx-key
    getNormalSSOToken(), // x-sso-token
    getPlatformValue(), // platform
    getAppVersion(), // app_version,
    getDeviceID(), // device_id
    getDeviceName(), // model
    getOsVersion(), // os_version
    getUserId(), // user_id
  ];
}

function useIrStatus(enabled = true) {
  const data = useIRApi(enabled);
  const irStatus = useMemo(() => data?.irData?.irStatus, [data]);
  return { irStatus, setIrStatus: () => {} };
}

function AppContextProvider({
  children,
  isFeedRequired = true,
  externalDataFeed = null,
}) {
  // console.log('check isFeedRequired', isFeedRequired);
  const [snackBarList, setSnackBarList] = useState({});
  const [store, setStore] = useState({});
  const [loader, setLoader] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('');
  const [showErrorScreenData, setRootErrorScreenView] = useState({});
  const [isDataFeedReady, setIsDataFeedReady] = useState(false);
  const [error, setRootError] = useState({});
  const [dataFeed, setDataFeed] = useState(null);
  const [orderFeed, setOrderFeed] = useState(null);
  const [isOrderFeedReady, setIsOrderFeedReady] = useState(false);
  // const irStatus = useIrStatus();
  const broadCastRef = useRef({
    dataFeedRef: false,
    isDataFeedReadyRef: false,
  });
  const [irData, setIrData] = useState({ irData: {}, isLoading: true });

  // Initialize IR data
  useEffect(() => {
    if (isEmpty(irData?.irData)) {
      setIrData((prev) => ({ ...prev, isLoading: true }));

      getUserIrStatus(async (status) => {
        const irResponse = JSON.parse(status?.data || '{}');
        if (isEmpty(irResponse)) {
          try {
            const { data } = await makeApiGetCall({
              url: GENERIC_API_URL.READINESS_V5.replace('user_id', getUserId()),
              headers: getGenericAppHeaders(),
            });
            const result = { irData: modifyIrData(data?.data) };
            setIrData((prev) => ({ ...prev, ...result, isLoading: false }));
            return;
          } catch (err) {
            setIrData((prev) => ({ ...prev, isLoading: false }));
            return AxiosErrorHandler(err);
          }
        }
        setIrData((prev) => ({ ...prev, ...irResponse, isLoading: false }));
      });
    }
  }, [irData?.irData]);

  const updateStore = useCallback((key, newValue) => {
    setStore((prevState) => ({
      ...prevState,
      [key]: newValue,
    }));
  }, []);

  const addSnackBar = useCallback((options) => {
    setSnackBarList({
      message: options.message,
      type: options.type,
      TrailingIcon: options.TrailingIcon,
      customClass: options.customClass,
      autoHide: options.autoHide,
      hideIcon: options.hideIcon || false,
      dismissTime: options.dismissTime,
      dismissCallback: options.dismissCallback,
    });
  }, []);

  const removeSnackBar = () => {
    setSnackBarList({});
  };

  const showLoader = (isLoading) => {
    setLoader(isLoading);
  };

  const updateRootErrorScreenView = (obj) => {
    setRootErrorScreenView(obj);
  };

  function updateConnectionStatus(value) {
    setConnectionStatus(value);
  }

  const pauseFeed = useCallback(() => {
    if (
      isFeedRequired &&
      broadCastRef.current.dataFeedRef &&
      broadCastRef.current.isDataFeedReadyRef
    ) {
      setIsDataFeedReady(false);
      broadCastRef.current.dataFeedRef.pauseConnection();
    }
  }, [isFeedRequired]);

  const resumeFeed = useCallback(() => {
    if (
      isFeedRequired &&
      broadCastRef.current.dataFeedRef &&
      !broadCastRef.current.isDataFeedReadyRef
    ) {
      broadCastRef.current.dataFeedRef.reInitializeSocket();
      setIsDataFeedReady(true);
    }
  }, [isFeedRequired]);

  const reconnect = useCallback(() => {
    if (isFeedRequired && orderFeed) {
      orderFeed.reconnectSocket();
    }
  }, [orderFeed, isFeedRequired]);

  useReconnect(reconnect);

  useDocumentHide({
    onPause: isFeedRequired ? pauseFeed : undefined,
    onResume: isFeedRequired ? resumeFeed : undefined,
  });

  useEffect(() => {
    console.log('check isFeedRequired 1', isFeedRequired);
    if (isFeedRequired) {
      import('../services/equities').then(({ feeds, eventTypes }) => {
        const { data = null } = feeds;
        console.log('check data', data);
        if (data) {
          console.log('check getAuthDetails 1', getAuthDetails());
          data.initializeStream(getAuthDetails());
          data.addEventListener(eventTypes.connected, () => {
            setIsDataFeedReady(true);
          });
          setDataFeed(feeds?.data);
        }
      });
    } else if (externalDataFeed) {
      setDataFeed(externalDataFeed);
      setIsDataFeedReady(true);
    }
  }, [isFeedRequired, externalDataFeed]);

  useEffect(() => {
    if (dataFeed) {
      broadCastRef.current = {
        dataFeedRef: dataFeed,
        isDataFeedReadyRef: isDataFeedReady,
      };
    }
  }, [dataFeed, isDataFeedReady]);

  useEffect(() => {
    if (!window?.navigator?.onLine) {
      updateConnectionStatus(CONNECTION_MODE.OFFLINE);
    } else {
      updateConnectionStatus(CONNECTION_MODE.ONLINE);
    }

    const handleOnlineStatusChange = () => {
      updateConnectionStatus(
        window?.navigator?.onLine
          ? CONNECTION_MODE.ONLINE
          : CONNECTION_MODE.OFFLINE,
      );
    };

    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);

    return () => {
      // Clean up the event listeners when the component is unmounted
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
    };
  }, []);

  const globalStoreContextProviderValue = useMemo(
    () => ({
      store,
      updateStore,
      showLoader,
      loader,
      dataFeed: isDataFeedReady ? dataFeed : null,
      orderFeed: isOrderFeedReady ? orderFeed : null,
      updateRootErrorScreenView,
      showErrorScreenData,
      updateConnectionStatus,
      connectionStatus,
      error,
      setRootError,
      snackBarList,
      addSnackBar,
      removeSnackBar,
      irData,
      setIrData,
    }),
    [
      isDataFeedReady,
      dataFeed,
      isOrderFeedReady,
      orderFeed,
      store,
      updateStore,
      showLoader,
      loader,
      updateRootErrorScreenView,
      showErrorScreenData,
      updateConnectionStatus,
      connectionStatus,
      error,
      setRootError,
      snackBarList,
      addSnackBar,
      removeSnackBar,
      irData,
      setIrData,
    ],
  );

  return (
    <AppContext.Provider value={globalStoreContextProviderValue}>
      <SnackbarProvider>{children}</SnackbarProvider>
    </AppContext.Provider>
  );
}

const useAppStore = () => useContext(AppContext);

const useGetUserId = () => {
  const { store = {} } = useContext(AppContext) || emptyObj;
  return store?.userId;
};

export default AppContextProvider;

export { useAppStore, useIrStatus, useGetUserId };
