import { getAppTheme } from '@src/utils/bridgeUtils';
import { setDarkModeValue } from '@src/utils/coreUtil';
import React, { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext(null);

const getOnloadTheme = () => {
  const urlSearchParams = new URLSearchParams(window.location.search);
  const isDarkModeParam = urlSearchParams.get('darkmode');
  return (
    (window?.matchMedia &&
      window?.matchMedia('(prefers-color-scheme: dark)')?.matches) ||
    isDarkModeParam === 'true'
  );
};

const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(getOnloadTheme());
  const [theme, setTheme] = useState(getOnloadTheme() ? 'dark' : 'light');
  console.log('check defaultTheme', isDarkMode, theme);
  useEffect(() => {
    console.log('check useffect', getOnloadTheme());
    setDarkModeValue(getOnloadTheme());

    // if (isDarkModeParam === 'true') {
    //   setIsDarkMode(true);
    //   setDarkModeValue(true);
    //   return;
    // }
    // setIsDarkMode(false);
    getAppTheme((res) => {
      const isDarkModeEnabled = res?.data?.isNightModeEnabled === true;
      setDarkModeValue(isDarkModeEnabled);
      setIsDarkMode(isDarkModeEnabled);
    });
  }, []);

  useEffect(() => {
    const colorSchemeQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleColorSchemeChange = () => {
      window.location.reload();
    };

    colorSchemeQuery.addEventListener('change', handleColorSchemeChange);

    return () => {
      colorSchemeQuery.removeEventListener('change', handleColorSchemeChange);
    };
  }, []);

  useEffect(() => {
    if (isDarkMode === null) return;
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
      setTheme('dark');
    } else {
      document.documentElement.classList.remove('dark');
      setTheme('light');
    }
  }, [isDarkMode]);

  const value = React.useMemo(
    () => ({ isDarkMode, theme, setIsDarkMode }),
    [isDarkMode, theme],
  );

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};

const useThemeProvider = () => useContext(ThemeContext);
export { ThemeProvider, useThemeProvider };
