import {
  createContext,
  useState,
  useEffect,
  useMemo,
  useCallback,
  useContext,
  useRef,
} from 'react';

import {
  ORDER_STATUS_BUCKETS,
  productTypeLabels,
  orderTypeLabels,
  validityTypeLabels,
} from '../utils/Equities/enum';
import { IR_STATUS_ENUM } from '../utils/constants';
import { REQUEST_TYPES, RESPONSE_TYPES } from '../services/equities/index';
import { useReconnect, orderUpdateSubject } from '../utils/react';
import { useAppStore, useIrStatus } from './AppContextProvider';

import { getOrderBookDetails } from '../actions/myOrderActions';

const orderResponseTypes = [RESPONSE_TYPES.ORDER];

const orderBucketFromStatus = (orderStatus) => {
  if (!orderStatus) {
    return null;
  }
  const buckets = Object.values(ORDER_STATUS_BUCKETS);
  const match = buckets.find(
    (bucket) => bucket.values.indexOf(orderStatus) > -1,
  );
  return match?.key || null;
};

const mapFieldFromOrderStream = (order) => ({
  ...order,
  display_status: orderBucketFromStatus(order.status),
  display_product: productTypeLabels[order.product],
  display_order_type: orderTypeLabels[order.order_type],
  display_validity: validityTypeLabels[order.validity],
  leg_no: order.leg_no.toString(),
});

export const OrdersBookContext = createContext(null);

function OrdersBook({ children }) {
  const fetchCount = useRef(0);

  const [ordersData, setOrders] = useState([]);
  const [inProgress, setProgress] = useState(true);
  const [hasError, setHasError] = useState(false);

  const { orderFeed } = useAppStore();
  const { irStatus } = useIrStatus();

  const isInvestmentReady = useMemo(
    () =>
      [
        IR_STATUS_ENUM.ACTIVE,
        IR_STATUS_ENUM.REVOKED,
        IR_STATUS_ENUM.DORMANCY_IN_PROGRESS,
        IR_STATUS_ENUM.DORMANT_REVOKED,
        IR_STATUS_ENUM.REKYC_IN_PROGRESS,
      ].includes(irStatus),
    [irStatus],
  );

  const updateOrders = useCallback(
    (updatedOrder) => {
      const mappedUpdatedOrder = mapFieldFromOrderStream(updatedOrder);
      let clonedOrdersData = [...ordersData];
      const indexOfExistingOrder = clonedOrdersData.findIndex(
        ({ order_no, leg_no }) =>
          order_no === mappedUpdatedOrder.order_no &&
          leg_no === mappedUpdatedOrder.leg_no,
      );

      if (indexOfExistingOrder !== -1) {
        const existingOrder = clonedOrdersData[indexOfExistingOrder];
        clonedOrdersData.splice(indexOfExistingOrder, 1);
        clonedOrdersData = [
          { ...existingOrder, ...mappedUpdatedOrder },
          ...clonedOrdersData,
        ];
      } else {
        clonedOrdersData = [mappedUpdatedOrder, ...ordersData];
      }
      setOrders([...clonedOrdersData]);
    },
    [ordersData],
  );

  useEffect(() => {
    const orderStatusFeed = orderFeed?.getStream(
      REQUEST_TYPES.ORDER,
      null,
      orderResponseTypes,
    )[0].feed;
    const subscription = orderStatusFeed?.subscribe(updateOrders);
    return () => subscription?.unsubscribe();
  }, [orderFeed, updateOrders]);

  const getData = useCallback(() => {
    if (isInvestmentReady) {
      setProgress(true);
      setHasError(false);
      getOrderBookDetails()
        .then((res) => {
          fetchCount.current += 1;
          if (res.status === 200) {
            setOrders(res.data);
            setProgress(false);
          } else {
            setHasError(true);
            setProgress(false);
          }
        })
        .catch(() => {
          setHasError(true);
          setProgress(false);
        });
    }
  }, [isInvestmentReady]);

  useEffect(() => {
    getData();
  }, [getData]);

  useEffect(() => {
    const subscription = orderUpdateSubject.subscribe(getData);
    return () => subscription.unsubscribe();
  }, [getData]);

  useReconnect(getData);

  useEffect(() => () => setHasError(false), []);

  return (
    <OrdersBookContext.Provider
      value={{
        ordersData,
        inProgress,
        failed: hasError,
        getData,
        reFetchOrdersData: getData,
        fetchCount: fetchCount.current,
      }}
    >
      {children}
    </OrdersBookContext.Provider>
  );
}

function useOrders() {
  return useContext(OrdersBookContext);
}

export { useOrders };
export default OrdersBook;
