import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { Snackbar } from '@paytm-h5-common/paytm_common_ui';

import { SNACKBAR_THEME } from '../../enums/ToastSnackbarEnums';
import { APPEARANCE_TYPES } from '../../utils/constants';

import styles from './index.scss';

const SnackbarProviderContext = createContext(null);

const SnackbarProvider = ({ children }) => {
  const [snackBarList, setSnackBarList] = useState([]);
  const snackbarItem = useRef({});

  const addSnackbar = useCallback((options) => {
    setSnackBarList((prevSnackbarList) => [
      ...prevSnackbarList,
      {
        key: `${options?.message}${new Date().getTime()}`,
        message: options.message,
        type: options.type,
        onClick: options.onClick,
        label: options.label,
        labelAction: options.labelAction,
        closeButton: options.closeButton,
        customStyle: options.customStyle,
        autoDelete: options.autoDelete ?? true,
        customIcon: options.customIcon,
        hideIcon: options.hideIcon ?? false,
        customStyles: options.customStyles,
        toastStyle: options.toastStyle,
        component: options.component,
        dismissTime: options.dismissTime ?? 5000,
        reserveSpaceForStatusBar: false,
      },
    ]);
  }, []);

  const removeSnackBar = useCallback(() => {
    setSnackBarList([]);
  }, []);

  useEffect(() => {
    if (snackBarList.length) {
      snackbarItem.current = { ...snackBarList[0] };
      setTimeout(() => {
        setSnackBarList((prevSnackBarList) => {
          prevSnackBarList.shift();
          return [...prevSnackBarList];
        });
      }, snackbarItem.current.dismissTime + 300);
    }
  }, [snackBarList]);

  const getSnackbarTheme = (type) => {
    switch (type) {
      case APPEARANCE_TYPES.INFO:
        return SNACKBAR_THEME.NOTICE;

      case APPEARANCE_TYPES.FAIL:
        return SNACKBAR_THEME.NEGATIVE;
      default:
        return SNACKBAR_THEME.POSITIVE;
    }
  };

  const snackbarProviderValue = useMemo(
    () => ({
      snackBarList,
      addSnackbar,
      removeSnackBar,
    }),
    [snackBarList, addSnackbar, removeSnackBar],
  );

  return (
    <SnackbarProviderContext.Provider value={snackbarProviderValue}>
      {children}
      {snackBarList[0] && Object.keys(snackBarList[0]).length && (
        <Snackbar
          noClamp
          key={snackBarList[0]?.key}
          text={snackBarList[0]?.message}
          theme={getSnackbarTheme(snackBarList[0]?.type)}
          autoHide={snackBarList[0]?.autoDelete}
          autoHideAfter={snackBarList[0]?.dismissTime}
          reserveSpaceForStatusBar={snackBarList[0]?.reserveSpaceForStatusBar}
          {...snackBarList[0]?.snackBarProps}
          customClass={styles.snackbarCustomClass}
        />
      )}
    </SnackbarProviderContext.Provider>
  );
};

const useSnackbarProviderContext = () => useContext(SnackbarProviderContext);

const useSnackbar = () => {
  const ctx = useSnackbarProviderContext();

  const showSnackbar = (message, type) => {
    ctx.removeSnackBar();
    ctx.addSnackbar({
      message,
      hideIcon: true,
      type: type || APPEARANCE_TYPES.FAIL,
    });
  };

  return {
    addSnackbar: ctx.addSnackbar,
    appearanceType: APPEARANCE_TYPES,
    removeSnackBar: ctx.removeSnackBar,
    showSnackbar,
  };
};

export default SnackbarProvider;

export { useSnackbar, SnackbarProviderContext, useSnackbarProviderContext };
