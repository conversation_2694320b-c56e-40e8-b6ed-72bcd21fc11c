import { createContext, useEffect, useContext } from 'react';
import { useNativeDocumentHide } from '../utils/react';

const NativeDocumentHideContext = createContext();

function NativeDocumentHideProvider({ children }) {
  const DocumentEvents = useNativeDocumentHide();
  return (
    <NativeDocumentHideContext.Provider value={DocumentEvents}>
      {children}
    </NativeDocumentHideContext.Provider>
  );
}

const useDocumentHide = ({ onPause, onResume }) => {
  const { pushPauseCallbackEvent, pushResumeCallbackEvent } = useContext(
    NativeDocumentHideContext,
  );

  useEffect(() => {
    if (onResume && pushResumeCallbackEvent) {
      pushResumeCallbackEvent(onResume);
    }
    if (onPause && pushPauseCallbackEvent) {
      pushPauseCallbackEvent(onPause);
    }
  }, [onPause, onResume, pushPauseCallbackEvent, pushResumeCallbackEvent]);
};
export { useDocumentHide };

export default NativeDocumentHideProvider;
