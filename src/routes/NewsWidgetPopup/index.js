import { useEffect } from 'react';
import queryString from 'query-string';
import { setDailySipCohort, setETFWidgetType } from '../../utils/commonUtil';
import CentralLoader from '../../components/atoms/CentralLoader/CentralLoader';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { sendErrorToBackend } from '../../actions/runtime';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';
import NewsWidgetPopupPage from '../../pages/NewsWidgetPopupPage';
import { LOCAL_STORAGE } from '../../components/organisms/NewsWidget/enums';

const NewsWidgetPopupWrapper = ({ pages = {} }) => {
  if (!pages.data) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'news-widget-popup-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  return <NewsWidgetPopupPage data={pages} />;
};

const NewsWidgetPopupPageRoute = () => {
  const query = queryString.parse(window.location?.search);
  const {
    cohort = '',
    widgetType = '',
    aggrKey,
    businessType,
    widgetId,
  } = query || {};

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setETFWidgetType(widgetType);
  }, [cohort, widgetType]);

  const newsWidgetPopupData = localStorage.getItem(
    LOCAL_STORAGE.NEWS_POPUP_DATA,
  );

  if (newsWidgetPopupData) {
    const parsedData = JSON.parse(newsWidgetPopupData);

    return (
      <NewsWidgetPopupWrapper
        pages={
          parsedData?.data
            ? parsedData
            : {
                widgetId,
                data: parsedData,
              }
        }
      />
    );
  }

  if (!aggrKey) {
    return null;
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'NewsWidgetPopup',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.STOCKS_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.STOCKS_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <CentralLoader />,
  })(NewsWidgetPopupWrapper);

  return <WrappedComponent />;
};

export default NewsWidgetPopupPageRoute;
