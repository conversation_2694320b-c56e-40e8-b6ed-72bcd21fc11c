import { useEffect } from 'react';
import queryString from 'query-string';
import ETFSelectionPage from '../../pages/ETFSelectionPage/ETFSelectionPage';
import { setDailySipCohort, setETFWidgetType } from '../../utils/commonUtil';
import CentralLoader from '../../components/atoms/CentralLoader/CentralLoader';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { sendErrorToBackend } from '../../actions/runtime';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';

const ETFPopupWrapper = ({ pages = {} }) => {
  if (!pages.data) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'etf-selection-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  console.log('ETFPopupWrapper pages:', pages);
  return <ETFSelectionPage data={pages} />;
};

const ETFSelectionPopupPageRoute = () => {
  const query = queryString.parse(window.location?.search);
  const {
    cohort = '',
    widgetType = '',
    aggrKey,
    businessType,
    widgetId,
  } = query || {};

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setETFWidgetType(widgetType);
  }, [cohort, widgetType]);

  const etfWidgetLsKey =
    widgetId === 'gold-etf-widget-at' ? 'etfThemedNativeData' : 'etfNativeData';
  const etfWidgetData = localStorage.getItem(etfWidgetLsKey);

  if (etfWidgetData) {
    const parsedData = JSON.parse(etfWidgetData);
    return (
      <ETFPopupWrapper
        pages={
          parsedData?.data
            ? parsedData
            : {
                widgetId,
                data: parsedData,
              }
        }
      />
    );
  }

  if (!aggrKey) {
    return null;
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'ETFCard',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <CentralLoader />,
  })(ETFPopupWrapper);

  return <WrappedComponent />;
};

export default ETFSelectionPopupPageRoute;
