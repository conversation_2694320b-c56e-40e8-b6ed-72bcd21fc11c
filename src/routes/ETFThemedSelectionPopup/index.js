import { useEffect } from 'react';
import queryString from 'query-string';
import ETFSelectionPage from '../../pages/ETFSelectionPage/ETFSelectionPage';
import { setDailySipCohort, setETFWidgetType } from '../../utils/commonUtil';
import CentralLoader from '../../components/atoms/CentralLoader/CentralLoader';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { sendErrorToBackend } from '../../actions/runtime';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';

const ETFThemedPopupWrapper = ({ pages = {} }) => {
  if (!pages.data) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'etf-selection-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  console.log('ETFThemedPopupWrapper pages:', pages);
  return <ETFSelectionPage data={pages} />;
};

const ETFThemedSelectionPopupPageRoute = () => {
  const query = queryString.parse(window.location?.search);
  const {
    cohort = '',
    widgetType = '',
    aggrKey,
    businessType,
    widgetId,
  } = query || {};

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setETFWidgetType(widgetType);
  }, [cohort, widgetType]);

  const etfWidgetLsKey = 'etfThemedNativeData';
  const etfWidgetData = localStorage.getItem(etfWidgetLsKey);

  if (etfWidgetData) {
    const parsedData = JSON.parse(etfWidgetData);
    return (
      <ETFThemedPopupWrapper
        pages={
          parsedData?.data
            ? parsedData
            : {
                widgetId,
                data: parsedData,
              }
        }
      />
    );
  }

  if (!aggrKey) {
    return null;
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'ETFCard',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <CentralLoader />,
  })(ETFThemedPopupWrapper);

  return <WrappedComponent />;
};

export default ETFThemedSelectionPopupPageRoute;
