import { useEffect } from 'react';
import queryString from 'query-string';

import { setDailySipCohort, setIsDailySipMF } from '../../utils/commonUtil';
import FundSelectionMonthlyPopup from '../../components/organisms/FundSelectionMonthlyPopup/FundSelectionMonthlyPopup';

const FundSelectionMonthlyPopupPageRoute = () => {
  const query = queryString.parse(window.location?.search);
  const { cohort = '', isDailySipMF = false } = query || {};

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setIsDailySipMF(isDailySipMF);
  }, []);

  return <FundSelectionMonthlyPopup />;
};

export default FundSelectionMonthlyPopupPageRoute;
