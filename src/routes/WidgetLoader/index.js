import { useEffect } from 'react';

const WidgetLoader = () => {
  useEffect(() => {
    // Preload ETFCard chunk
    import(/* webpackChunkName: "ETFCard" */ '../ETFCard');

    // Preload FirstStockCard chunk
    import(/* webpackChunkName: "FirstStockCard" */ '../FirstStockPage');

    // Preload ReminderWidget chunk
    import(/* webpackChunkName: "ReminderWidget" */ '../ReminderWidget');

    // Preload ReminderWidgetList chunk
    import(
      /* webpackChunkName: "ReminderWidgetList" */ '../ReminderWidgetList'
    );

    // Preload NewsWidget chunk
    import(/* webpackChunkName: "NewsWidget" */ '../NewsWidget');

    // Preload NewsWidgetList chunk
    import(/* webpackChunkName: "NewsWidgetList" */ '../NewsWidgetList');

    // Preload NewsWidgetPopup chunk
    import(/* webpackChunkName: "NewsWidgetPopup" */ '../NewsWidgetPopup');

    // Preload FOIndexAnalysisWidget chunk
    import(
      /* webpackChunkName: "FOIndexAnalysisWidget" */ '../FOIndexAnalysisWidget'
    );

    // You can also add logic here to redirect or display something
  }, []);

  return <div>Widget Loader - Preloading ETF and FirstStock chunks</div>;
};

export default WidgetLoader;
