import { useEffect } from 'react';

import { exitApp } from '../../utils/bridgeUtils';
import DailySipDisclaimer from '../../components/molecules/DailySipDisclaimer/DailySipDisclaimer';
import {
  getDeeplinkData,
  setDailySipCohort,
  setIsDailySipMF,
} from '../../utils/commonUtil';

const MFDailySIPDisclaimer = () => {
  const cohort = getDeeplinkData('cohort');
  const isDailySipMF = getDeeplinkData('isDailySipMF');

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setIsDailySipMF(!!isDailySipMF);
  }, []);

  return <DailySipDisclaimer active triggerClose={exitApp} />;
};

export default MFDailySIPDisclaimer;
