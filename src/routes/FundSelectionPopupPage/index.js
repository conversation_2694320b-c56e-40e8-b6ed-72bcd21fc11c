import { useEffect } from 'react';

import FundSelectionPopup from '../../pages/FundSelectionPage/FundSelectionPage';
import {
  getDeeplinkData,
  setDailySipCohort,
  setIsDailySipMF,
} from '../../utils/commonUtil';

const FundSelectionPopupPageRoute = () => {
  const cohort = getDeeplinkData('cohort');
  const isDailySipMF = getDeeplinkData('isDailySipMF');

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setIsDailySipMF(!!isDailySipMF);
  }, []);

  return <FundSelectionPopup />;
};

export default FundSelectionPopupPageRoute;
