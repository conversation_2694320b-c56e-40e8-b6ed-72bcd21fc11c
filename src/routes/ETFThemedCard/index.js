import { useEffect, useRef, useState } from 'react';
import queryString from 'query-string';

import ETFThemedCard from '../../pages/ETFThemedCard';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import { getStartupParamsAllCallback } from '../../utils/bridgeUtils';
import { useNotifyHeight } from '../../hooks/useNotifyHeight';
import { sendErrorToBackend } from '../../actions/runtime';
import ETFLoader from '../../components/molecules/ETFLoader/ETFLoader';
import styles from './index.scss';
import { BUSINESS_TYPE_MAPPINGS } from '../../utils/constants';

const SipCardRouteWrapper = ({ pages = {}, aggrKey = '' }) => {
  const cardRef = useRef(null);
  useNotifyHeight(cardRef, pages?.widgetId, {
    flowType:
      BUSINESS_TYPE_MAPPINGS[pages?.data?.businessType]?.height ||
      'combinedHomeH5FragmentHeight',
  });

  // Add cleanup effect to remove etfNativeData on unmount
  useEffect(
    () => () => {
      if (localStorage.getItem('etfThemedNativeData')) {
        localStorage.removeItem('etfThemedNativeData');
      }
    },
    [],
  );

  if (!pages.data) {
    try {
      sendErrorToBackend({
        level: 'error',
        key: 'gold-etf-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  return (
    <div ref={cardRef} className={styles.etfCardPage}>
      <ETFThemedCard data={pages} aggrKey={aggrKey} />
    </div>
  );
};

const SipCardRoute = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [nativeData, setNativeData] = useState(null);
  const [widgetId, setwidgetId] = useState(null);
  const query = queryString.parse(window.location?.search);
  const { aggrKey, businessType } = query || {};

  useEffect(() => {
    getStartupParamsAllCallback((result) => {
      if (result?.nativeData) {
        const parsedData = JSON.parse(result.nativeData);
        localStorage.setItem('etfThemedNativeData', JSON.stringify(parsedData));
        setNativeData(parsedData);
        setwidgetId(result?.widgetId);
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }, []);

  if (!aggrKey) {
    return null;
  }

  if (isLoading) {
    return <ETFLoader />;
  }

  if (nativeData) {
    return (
      <SipCardRouteWrapper
        pages={
          nativeData?.data
            ? nativeData
            : {
                widgetId: widgetId || nativeData?.widgetType,
                data: nativeData,
              }
        }
        aggrKey={aggrKey}
      />
    );
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'ETFCard',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <ETFLoader />,
  })(SipCardRouteWrapper);

  return <WrappedComponent aggrKey={aggrKey} />;
};

export default SipCardRoute;
