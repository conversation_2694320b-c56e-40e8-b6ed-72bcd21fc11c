import { useEffect } from 'react';

import MfDailySipSelectAmount from '../../pages/MfDailySipSelectAmount';
import { exitApp } from '../../utils/bridgeUtils';
import {
  getDeeplinkData,
  setDailySipCohort,
  setIsDailySipMF,
} from '../../utils/commonUtil';

const MfDailySipSelectAmountRoute = () => {
  const fund = JSON.parse(localStorage.getItem('selectedFund'));
  const cohort = getDeeplinkData('cohort');
  const isDailySipMF = getDeeplinkData('isDailySipMF');

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setIsDailySipMF(!!isDailySipMF);
    return () => {
      localStorage.removeItem('selectedFund');
    };
  }, []);

  return (
    <MfDailySipSelectAmount
      fund={fund}
      showAmountSelectionPopup
      onAmountSelectionPopupClose={exitApp}
    />
  );
};

export default MfDailySipSelectAmountRoute;
