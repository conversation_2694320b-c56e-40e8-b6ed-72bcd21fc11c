const ACTIONS = {
  FIRST_TRADE_ORDER_PAD_LANDED: 'first_trade_order_pad_landed',
  EXCHANGE_SELECTED: 'exchange_selected',
  QUANTITY_SELECTED: 'quantity_selected',
  MARKET_DEPTH_CLICKED: 'market_depth_clicked',
  FUNDS_REQUIRED_LINK_CLICKED: 'funds_required_link_clicked',
  CHARGES_LINK_CLICKED: 'charges_link_clicked',
  FIRST_ORDER_PAD_SCREEN_BACK_CLICKED: 'First_Order_Pad_Screen_back_clicked',
  CHARGES_BOTTOMSHEET_DISPLAYED: 'charges_bottomsheet_displayed',
  CHARGES_BOTTOMSHEET_CTA_CLICKED: 'charges_bottomsheet_CTA_clicked',
  AVAILABLE_BALANCE_LINK_CLICKED: 'Available_balance_link_clicked',
  AVAILABLE_BALANCE_DISPLAYED: 'available_balance_displayed',
  AVAILABLE_BALANCE_CTA_CLICKED: 'available_balance_CTA_clicked',
  BUY_ORDER_CLICKED: 'Buy_order_clicked',
  INVESTCARE_ERROR_RECEIVED: 'InvestCare_error_received',
  INVESTCARE_BOTTOM_SHEET_DISPLAYED: 'InvestCare_bottom_sheet_displayed',
  INVESTCARE_BOTTOM_SHEET_YES_CLICKED: 'InvestCare_bottom_sheet_Yes_clicked',
  INVESTCARE_BOTTOM_SHEET_NO_CLICKED: 'InvestCare_bottom_sheet_No_clicked',
  INSUFFICIENT_FUND_ERROR_RECEIVED: 'Insufficient_fund_error_received',
  ORDER_LOTSIZE_ERROR_RECEIVED: 'order_lotsize_error_received',
  ORDER_PAD_ERROR_RECEIVED: 'order_pad_error_received',
  INSUFFICIENT_FUND_BOTTOMSHEET_LANDED: 'Insufficient_fund_bottomsheet_landed',
  ADD_FUND_INSUFFICIENT_FUND_BOTTOMSHEET_CLICKED:
    'Add_fund_Insufficient_fund_bottomsheet_clicked',
  MODIFY_ORDER_INSUFFICIENT_FUND_BOTTOMSHEET_CLICKED:
    'Modify_order_Insufficient_fund_bottomsheet_clicked',
  FIRST_ORDER_SUCCESS_SCREEN_LANDED: 'First_Order_success_screen_landed',
  VIEW_DETAILS_FIRST_ORDER_SUCCESS_SCREEN_CLICKED:
    'view_details_First_Order_success_screen_clicked',
  GO_TO_HOMEPAGE_FIRST_ORDER_SUCCESS_SCREEN_CTA_CLICKED:
    'Go_to_homepage_First_Order_success_screen_CTA_clicked',
  NEED_HELP_ORDER_STATUS_BOTTOMSHEET_CLICKED:
    'Need_help_Order_status_bottomsheet_clicked',
  CANCEL_ORDER_STATUS_BOTTOMSHEET_CLICKED:
    'Cancel_Order_status_bottomsheet_clicked',
  ORDER_STATUS_BOTTOMSHEET_LANDED: 'Order_status_bottomsheet_landed',
};

const SCREENS = {
  ADD_FUND_SUCCESS: 'First_Order_Pad_Screen',
  ORDER_SUCCESS_SCREEN: 'First_Order_success_screen',
  ORDER_STATUS_BOTTOMSHEET_FIRST_ORDER_SUCCESS:
    'Order_status_bottomsheet_first_order_success',
};

const CATEGORY = {
  ONBOARDING_V3: 'Onboarding_V3',
};

export { ACTIONS, CATEGORY, SCREENS };
