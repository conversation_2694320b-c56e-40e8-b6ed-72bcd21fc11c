import { useQuery } from '@tanstack/react-query';
import {
  getGenericAppHeaders,
  makeApiGetCall,
  makeApiPostCall,
} from '../utils/apiUtil';

import { useCombinedIr } from './readinessQuery';
import { isEmpty } from '../utils/commonUtil';

const FLOATING_WIDGETS = 'floating-widget';

const sortCardOrder = (cardOrder) =>
  cardOrder?.sort((a, b) => a.priority - b.priority);

const getComponentsArray = (data, order = []) => {
  const { cards, staticCards } = data;
  if (data[FLOATING_WIDGETS]) {
    order.push({
      card: FLOATING_WIDGETS,
    });
  }
  return order.map((element) => {
    const { card } = element;
    if (data[card]) {
      return {
        ...element,
        data: data[card],
      };
    }
    if (cards[card]) {
      return {
        ...element,
        data: cards[card],
      };
    }
    if (staticCards[card]) {
      return {
        ...element,
        data: staticCards[card],
      };
    }
    if (card === FLOATING_WIDGETS) {
      return {
        ...element,
        data: data[FLOATING_WIDGETS],
      };
    }
    return element;
  });
};

const orderPages = (pageData) => {
  const { data } = pageData;
  const sortedCardOrder = sortCardOrder(data?.cardOrder);
  return getComponentsArray(data, sortedCardOrder) || [];
};

export const useAggregator = (
  { name, url, fallbackUrl, queryParams },
  enabled = true,
) => {
  const { data: readinessData } = useCombinedIr(enabled);

  const irData = readinessData?.irData;
  return useQuery(
    ['aggregator', name],
    async () => {
      try {
        const { data } = await makeApiPostCall({
          url,
          headers: getGenericAppHeaders(),
          body: readinessData,
          queryParams,
        });

        /** Hardcode profile progress card for combinedDashboard */
        if (name === 'combinedDashboard' && data?.data?.cardOrder) {
          const cardOrder = data?.data?.cardOrder;
          const cardNames = [
            'portfolio-overview',
            'invest-and-trade',
            'sleek-card',
            'indices',
          ];
          const profileProgressCard = {
            card: 'profile-progress',
            priority: 201,
          };

          for (const cardName of cardNames) {
            const card = cardOrder.find((item) => item.card === cardName);
            if (card) {
              profileProgressCard.priority = card.priority + 1;
              break;
            }
          }

          cardOrder.push(profileProgressCard);
        }

        return orderPages(data);
      } catch (error) {
        const { data } = await makeApiGetCall({
          url: fallbackUrl,
        });
        return orderPages(data);
      }
    },
    { staleTime: Infinity, enabled: !isEmpty(irData) },
  );
};

const getKeyData = (pageData, key) => {
  const { data = {} } = pageData;
  const { cards = {}, staticCards = {} } = data;
  return cards[key] || staticCards[key] || {};
};

export const useAggregatorKey = (
  { name, url, fallbackUrl, queryParams },
  enabled = true,
) => {
  const { irData } = useCombinedIr(enabled);
  return useQuery(
    ['aggregatorKey', name],
    async () => {
      try {
        const { data } = await makeApiPostCall({
          url,
          headers: getGenericAppHeaders(),
          body: { irData },
          queryParams,
        });
        return getKeyData(data, queryParams?.keys);
      } catch (error) {
        const { data } = await makeApiGetCall({
          url: fallbackUrl,
        });
        return orderPages(data);
      }
    },
    { staleTime: Infinity, enabled: !isEmpty(irData) },
  );
};

export const useMFAggregatorKey = (
  { name, url, fallbackUrl, queryParams },
  enabled = true,
) =>
  useQuery(
    ['aggregatorKey', name],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url,
          headers: getGenericAppHeaders(),
          queryParams,
        });
        return data?.data?.[queryParams?.keys];
      } catch (error) {
        const { data } = await makeApiGetCall({
          url: fallbackUrl,
        });
        return orderPages(data);
      }
    },
    { staleTime: Infinity, enabled },
  );

export const useFundSectionAggregatorKey = (
  { name, url, fallbackUrl, queryParams },
  enabled = true,
) => {
  const { data: readinessData } = useCombinedIr(enabled);
  const irData = readinessData?.irData;
  return useQuery(
    ['fundSectionAggregatorKey', name],
    async () => {
      try {
        const { data } = await makeApiPostCall({
          url,
          headers: getGenericAppHeaders(),
          queryParams,
          body: irData,
        });
        return data?.data?.[queryParams?.keys];
      } catch (error) {
        const { data } = await makeApiGetCall({
          url: fallbackUrl,
        });
        return orderPages(data);
      }
    },
    { staleTime: Infinity, enabled: !isEmpty(irData) },
  );
};
