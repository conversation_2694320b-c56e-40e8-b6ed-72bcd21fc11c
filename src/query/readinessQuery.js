import { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';

import {
  getGenericAppHeaders,
  makeApiGetCall,
  makeApiPostCall,
} from '../utils/apiUtil';
import { GENERIC_API_URL } from '../config/urlConfig';
import { getUserId } from '../utils/coreUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';
import { emptyObj, isEmpty } from '../utils/commonUtil';
import { useAppStore } from '../contexts/AppContextProvider';
import { getUserIrStatus } from '../utils/bridgeUtils';

export const modifyIrData = (data) => {
  const combinedIrModel = {};
  combinedIrModel.eqCashIrStatus = data.EQUITY[0].irStatus || '';
  combinedIrModel.eqCashRevokedSubType = data.EQUITY[0].revokeSubType || '';
  combinedIrModel.eqCashHasInvested = data.EQUITY[0].hasInvested || '';
  combinedIrModel.eqCashHasTraded = data.EQUITY[0].hasTraded || '';
  combinedIrModel.eqFOIrStatus = data.EQUITY[1].irStatus || '';
  combinedIrModel.eqFORevokedSubType = data.EQUITY[1].revokeSubType || '';
  combinedIrModel.eqFOHasInvested = data.EQUITY[1].hasInvested || '';
  combinedIrModel.eqFOHasTraded = data.EQUITY[1].hasTraded || '';
  combinedIrModel.mfIrStatus = data.MUTUAL_FUND[0].irStatus || '';
  combinedIrModel.mfHasInvested = data.MUTUAL_FUND[0].hasInvested || '';
  combinedIrModel.mfRevokedSubType = data.MUTUAL_FUND[0].revokeSubType || '';
  combinedIrModel.npsIrStatus = data.NPS[0].irStatus || '';
  combinedIrModel.npsHasInvested = data.NPS[0].hasInvested || '';
  combinedIrModel.npsRevokedSubType = data.NPS[0].revokeSubType || '';
  return combinedIrModel;
};

export const useUserReadiness = (isEnabled = true) =>
  useQuery(
    ['userReadinessv1'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: GENERIC_API_URL.READINESS_V5.replace('user_id', getUserId()),
          headers: getGenericAppHeaders(),
        });
        localStorage.setItem('userReadinessv1', JSON.stringify(data));
        const result = { irData: modifyIrData(data?.data) };
        return result;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);

        const localData = JSON.parse(localStorage.getItem('userReadinessv1'));

        const result = { irData: modifyIrData(localData?.data) };
        return result;
      }
    },
    { staleTime: 0, enabled: isEnabled },
  );

export const useKycMessage = (isEnabled) => {
  const { data: readinessData } = useUserReadiness(isEnabled);
  const irData = readinessData?.irData;

  return useQuery(
    ['kyc message', { ...irData }],
    async () => {
      const headers = getGenericAppHeaders();
      const url = GENERIC_API_URL.KYC_MESSAGE;
      try {
        const request = await makeApiPostCall({
          url,
          headers,
          body: { irData },
        });
        return request.data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
        throw error;
      }
    },
    { staleTime: 0, enabled: !!irData },
  );
};

export const useCombinedIr = () => {
  const { irData, setIrData } = useAppStore() || emptyObj;
  console.log('useCombinedIr', irData, setIrData);
  useEffect(() => {
    // Only fetch if we don't have data or if we're in a loading state
    if (isEmpty(irData?.irData) || irData?.isLoading) {
      // Set loading state when starting to fetch data
      setIrData((prev) => ({ ...prev, isLoading: true }));

      getUserIrStatus(async (status) => {
        const irResponse = JSON.parse(status?.data || '{}');
        if (isEmpty(irResponse)) {
          try {
            const { data } = await makeApiGetCall({
              url: GENERIC_API_URL.READINESS_V5.replace('user_id', getUserId()),
              headers: getGenericAppHeaders(),
            });
            const result = { irData: modifyIrData(data?.data) };
            setIrData((prev) => ({ ...prev, ...result, isLoading: false }));
            return;
          } catch (error) {
            setIrData((prev) => ({ ...prev, isLoading: false }));
            return AxiosErrorHandler(error);
          }
        }
        setIrData((prev) => ({ ...prev, ...irResponse, isLoading: false }));
      });
    }
  }, [irData?.irData, irData?.isLoading, setIrData]);

  return irData;
};
