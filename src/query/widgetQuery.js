import { useQuery } from '@tanstack/react-query';
import { getGenericAppHeaders, makeApiGetCall } from '../utils/apiUtil';
import { AGGREGATOR_API, BANNERS_DATA } from '../config/urlConfig';
import { AxiosErrorHandler } from '../utils/errorUtils';

export const useWidgets = (enabled=true) => useQuery(
    ['widgets'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: AGGREGATOR_API.WIDGETS,
          headers: getGenericAppHeaders(),
        });
        return data?.data?.cards?.['container-1'] || {};
      } catch (e) {
        AxiosErrorHandler(e, false, false, false, true);
        return {};
      }
    },
    {
      staleTime: Infinity,
      enabled
    },
  );

export const useBanners=()=>useQuery(
  ['bannersData'],
  async()=>{
    try{
      const {data} = await makeApiGetCall({
        url:BANNERS_DATA
      })
      return data
    }
    catch(e){
      AxiosErrorHandler(e, false, false, false, true);
        return [];
    }
  }
)
