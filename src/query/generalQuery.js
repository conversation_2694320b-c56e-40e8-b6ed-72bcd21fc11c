import { useQuery } from '@tanstack/react-query';
import {
  makeApiGetCall,
  getGenericAppHeaders,
  makeApiPostCall,
  makeImageWithAuth,
} from '../utils/apiUtil';
import { GENERIC_API_URL, MY_FUNDS_DETAILS_API } from '../config/urlConfig';
import { BASE_URL } from '../config/envConfig';
import { getUserId } from '../utils/coreUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';
import { HAS_INVESTED_STATUS } from '../utils/constants';
import { PRODUCT_TYPE } from '../utils/Equities/enum';
import { normalizeData, getUpdatedIrStatus } from '../utils/IRutils';

const headerWithProductSource = () => ({
  ...getGenericAppHeaders(),
  'x-product-source': 'EQUITY',
});

export const formatV2resToV5Summary = (data) => ({
  ...data,
  data: {
    funds_summary: {
      trade_balance: data?.data?.available_funds,
      withdrawal_balance: data?.data?.available_funds,
      available_cash: data?.data?.available_funds,
      utilised_amount: data?.data?.margin_utilised,
      collaterals: 0,
    },
  },
});

const showReKycCta = (accountModificationData) =>
  accountModificationData?.length === 0 ||
  !!accountModificationData?.find(
    (item) =>
      item?.modificationRequestType === 'REKYC' && item.nextAction !== 'WAIT',
  );

function modifyUserReadinessData(data) {
  const responseObject = {};
  const { EQUITY = [], MUTUAL_FUND = [] } = data?.data || {};
  const { irStatus: MFirStatus } = MUTUAL_FUND?.[0] || {};
  EQUITY.forEach((element) => {
    if (element.subProduct === 'CASH') {
      responseObject.CASH = element;
    } else if (element.subProduct === 'FO') responseObject.FO = element;
  });
  const { hasInvested, irStatus, profileStatus } = responseObject.CASH;
  const { irStatus: FOirStatus } = responseObject?.FO || {};
  return {
    ...data,
    hasInvested,
    investmentStatus:
      hasInvested === HAS_INVESTED_STATUS.HAS_INVESTED ||
      hasInvested === HAS_INVESTED_STATUS.REDEEMED_ALL,
    irStatus,
    FOirStatus,
    MFirStatus,
    profileStatus,
  };
}

export const useUserReadiness = (isEnabled) =>
  useQuery(
    ['userReadiness'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: GENERIC_API_URL.READINESS_V5.replace('user_id', getUserId()),
          headers: headerWithProductSource(),
        });
        localStorage.setItem('userReadinessData', JSON.stringify(data));
        return modifyUserReadinessData(data);
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);

        const localData = JSON.parse(localStorage.getItem('userReadinessData'));

        return modifyUserReadinessData(localData);
      }
    },
    { staleTime: Infinity, enabled: isEnabled },
  );

export const useIrData = (isEnabled = true) => {
  const { status } = useUserReadiness();

  return useQuery(
    ['irData'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: GENERIC_API_URL.IR(getUserId()),
          headers: getGenericAppHeaders(),
        });
        const result = normalizeData(data?.data);
        if (result) {
          return {
            data: data?.data,
            normalizeData: {
              ...result,
              irStatus: getUpdatedIrStatus(result?.irStatus),
            },
          };
        }
        return null;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
      }
    },
    {
      staleTime: Infinity,
      enabled: status === 'success' && isEnabled,
      retry: 1,
    },
  );
};

export const useAccountModificationStatus = (isEnabled) =>
  useQuery(
    ['accountModification'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: GENERIC_API_URL.GET_ACCOUNT_MODIFICATION_STATUS(getUserId()),
          headers: headerWithProductSource(),
        });
        return {
          data: data?.data,
          showReKycCta: showReKycCta(data?.data),
        };
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
      }
    },
    { staleTime: Infinity, enabled: isEnabled },
  );

export const useOrderRestrictionJson = () =>
  useQuery(
    ['order-restriction'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: BASE_URL.ORDER_RESTRICTION_CONFIG,
        });
        return data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
      }
    },
    {
      staleTime: Infinity,
    },
  );

export const useGetFundsSummary = (triggerApi = true) =>
  useQuery(
    ['fundsSummary v2'],
    async () => {
      try {
        const { data } = await makeApiPostCall({
          url: MY_FUNDS_DETAILS_API.GET_ORDER_FUNDS_SUMMARY_V2,
          headers: getGenericAppHeaders(),
          body: {
            transaction_type: 'B',
            product_type: 'C',
            instrument_type: 'ES',
            is_sgb: false,
          },
        });
        return formatV2resToV5Summary(data);
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
      }
    },
    {
      enabled: triggerApi,
    },
  );

export const useGetOrderFundsSummary = (body, enabled) =>
  useQuery(
    ['orderFundsSummary'],
    async () => {
      try {
        const { data } = await makeApiPostCall({
          url: MY_FUNDS_DETAILS_API.GET_ORDER_FUNDS_SUMMARY,
          headers: getGenericAppHeaders(),
          body,
        });
        return data;
      } catch (error) {
        AxiosErrorHandler(error);
      }
    },
    {
      enabled,
    },
  );

export const useCurrentPlan = (isEnabled = true) =>
  useQuery(
    ['currentPlan'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: GENERIC_API_URL.GET_CUSTOMER_PLAN(getUserId(), PRODUCT_TYPE.EQ),
          headers: getGenericAppHeaders(),
        });

        return data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
      }
    },
    { staleTime: Infinity, enabled: isEnabled },
  );

export const useImgAuth = (url) =>
  useQuery(
    ['imgWithAuth', url],
    async () => {
      try {
        const data = await makeImageWithAuth(url);
        return data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
      }
    },
    { staleTime: Infinity, enabled: !!url },
  );

export const useUserBoot = (isEnabled) =>
  useQuery(
    ['userBoot'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: GENERIC_API_URL.USER_BOOT(getUserId()),
          headers: headerWithProductSource(),
        });

        localStorage.setItem('userBootData', JSON.stringify(data.data));
        return data.data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
      }
    },
    { staleTime: Infinity, enabled: isEnabled },
  );
