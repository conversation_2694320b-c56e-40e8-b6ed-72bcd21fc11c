/* eslint-disable react-hooks/rules-of-hooks */
import { useMutation, useQuery } from '@tanstack/react-query';
import { AxiosErrorHandler } from '../utils/errorUtils';
import { queryClient } from '../provider/ReactQueryProvider';

import { FAV_API_URLS } from '../config/urlConfig';

import {
  makeApiPutCall,
  makeApiDeleteCall,
  getGenericAppHeaders,
} from '../utils/apiUtil';
import { getAllWatchlist } from '../actions/stockActions';

export const reorderWatchList = () =>
  useMutation(
    (data) =>
      makeApiPutCall({
        url: FAV_API_URLS.REORDER_WATCHLIST,
        body: {
          watchlists: data.watchlists.map((watchlist) => watchlist.id),
        },
        headers: getGenericAppHeaders(),
      }),
    {
      onMutate: async (data) => {
        await queryClient.cancelQueries(['allWatchlist']);
        const previousValue = queryClient.getQueriesData(['allWatchlist']);

        queryClient.setQueriesData(['allWatchlist'], (old) => ({
          ...old,
          watchlists: [...data.watchlists],
        }));
        return previousValue;
      },
      onError: (err, variables, previousValue) => {
        AxiosErrorHandler(err, false, false, false, true);
        queryClient.setQueryData(['allWatchlist'], previousValue);
      },

      onSettled: () => {
        queryClient.invalidateQueries(['allWatchlist']);
      },
    },
  );

export const reorderWatchListStocks = (onSuccess = () => {}) =>
  useMutation(
    (data) =>
      makeApiPutCall({
        url: FAV_API_URLS.REORDER_WATCHLIST_STOCKS(data.id),
        body: {
          securities: data.securities.map((security) => security.id),
        },
        headers: getGenericAppHeaders(),
      }),
    {
      onMutate: async (data) => {
        await queryClient.cancelQueries(['allWatchlist']);
        const previousValue = queryClient.getQueriesData(['allWatchlist']);

        queryClient.setQueriesData(['allWatchlist'], (old) => ({
          ...old,
          watchlists:
            old?.watchlist?.map((watchlist) => {
              if (watchlist.id === data.id) {
                return {
                  ...watchlist,
                  securities: [...data.watchlists],
                };
              }
              return watchlist;
            }) || [],
        }));
        onSuccess(previousValue);
        return previousValue;
      },
      onError: (err, variables, previousValue) => {
        AxiosErrorHandler(err, false, false, false, true);
        queryClient.setQueryData(['allWatchlist'], previousValue);
      },

      onSettled: () => {
        queryClient.invalidateQueries(['allWatchlist']);
      },
    },
  );

export const removeFromWatchList = (
  onSuccess = () => {},
  onSettled = () => {},
) =>
  useMutation(
    (data) =>
      makeApiDeleteCall({
        url: FAV_API_URLS.REMOVE_SECURITY_FRM_WATCHLIST(
          data.watchlist_id,
          data.companyDetails.id,
        ),
        headers: getGenericAppHeaders(),
      }),
    {
      onMutate: async (data) => {
        await queryClient.cancelQueries(['allWatchlist']);
        const previousValue = queryClient.getQueryData(['allWatchlist']);

        queryClient.setQueryData(['allWatchlist'], (old) => ({
          ...old,
          watchlists: old.watchlists.map((watchlist) => {
            if (watchlist.id === data.watchlist_id) {
              return {
                ...watchlist,
                security_count: watchlist.security_count - 1,
                securities: watchlist.securities.filter(
                  (securitie) => securitie.id !== data.companyDetails.id,
                ),
              };
            }
            return watchlist;
          }),
        }));

        onSuccess(data.companyDetails.name, data.watchlistName);

        return previousValue;
      },

      onError: (err, variables, previousValue) => {
        AxiosErrorHandler(err, false, false, false, true);
        queryClient.setQueryData(['allWatchlist'], previousValue);
      },

      onSettled: () => {
        queryClient.invalidateQueries(['allWatchlist']);
        onSettled();
      },
    },
  );

export const useGetAllWatchList = (isEnabled) =>
  useQuery(['allWatchlist'], () => getAllWatchlist(), {
    staleTime: Infinity,
    enabled: isEnabled,
  });
