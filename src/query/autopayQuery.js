import { useApiRequest } from '@paytm-money/utils-frontend/customHooks';
import { useAxiosClient } from '@paytm-money/utils-frontend/providers';

import { AUTO_PAY_API_URLS } from '../config/urlConfig';
import { getGenericAppHeaders } from '../utils/apiUtil';
import { getPaymentOptionHeaders } from '../config/envConfig';

export const useMandateRegOption = amount => {
  const axiosClient = useAxiosClient();
  const { useGet } = useApiRequest(axiosClient);
  const reactQueryKey = ['bankAccounts', amount];
  return useGet(
    [reactQueryKey],
    AUTO_PAY_API_URLS.MANDATE_REG_OPTIONS(amount),
    { headers: { ...getPaymentOptionHeaders(), ...getGenericAppHeaders() } },
    { cacheTime: 0 },
  );
};

