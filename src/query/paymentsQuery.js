/* eslint-disable no-undef */
import { useApiRequest } from '@paytm-money/utils-frontend/customHooks';
import { useAxiosClient } from '@paytm-money/utils-frontend/providers';

import { useAppStore } from '../contexts/AppContextProvider';
import { PAYMENT_API_URLS } from '../config/urlConfig';
import { PAYMENTS_CONFIG_URL } from '../config/envConfig';
import { PAYMENTS_CONFIG } from '../config/paymentConfig';
import { emptyObj } from '../utils/commonUtil';
import { getUserId } from '../utils/coreUtil';

import { useBbcApiHeaders } from '../utils/apiUtil';

export const useGetUpiAppsIos = (isEnabled) => {
  const axiosClient = useAxiosClient();
  const { useGet } = useApiRequest(axiosClient);

  const reactQueryKey = ['upi-apps-ios'];
  const url = PAYMENT_API_URLS.FETCH_UPI_APPS_IOS;

  return useGet(reactQueryKey, url, {}, { enabled: isEnabled }, (data) =>
    data.data.map((app) => {
      if (app.name === 'Paytm')
        return {
          ...app,
          iconUrl: app.iconUrlPML || app.iconUrl,
          name: 'PayTM',
        };
      return {
        ...app,
        iconUrl: app.iconUrlPML || app.iconUrl,
      };
    }),
  );
};

export const usePaymentsConfig = () => {
  const axiosClient = useAxiosClient();
  const { useGet } = useApiRequest(axiosClient);

  const reactQueryKey = ['payments-config'];
  const url = PAYMENTS_CONFIG_URL.GET_CONFIG;

  const data = useGet(reactQueryKey, url, {}, {});
  if (data.isLoading || data.isError) {
    return { data: PAYMENTS_CONFIG, isLoading: false, isError: false };
  }
  return data;
};

export const useValidateVpa = (onSuccess, onError, isV2flow) => {
  const { store } = useAppStore() || emptyObj;
  const { apiHeader } = store || emptyObj;
  const { bbcApiHeaders } = useBbcApiHeaders();
  const axiosClient = useAxiosClient();
  const { usePost } = useApiRequest(axiosClient);
  const userId = getUserId();
  return usePost(
    ['validate-vpa', userId],
    PAYMENT_API_URLS.VALIDATE_VPA,
    {
      headers: isV2flow ? bbcApiHeaders : apiHeader,
    },
    {
      onSuccess,
      onError,
    },
  );
};

export const useGetPPBLBalance = () => {
  const axiosClient = useAxiosClient();
  const { usePost } = useApiRequest(axiosClient);
  const userId = getUserId();
  return usePost(
    ['balance-info', userId],
    PAYMENT_API_URLS.BALANCE_INFO,
    {},
    { enabled: false },
  );
};
