/* eslint-disable default-param-last */
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { makeApiGetCall, getGenericAppHeaders } from '../utils/apiUtil';
import { getUserId } from '../utils/coreUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';
import {
  ORDER_VERIFICATION_SIKKIM,
  COMPANY_DETAILS_API,
  GENERIC_API_URL,
  MTF_API_URL,
} from '../config/urlConfig';

export const useOrderVerificationSikkimUser = (enabled) =>
  useQuery(
    ['order-verification-sikkim'],
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url: ORDER_VERIFICATION_SIKKIM.GET_ADDRESS(getUserId()),
          headers: getGenericAppHeaders(),
        });
        return data;
      } catch (e) {
        AxiosErrorHandler(e, false, false, false, true);
        return e.response.data;
      }
    },
    {
      enabled,
      staleTime: Infinity,
    },
  );

export const useCompanyDetails = (
  companyId,
  fullPageError = false,
  isTimeout = false,
  isEnabled,
) => {
  const queryKey = ['companyDetails', companyId];
  return useQuery(
    queryKey,
    async () => {
      try {
        const response = await makeApiGetCall({
          url: COMPANY_DETAILS_API.GET_COMPANY_DETAILS(companyId),
          headers: getGenericAppHeaders(),
          isTimeout,
        });
        return response.data;
      } catch (error) {
        AxiosErrorHandler(
          error,
          fullPageError,
          fullPageError ? { type: 'query', queryKey } : false,
          true,
        );
        return error;
      }
    },
    { staleTime: Infinity, enabled: isEnabled },
  );
};

export const useEquityInfoCard = () => {
  const queryKey = ['equityInfoCard'];
  return useQuery(
    queryKey,
    async () => {
      try {
        const response = await makeApiGetCall({
          url: GENERIC_API_URL.EQUITY_INFO_CARD,
          headers: getGenericAppHeaders(),
          isTimeout: true,
        });
        return response.data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
        return error;
      }
    },
    { staleTime: Infinity },
  );
};

export const useNudgeInfo = ({ securityId, segment, exchange, isin }) => {
  const queryKey = ['nudgeInfo', securityId];
  return useQuery(
    queryKey,
    async () => {
      try {
        const response = await makeApiGetCall({
          url: COMPANY_DETAILS_API.GET_NUDGE_INFO(
            securityId,
            segment,
            exchange,
            isin,
          ),
          headers: getGenericAppHeaders(),
          isTimeout: true,
        });
        return response.data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
        return error;
      }
    },
    { staleTime: Infinity, enabled: !!segment },
  );
};

export const useMfHomePageListing = (type, page, pageSize, enabled = true) =>
  useInfiniteQuery(
    ['mf-home-page-sips', type, page, pageSize],
    async () => {
      const queryParams = {
        type,
        pageNumber: page || 1,
        pageSize: pageSize || 5,
      };

      const response = await makeApiGetCall({
        url: GENERIC_API_URL.GET_MF_SIP_LISTING,
        headers: getGenericAppHeaders(),
        queryParams,
        isTimeout: true,
      });
      return response.data.data.results;
    },
    {
      enabled,
      getNextPageParam: (lastPage, allPages) => {
        const maxPages = lastPage.total_count / 5;
        const nextPage = allPages.length;
        return nextPage <= maxPages ? nextPage : undefined;
      },
    },
  );

export const useGetMarginPerc = ({ query, isEnabled }) =>
  useQuery(
    ['mtf-margin'],
    async () => {
      const response = await makeApiGetCall({
        url: MTF_API_URL.MTF_SCRIP,
        headers: getGenericAppHeaders(),
        queryParams: query,
        isTimeout: true,
      });
      return response?.data?.data;
    },
    {
      isEnabled,
    },
  );
