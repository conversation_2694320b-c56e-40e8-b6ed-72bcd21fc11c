import { Subject } from 'rxjs';
import { throttleTime } from 'rxjs/operators';
import { useState, useEffect, useRef, useCallback } from 'react';
import { getBridge } from './bridgeUtils';
import { isPhoenixContainer } from './coreUtil';

export const AppEvents = {
  subscribe: (callBackFn) => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.subscribe('resume', callBackFn);
        bridgeName.subscribe('pause', callBackFn);
      }
    }
  },
  unSubscribe: (callBackFn) => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.subscribe('resume', callBackFn);
        bridgeName.subscribe('pause', callBackFn);
      }
    }
  },
};

export const useNativeDocumentHide = () => {
  const pauseCallbacks = useRef([]);
  const resumeCallbacks = useRef([]);

  const pushPauseCallbackEvent = (cb) => {
    pauseCallbacks.current.push(cb);
  };

  const pushResumeCallbackEvent = (cb) => {
    resumeCallbacks.current.push(cb);
  };

  function executeEvents(events) {
    events.forEach((event) => {
      event();
    });
  }

  function appEvents(event) {
    const { data } = event;
    if (data?.success) {
      switch (data?.event) {
        case 'pause':
          executeEvents(pauseCallbacks.current);
          break;
        case 'resume':
          executeEvents(resumeCallbacks.current);
          break;
        default:
          break;
      }
    }
  }

  useEffect(() => {
    AppEvents.subscribe(appEvents);
    return () => {
      AppEvents.unSubscribe(appEvents);
    };
  }, []);

  return {
    pushPauseCallbackEvent,
    pushResumeCallbackEvent,
  };
};

function useDidUpdateEffect(fn, inputs) {
  const didMountRef = useRef(false);
  const memoizedFn = useCallback(fn, inputs);
  useEffect(() => {
    if (didMountRef.current) memoizedFn();
    else didMountRef.current = true;
  }, [memoizedFn]);
}

const useImageLoader = ({ imgSrc }) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  useEffect(() => {
    const img = new Image();
    img.src = imgSrc;
    img.onload = () => {
      setIsImageLoaded(true);
    };
  }, []);
  return {
    image: imgSrc,
    isImageLoaded,
  };
};

function useCallbackForEvents(fn, input) {
  const callBackRef = useRef(fn);
  useDidUpdateEffect(() => {
    callBackRef.current = fn;
  }, input);
  return useCallback((...args) => callBackRef.current(...args), []);
}

export const orderUpdateSubject = new Subject();
export const reconnectSubject = new Subject();

export function useReconnect(fxn) {
  useEffect(() => {
    const subscription = reconnectSubject
      .pipe(throttleTime(1000))
      .subscribe(fxn);
    return () => subscription.unsubscribe();
  }, [fxn]);
}
export { useImageLoader, useCallbackForEvents, useDidUpdateEffect };
