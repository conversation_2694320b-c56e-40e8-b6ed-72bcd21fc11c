import queryString from 'query-string';
import { errorLog } from './commonUtil';
import {
  openInBrowser,
  openDeepLinkPaytm,
  openDeepLinkPaytmMoney,
} from './bridgeUtils';
import { isPaytmMoney } from './coreUtil';

const HOST = 'https://www.paytmmoney.com/';

export const revampDeeplinkHelper = (deepLinkUrl, navigate) => {
  if (!deepLinkUrl) {
    errorLog('No deeplink url found');
    return;
  }
  if (
    deepLinkUrl.startsWith(HOST) ||
    deepLinkUrl.startsWith('https://paytmmoney')
  ) {
    openInBrowser(deepLinkUrl);
  } else if (deepLinkUrl.startsWith('paytmmp://mini-app')) {
    openDeepLinkPaytm(deepLinkUrl);
  } else {
    const trimmedDeepLinkUrl = deepLinkUrl.replace(
      'paytmmp://paytmmoney/stocks',
      '',
    );
    navigate(trimmedDeepLinkUrl);
  }
};

export const callPaymentFlow = ({
  amount = '',
  path = 'add-funds',
  queryParams = {},
} = {}) => {
  let params = `?stsBarHt=true&origin=PAYTM`;
  params = `${params}&amount=${amount}`;
  const queryParamsStr = queryString.stringify(queryParams);
  if (queryParamsStr) {
    params = `${params}&${queryParamsStr}`;
  }
  const deepLinkData = {
    params,
    path: `/mini-app/payments/${path}`,
    sparams: {
      showTitleBar: false,
      canPullDown: false,
      isCancellable: false,
    },
  };

  const base64 = btoa(JSON.stringify(deepLinkData));
  revampDeeplinkHelper(
    `paytmmp://mini-app?aId=8342ec35254f47dd99f057698ebb2aaf&data=${base64}`,
  );
};

export const navigateToAddFunds = ({ amount = '' } = {}) => {
  const pmlDeeplink = `paytmmoney:///mini-app?aId=3c5f71ba5bfd42e19cf1c2cae6345780&pageName=payments/add-funds?amount=${amount}`;

  if (isPaytmMoney()) {
    openDeepLinkPaytmMoney(pmlDeeplink);
  } else {
    callPaymentFlow({ amount });
  }
};

export const navigateToPaymentRedirect = ({ params = {}, isPml = false }) => {
  const encodedParams = btoa(JSON.stringify(params)); // base64 encode
  const paymentRedirectDeeplink = `paytmmoney:///mini-app?aId=3c5f71ba5bfd42e19cf1c2cae6345780&pageName=payment-redirect?data=${encodedParams}`;

  console.log('paymentRedirectDeeplink ::: ', paymentRedirectDeeplink);

  if (isPml || isPaytmMoney()) {
    openDeepLinkPaytmMoney(paymentRedirectDeeplink);
  }
};

export const callPaymentStatusFlow = ({
  transactionId = '',
  prevPage = 'nativeQnr',
  path = 'transaction-status',
  queryParams = {},
} = {}) => {
  let params = `?stsBarHt=true&origin=PAYTM`;
  params = `${params}&transactionId=${transactionId}&prevPage=${prevPage}`;
  const queryParamsStr = queryString.stringify(queryParams);
  if (queryParamsStr) {
    params = `${params}&${queryParamsStr}`;
  }
  const deepLinkData = {
    params,
    path: `/mini-app/payments/${path}`,
    sparams: {
      showTitleBar: false,
      canPullDown: false,
      isCancellable: false,
    },
  };

  const base64 = btoa(JSON.stringify(deepLinkData));
  revampDeeplinkHelper(
    `paytmmp://mini-app?aId=8342ec35254f47dd99f057698ebb2aaf&data=${base64}`,
  );
};

export const navigateToTxnStatus = ({
  transactionId = '',
  prevPage = 'combined-dashboard',
} = {}) => {
  const params = `?transactionId=${transactionId}&prevPage=${prevPage}`;
  const pmlDeeplink = `paytmmoney:///mini-app?aId=3c5f71ba5bfd42e19cf1c2cae6345780&pageName=payments/transaction-status${params}`;

  if (isPaytmMoney()) {
    openDeepLinkPaytmMoney(pmlDeeplink);
  } else {
    callPaymentStatusFlow({ transactionId, prevPage });
  }
};

export const callOrderPadLiteDrawer = (routeName = '') => {
  openDeepLinkPaytmMoney(
    `paytmmoney:///mini-app?aId=a3f92b7c0cba471f853172b02d1c8a4f&pageName=${routeName}`,
  );
};
