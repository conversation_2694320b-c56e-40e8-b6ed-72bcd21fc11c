import { useEffect } from 'react';
import { getPlatformValue } from './apiUtil';
import { getSSOToken, getUserId } from './coreUtil';

// import from config based on environment
const SIGNALS_CONFIG = {
  environment: process.env.SIGNAL_ENV,
  hmacKey: process.env.hmacKey,
  SIG_URL: process.env.SIG_URL,
};

const platform = getPlatformValue();

function useSignal() {
  window.pmEnvironment = SIGNALS_CONFIG.environment;
  window.pmSignalsConfigEnv = SIGNALS_CONFIG.environment;
  window.pmSignalsConfigKey = SIGNALS_CONFIG.hmacKey;

  useEffect(() => {
    const script = document.createElement('script');

    script.src =
      'https://webappsstatic.paytm.com/signalsdk-web/v1/config-2.2.2.min.js';
    script.async = true;

    document.body.appendChild(script);

    window.paytm = window.paytm || { aQ: [], analytics: {} };
    window.paytm.analytics.addEvent =
      window.paytm.analytics.addEvent ||
      // eslint-disable-next-line func-names
      function(e) {
        window.paytm.aQ.push(e);
      };

    const handler = () => {
      // eslint-disable-next-line no-unused-expressions
      window.paytm.analytics.configureAnalyticsSDK &&
        window.paytm.analytics.configureAnalyticsSDK(
          platform,
          window.pmSignalsConfigEnv,
          {
            xRequester: 'pml-mini-app',
            hmacKey: SIGNALS_CONFIG.hmacKey,
            realtime: false,
            apiRoute: SIGNALS_CONFIG.SIG_URL,
          },
        );
      const updateSDKValues = window?.paytm?.analytics?.updateSDKValues;
      if (updateSDKValues) {
        const id = getUserId();
        const token = getSSOToken();
        window.paytm.analytics.updateSDKValues(id, token);
      }
    };
    try {
      window.addEventListener('sdk-config-loaded', handler);
    } catch (e) {
      console.log(e);
    }
    return () => {
      window.removeEventListener('sdk-config-loaded', handler);
      document.body.removeChild(script);
    };
  }, []);
}

export default useSignal;
