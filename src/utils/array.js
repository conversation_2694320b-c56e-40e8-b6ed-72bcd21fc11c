export function getObjectInArray(source, propertyName, propertyValue) {
  if (!source) return undefined;
  return source.find((obj) => obj[propertyName] === propertyValue);
}

export function enrichArrayObjectsWithLookupData(
  array,
  enrichments,
  lookupKey,
) {
  function findWithComparator(source, item, callback) {
    if (!source) return undefined;
    return source.find((obj) => callback(obj, item));
  }
  return array.map((item) => {
    const enrichedFields = enrichments.reduce(
      (acc, { source, fieldName, targetKey, extractor, comparator }) => {
        const lookupValue = item?.[lookupKey];
        const matchedObj =
          typeof comparator === 'function'
            ? findWithComparator(source, item, comparator)
            : getObjectInArray(source, fieldName, lookupValue);
        acc[targetKey] =
          typeof extractor === 'function'
            ? extractor(matchedObj)
            : { ...matchedObj };
        return acc;
      },
      {},
    );

    return { ...item, ...enrichedFields };
  });
}
