/* eslint-disable import/no-cycle */
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import { getPlatformValue, isEquityMiniApp } from './apiUtil';

import { isBridge, exitApp, isH5, getBridge } from './bridgeUtils';
import { getOrigin, getSource, isIosBuild, log } from './commonUtil';
import { PULSE_EVENTS, WIDGET_DEEPLINK_SOURCE } from './constants';

export function getH5NativeDeepLinkData() {
  log('## getH5NativeDeepLinkData: ');
  if (isBridge()) {
    return DeviceInfoProvider.getInfo('H5NativeDeeplinkData');
  }
  return '';
}

export function setH5NativeDeepLink(deepLinkUrl) {
  log('## setH5NativeDeepLink: ');
  if (isBridge()) {
    log('## setH5NativeDeepLink: ', deepLinkUrl);
    return DeviceInfoProvider.setInfo('H5NativeDeeplinkData', deepLinkUrl);
  }
  return '';
}

export function resetH5NativeDeepLinkData() {
  log('## resetH5NativeDeepLinkData: ');
  if (isBridge()) {
    log('## resetH5NativeDeepLinkData: - isBridge');
    return DeviceInfoProvider.setInfo('H5NativeDeeplinkData', '');
  }
  return '';
}

export function setJsBundleVersion(version) {
  DeviceInfoProvider.setInfo('ver', version ? parseInt(version, 10) : 1);
}

export function getNormalSSOToken() {
  return DeviceInfoProvider.getInfo('sso_token');
}

export function getUserId() {
  return DeviceInfoProvider.getInfo('userId');
}

export function getOsVersion() {
  return DeviceInfoProvider.getInfo('osVersion');
}

export function getDeviceName() {
  return DeviceInfoProvider.getInfo('deviceName');
}

export function getAppVersion() {
  return DeviceInfoProvider.getInfo('appVersionName');
}

export function getAppVersionCode() {
  return DeviceInfoProvider.getInfo('appVersionCode');
}

export function getDeviceID() {
  return DeviceInfoProvider.getInfo('deviceId');
}

export function getDeviceType() {
  return DeviceInfoProvider.getInfo('device_type');
}

export function getDeviceWebviewVersion() {
  return DeviceInfoProvider.getInfo('webviewVersion');
}

export function getPaytmUserInfo() {
  return DeviceInfoProvider.getInfo('paytmUserInfo');
}

export function getMiniAppSource() {
  return DeviceInfoProvider.getInfo('miniAppSource');
}

export function setMiniAppSource(miniAppSource) {
  return DeviceInfoProvider.setInfo('miniAppSource', miniAppSource);
}

export function setDarkModeValue(isDarkMode) {
  DeviceInfoProvider.setInfo('darkmode', isDarkMode);
}

export function getTwoFAtoken() {
  return DeviceInfoProvider.getInfo('twoFAToken');
}

export function getEncSSOToken() {
  return DeviceInfoProvider.getInfo('sso_token_enc');
}

export function getSSOToken() {
  if (isBridge()) {
    return getNormalSSOToken();
  }
  // eslint-disable-next-line no-undef
  if (__ENV__ === 'production') {
    return getEncSSOToken();
  }
  return getNormalSSOToken();
}

const getUTMPayload = () => {
  const utmPayload = {};

  const utmTerm = sessionStorage.getItem('utm_term');
  const utmCampaign = sessionStorage.getItem('utm_campaign');
  const utmContent = sessionStorage.getItem('utm_content');
  const utmSource = sessionStorage.getItem('utm_source');
  const utmMedium = sessionStorage.getItem('utm_medium');

  if (utmTerm) {
    utmPayload.utm_term = utmTerm;
  }
  if (utmCampaign) {
    utmPayload.utm_campaign = utmCampaign;
  }
  if (utmContent) {
    utmPayload.utm_content = utmContent;
  }
  if (utmSource) {
    utmPayload.utm_source = utmSource;
  }
  if (utmMedium) {
    utmPayload.utm_medium = utmMedium;
  }

  return utmPayload;
};

export const isPhoenixContainer = () => {
  const ua = window.navigator.userAgent;
  return /PhoenixContainer/i.test(ua);
};

export const isPaytmMoney = () => getOrigin() === 'PAYTMMONEY';

export const isSourceMFH5 = () => getSource() === WIDGET_DEEPLINK_SOURCE;

export const setIsConsentOverlayShown = (value) => {
  localStorage.setItem('isConsentOverlayShown', value);
};

export const goBack = (navigate) => {
  // if (window.location.pathname === '/') {
  console.log('exitApp() - 2')
  exitApp();
  // } else navigate(-1);
};

export const isH5Container = () => {
  if (typeof window !== 'undefined') {
    const ua = window.navigator.userAgent;
    return /AppContainer/i.test(ua);
  }
};

export const sendEventToBridge = (analyticsDataMap) => {
  try {
    if (isEquityMiniApp() || !isH5()) {
      if (
        window.paytm &&
        window.paytm.analytics &&
        window.paytm.analytics.addEvent
      ) {
        const { data, eventName: event, ...payload } = analyticsDataMap;
        const analyticsPayload = {
          event,
          platform: getPlatformValue(),
          ...data,
          ...payload,
        };
        window.paytm.analytics.addEvent(analyticsPayload);
      }
    } else if (typeof window.JSBridge !== 'undefined') {
      const bridgeName = getBridge();
      console.log('analyticsDataMap', analyticsDataMap);
      bridgeName.call('paytmAnalyticsTracking', analyticsDataMap, () => {
        // Logger(`Analytics:: Analytics Bridge result - ${result}`);
      });
    }
  } catch (error) {
    // eslint-disable-next-line no-console
  }
};

export function sendAnalyticsEventFirstCard({
  verticalName,
  screenName,
  category,
  event,
  action,
  label,
  label2 = '',
  label3 = '',
  label6 = '',
}) {
  const utmPayload = getUTMPayload();

  const map = {
    eventName: 'custom_event',
    screenName,
    data: {
      vertical_name: verticalName || PULSE_EVENTS.verticalName,
      event_category: category,
      event_action: action,
      event,
      event_label: label,
      event_label3: label3,
      event_label2: label2,
      event_label6: label6,
      ...utmPayload,
    },
  };

  sendEventToBridge(map);
}

export function sendAnalyticsEventFirstStock({
  App = 'Main',
  Priority = 'P0',
  verticalName,
  screenName,
  category,
  event,
  action,
  label,
  label2 = '',
  label3 = '',
  label4 = '',
  label6 = '',
}) {
  const utmPayload = getUTMPayload();

  const map = {
    eventName: 'custom_event',
    screenName,
    data: {
      App,
      Priority,
      vertical_name: verticalName || PULSE_EVENTS.verticalName,
      event_category: category,
      event_action: action,
      event,
      event_label: label,
      event_label2: label2,
      event_label3: label3,
      event_label4: label4,
      event_label6: label6,
      ...utmPayload,
    },
  };
  console.log('map', map);
  sendEventToBridge(map);
}

export function sendAnalyticsEventDailySIP({
  App = isPaytmMoney() ? 'Main' : 'Mini App',
  verticalName,
  screenName,
  category,
  event = 'custom_event',
  action,
  label,
  label2 = '',
  label3 = '',
  label4 = '',
  label5 = '',
  label6 = '',
}) {
  const utmPayload = getUTMPayload();

  const map = {
    eventName: 'custom_event',
    screenName,
    data: {
      App,
      vertical_name: verticalName || PULSE_EVENTS.verticalName,
      event_category: category,
      event_action: action,
      event,
      event_label: label,
      event_label2: label2,
      event_label3: label3,
      event_label4: label4,
      event_label5: label5,
      event_label6: label6,
      ...utmPayload,
    },
  };
  console.log('map', map);
  sendEventToBridge(map);
}

export function sendAnalyticsEventFirstTradeProgressCard(
  verticalName,
  screenName,
  category,
  action,
  event,
  label = '',
  label2 = '',
  label3 = '',
  label4 = '',
  label5 = '',
  label6 = '',
) {
  const utmPayload = getUTMPayload();
  const map = {
    eventName: 'custom_event',
    screenName,
    data: {
      vertical_name: verticalName || PULSE_EVENTS.verticalName,
      event_category: category,
      event_action: action,
      event,
      event_label: label,
      event_label2: label2,
      event_label3: label3,
      event_label4: label4,
      event_label5: label5,
      event_label6: label6,
      user_id: getUserId(),
      ...utmPayload,
    },
  };
  sendEventToBridge(map);
}

export function sendAnalyticsEvent(
  verticalName,
  screenName,
  category,
  action,
  label1 = '',
  label2 = '',
  label3 = '',
) {
  const utmPayload = getUTMPayload();

  const map = {
    eventName: 'custom_event',
    screenName,
    data: {
      vertical_name: verticalName || PULSE_EVENTS.verticalName,
      event_category: category,
      event_action: action,
      event_label: `H5_${DeviceInfoProvider.getInfo('device_type')}`,
      event_label3: label3 || label2,
      event_label2: label1,
      ...utmPayload,
    },
  };
  if (label3) {
    map.data.event_label4 = label2;
  }

  sendEventToBridge(map);
}

export function sendAnalyticsEventWidget({
  App = isPaytmMoney() ? 'Main' : 'Mini App',
  verticalName,
  screenName,
  category,
  event = 'custom_event',
  action,
  label = '',
  label2 = '',
  label3 = '',
  label4 = '',
  label5 = '',
  label6 = '',
}) {
  const utmPayload = getUTMPayload();
  const map = {
    eventName: 'custom_event',
    screenName,
    data: {
      App,
      vertical_name: verticalName || PULSE_EVENTS.verticalName,
      event_category: category,
      event_action: action,
      event,
      event_label: label,
      event_label2: label2,
      event_label3: label3,
      event_label4: label4,
      event_label5: label5,
      event_label6: label6,
      ...utmPayload,
    },
  };
  console.log('map', map);
  sendEventToBridge(map);
}

export function sendAnalyticsEventOrderPlacedWidget({
  App = isPaytmMoney() ? 'Main' : 'Mini',
  verticalName,
  screenName,
  category,
  event,
  action,
  label = '',
  label2 = '',
  label3 = '',
  label4 = '',
  label5 = '',
  label6 = '',
}) {
  const utmPayload = getUTMPayload();
  const map = {
    eventName: event,
    screenName,
    data: {
      App,
      vertical_name: verticalName,
      event_category: category,
      event_action: action,
      event,
      event_label: label,
      event_label2: label2,
      event_label3: label3,
      event_label4: label4,
      event_label5: label5,
      event_label6: label6,
      ...utmPayload,
    },
  };
  sendEventToBridge(map);
}

export const generateLabel = (obj) =>
  Object.values(obj)
    .reduce((acc, val, index, arr) => {
      let str = acc;
      if (val) {
        if (index === arr.length - 1) {
          str = `${str} ${val}`;
        } else {
          str = `${str} ${val} |`;
        }
      }
      return str;
    }, '')
    ?.trim();

export const getPlatformType = () => {
  if (isH5()) {
    if (isIosBuild()) {
      return 'iOS';
    }
    return 'Android';
  }
  return 'mweb';
};
