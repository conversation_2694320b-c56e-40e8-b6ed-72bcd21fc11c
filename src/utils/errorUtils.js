/* eslint-disable no-undef */
import get from 'lodash/get';

// import { reLogin } from './bridgeUtils';
// import {
//   setLoaderView,
//   setRootError,
//   setRootErrorScreenView,
//   setConnectionStatus,
// } from '../actions/genericActions';
import { sendErrorToBackend } from '../actions/runtime';
import { API_ERROR_CODES } from './constants';
// import { NO_INTERNET } from './Constants';

const isProd = __BUILD__ === 'prod';

export const getError = (error) =>
  error?.meta?.displayMessage ||
  error?.meta?.message ||
  error?.response?.data?.meta?.displayMessage ||
  error?.response?.data?.meta?.message ||
  error?.data?.meta?.displayMessage ||
  error?.displayMessage ||
  error?.message;

export const getErrorMetaCode = (error) =>
  error?.meta?.code ||
  error?.response?.data?.meta?.code ||
  error?.data?.meta?.code ||
  error?.code;

export const mapErrorCode = (error) => {
  if (error) {
    for (const key in error) {
      if (API_ERROR_CODES[key]) {
        return error[key];
      }
    }
  }
  return null;
};

export const getErrorCode = (error) => {
  const errorMeta =
    error?.meta || error?.response?.data?.meta || error?.data?.meta;
  return mapErrorCode(errorMeta);
};

/**
 * Axios error handler method
 * @param error
 * @param fullPageError - set `true` to show full page error screen
 * @param retryCallback - function that should be called on refresh click. Always pass when `fullPageError` is `true`. For `react-query` retry, pass {type: 'query', queryKey: key}
 * @param displaySelf - when set `true` AxiosErrorHandler will not show any error message
 * @param inlineErr - throw error back
 */

export const AxiosErrorHandler = (
  error,
  fullPageError = false,
  retryCallback = false,
  displaySelf = false,
  inlineErr = false,
  maskedUrl = '',
) => {
  // setLoaderView(false);
  let status = null;
  let errorData;
  if (error.response) {
    status = error.response?.status;
    errorData = error.response?.data;
    if (errorData === '') {
      errorData = {
        message: 'Something went wrong.',
      };
    }
  } else {
    errorData = JSON.parse(JSON.stringify(error));
  }

  if (isProd) {
    let errObj = {};
    if (error.response) {
      // Axios API Error Response
      errObj = {
        status: error.response?.status,
        data: error.response?.data,
        url: maskedUrl || error.response?.config?.url,
        method: error.response?.config?.method,
      };
    } else if (error.message || error.config) {
      // Network Error Case
      errObj = {
        message: error.message,
        url: maskedUrl || error.config?.url,
        timeout: error.config?.timeout,
        method: error.config?.method,
      };
    }
    sendErrorToBackend({
      data: JSON.stringify(errObj),
    });
  }

  switch (status) {
    case 0:
    case 401:
    // from promocode applied without login errorCode is Authorisation error
    // eslint-disable-next-line no-fallthrough
    case 499:
      // reLogin();
      break;
    case 502:
    case 503:
    case 400:
    case 412:
    case 422:
      if (inlineErr) {
        throw errorData;
      } else if (fullPageError) {
        // setRootErrorScreenView({
        //   showErrorScreen: true,
        //   message: getError(error),
        //   errorCode: getErrorCode(error),
        //   retryCallback,
        // });
      } else if (!displaySelf) {
        // setRootError(errorData);
      }
      throw errorData;
    case 504:
      if (inlineErr) {
        throw new SyntaxError('Time Out');
      } else if (!displaySelf) {
        // setRootError({ message: 'Time Out' });
      }
      throw new SyntaxError('Time Out');
    default:
      if (!get(window, 'navigator.onLine')) {
        // setConnectionStatus(CONNECTION_MODE.OFFLINE);
      } else if (inlineErr) {
        throw errorData;
      } else if (fullPageError) {
        // Will be showing default error message in case of API timeout
        // const message = error.code === 'ECONNABORTED' ? null : getError(error);
        // setRootErrorScreenView({
        //   showErrorScreen: true,
        //   message,
        //   errorCode: getErrorCode(error),
        //   retryCallback,
        // });
      } else if (!displaySelf) {
        // setRootErrorScreenView({
        //   showErrorScreen: false,
        // });
        // setRootError(errorData);
      }
      throw errorData;
  }
};

export const chunkLoadErrorHandler = () => {
  window.location.reload();
};

export const ERROR_CODES = {
  CHUNK_LOAD: {
    NAME: 'ChunkLoadError',
    CODE: 'CSS_CHUNK_LOAD_FAILED',
  },
};
