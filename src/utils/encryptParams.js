async function importKey(base64Key) {
  const raw = Uint8Array.from(atob(base64Key), (c) => c.charCodeAt(0));
  return crypto.subtle.importKey('raw', raw, { name: 'AES-GCM' }, false, [
    'encrypt',
  ]);
}

export async function encryptParams(params, base64Key) {
  const key = await importKey(base64Key);
  const iv = crypto.getRandomValues(new Uint8Array(12)); // 96-bit IV

  const encoded = new TextEncoder().encode(JSON.stringify(params));
  const cipherText = await crypto.subtle.encrypt(
    { name: 'AES-GCM', iv },
    key,
    encoded,
  );

  const encryptedData = new Uint8Array(cipherText);
  const combined = new Uint8Array(iv.length + encryptedData.length);
  combined.set(iv);
  combined.set(encryptedData, iv.length);

  return btoa(String.fromCharCode(...combined));
}
