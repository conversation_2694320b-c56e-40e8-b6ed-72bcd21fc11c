import { STATUS, MAIN_BUCKET_KEYS, IR_STATUS_ENUM } from './constants';
import {
  useUserReadiness,
  useIrData,
  useAccountModificationStatus,
} from '../query/generalQuery';

const getListForHeader = (status, key) => {
  if (
    status === STATUS.DEFAULT ||
    status === STATUS.PENDING ||
    status === STATUS.REJECTED
  ) {
    return key;
  }
  return true;
};

const generateKeyValuePair = (data) =>
  data.reduce(
    (acc, step) => ({
      ...acc,
      order: acc.order.concat(step.code),
      statusList: {
        ...acc.statusList,
        [step.code]: step?.status?.toUpperCase(),
      }, // list of status of each bucket
      bucketFilledList: acc.bucketFilledList.concat(
        getListForHeader(step?.status?.toUpperCase(), step.code),
      ),
      [step.code]: {
        ...step,
        status: step?.status?.toUpperCase(),
        steps: step.steps ? generateKeyValuePair(step.steps) : null,
      },
    }),
    {
      order: [],
      statusList: {},
      bucketFilledList: [],
    },
  );

const normalizeData = (data) => {
  const corePersonalBucketName = data.buckets.find(
    (item) =>
      item.code === MAIN_BUCKET_KEYS.EQ_KYC_PERSONAL ||
      item.code === MAIN_BUCKET_KEYS.EQ_KYC,
  )?.code;
  return {
    ...data,
    buckets: generateKeyValuePair(data.buckets || []),
    isPaymentPending: data?.isPaymentPending,
    isCrossOnboarding: data?.isCrossOnboarding,
    corePersonalBucketName,
  };
};

const getUpdatedIrStatus = (irStatus) => {
  if (
    [
      IR_STATUS_ENUM.REVOKED,
      IR_STATUS_ENUM.DORMANT_REVOKED,
      IR_STATUS_ENUM.DORMANCY_IN_PROGRESS,
      IR_STATUS_ENUM.REKYC_IN_PROGRESS,
    ].includes(irStatus)
  ) {
    return IR_STATUS_ENUM.REVOKED;
  }
  return irStatus;
};

const useIRApi = (isEnabled = true) => {
  const {
    data: readinessData,
    isLoading: isUserReadinessDataLoading,
    refetch: refetchData,
    isRefetching: isRefetchingIr,
    isError: isUserReadinessError,
    status: userReadinessStatus,
  } = useUserReadiness(isEnabled);

  const {
    data: accountModificationData,
    isLoading: accountModificationLoading,
    refetch: refetchAccountModification,
  } = useAccountModificationStatus(
    !isUserReadinessDataLoading &&
      readinessData?.irStatus === IR_STATUS_ENUM.DORMANT_REVOKED,
  );

  const {
    data: irData,
    isLoading,
    isError,
    refetch,
    isRefetching: isRefetchingJourney,
  } = useIrData(isEnabled);

  const refetchApi = () => {
    if (userReadinessStatus === 'success') {
      refetch();
    }
    refetchData();
  };

  const readinessObj = {
    isLoading: isUserReadinessDataLoading,
    isError: isUserReadinessError,
    refetch: refetchApi,
    isRefetchingIr,
    isRefetchingJourney,
  };

  if (!isUserReadinessDataLoading && !accountModificationLoading) {
    if (
      [
        IR_STATUS_ENUM.ACTIVE,
        IR_STATUS_ENUM.VERIFIED,
        IR_STATUS_ENUM.DORMANCY_IN_PROGRESS,
        IR_STATUS_ENUM.DORMANT_REVOKED,
        IR_STATUS_ENUM.REKYC_IN_PROGRESS,
      ].includes(readinessData?.irStatus)
    ) {
      return {
        irData: readinessData,
        showReKycCta: accountModificationData?.showReKycCta,
        refetchAccountModification,
        accountModificationLoading,
        ...readinessObj,
      };
    }
    if (!isLoading) {
      return {
        irData: irData?.normalizeData,
        showReKycCta: false,
        isLoading,
        isError,
        refetch: refetchApi,
        isRefetchingIr,
        accountModificationLoading,
      };
    }
    return {
      irData: null,
      showReKycCta: false,
      isLoading,
      isError,
      refetch: refetchApi,
      isRefetchingIr,
      accountModificationLoading,
    };
  }
  return {
    irData: {
      isInvestmentReady: null,
    },
    showReKycCta: false,
    accountModificationLoading,
    ...readinessObj,
  };
};

export { normalizeData, getUpdatedIrStatus, useIRApi };
