/* eslint-disable import/no-cycle */
import axios from 'axios';
import { isH5 } from './bridgeUtils';
import { getOrigin, isIosBuild, log, emptyObj } from './commonUtil';
import { API_TIMEOUT } from './constants';
import {
  getAppVersion,
  getDeviceID,
  getDeviceName,
  getDeviceType,
  getDeviceWebviewVersion,
  getMiniAppSource,
  getNormalSSOToken,
  getOsVersion,
  getTwoFAtoken,
  getUserId,
  isH5Container,
} from './coreUtil';

const { CancelToken } = axios;
const source = CancelToken.source();

const PAYTM_ANDROID = 'paytm-android';
const PAYTM_IOS = 'paytm-ios';

export const getPlatformValue = () => {
  const deviceType = getDeviceType();
  if (isH5()) {
    if (getOrigin() === 'PAYTM') {
      return `paytm-${deviceType}`;
    }
    if (getOrigin() === 'IBL') {
      return `ibl-${deviceType}`;
    }
  }
  return deviceType;
};

export const isEquityMiniApp = () => {
  const platform = getPlatformValue();
  log('getPlatformValue', platform);
  return platform === PAYTM_ANDROID || platform === PAYTM_IOS;
};

const parseDeviceToken = () => {
  const deviceId = getDeviceID() || '';
  if (isIosBuild()) return deviceId;
  if (isEquityMiniApp()) return deviceId;
  const splitArray = deviceId.split('-');
  return splitArray[splitArray.length - 1];
};

export const getUserAgent = () => {
  if (isH5()) {
    return {
      platform: getPlatformValue(),
      app_version: getAppVersion(),
      model: getDeviceName(),
      os_version: getOsVersion(),
      user_id: getUserId(),
      source: getOrigin(),
      h5: isH5Container(),
      device_id: parseDeviceToken(),
      webview_version: getDeviceWebviewVersion(),
    };
  }

  return {
    user_id: getUserId(),
    platform: 'android',
    h5: true,
    app_version: '10.12.10',
    device_id: '10cd88cd-c49d-5de8-b34c-dd72ee8679f1',
  };
};

//    device_id: getDeviceID(), for now removed...

export const getGenericAppHeaders = (customSource = '') => {
  log(
    `SSO TOKEN From Generic APP Header Value:::${JSON.stringify(
      getNormalSSOToken(),
    )}`,
  );
  const defaultHeaders = {
    'Content-Type': 'application/json; charset=utf-8',
    // TODO: Uncomment this line when we have the authorization value for paytm.com
    // Authorization: getAuthorizationValue(),
    'x-user-agent': JSON.stringify(getUserAgent(customSource)),
    'x-sso-token': getNormalSSOToken(),
    'x-pmngx-key': 'paytmmoney',
  };

  const twoFAToken = '1vQgJQ1WeXpik6YvEn3f4fxn1M7g5uDrtAC6vbn38yk=';
  if (twoFAToken) {
    defaultHeaders['x-2fa-token'] = twoFAToken;
  }
  log(
    `getGenericAppHeaders getGenericAppHeaders:: Value:::: ${JSON.stringify(
      defaultHeaders,
    )}`,
  );

  return defaultHeaders;
};

export const getManchSDKHeaders = (data) => {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    Authorization: data.authorization,
    'Request-Id': data.requestId,
  };
  return defaultHeaders;
};

export const makeApiGetCall = async ({
  url,
  headers = {},
  axiosSource = source,
  queryParams = {},
  isTimeout = false,
}) => {
  const response = await axios.get(url, {
    cancelToken: axiosSource.token,
    headers,
    params: queryParams,
    data: null,
    timeout: isTimeout ? API_TIMEOUT : 0,
  });
  return response;
};
export const makeApiPostCall = async ({
  url,
  body = {},
  headers = {},
  queryParams = {},
  axiosSource = source,
  isTimeout = false,
}) => {
  const response = await axios.post(url, body, {
    cancelToken: axiosSource.token,
    headers,
    params: queryParams,
    timeout: isTimeout ? API_TIMEOUT : 0,
  });
  return response;
};
export const makeApiPutCall = async ({
  url,
  body = {},
  headers = {},
  queryParams = {},
  axiosSource = source,
  isTimeout = false,
}) => {
  const response = await axios.put(url, body, {
    cancelToken: axiosSource.token,
    headers,
    params: queryParams,
    timeout: isTimeout ? API_TIMEOUT : 0,
  });
  return response;
};

export const makeApiDeleteCall = async ({
  url,
  headers = {},
  queryParams = {},
  axiosSource = source,
  isTimeout = false,
}) => {
  const response = await axios.delete(url, {
    cancelToken: axiosSource.token,
    headers,
    params: queryParams,
    timeout: isTimeout ? API_TIMEOUT : 0,
  });
  return response;
};

export const makeImageWithAuth = async (url, axiosSource) => {
  try {
    const response = await axios.get(url, {
      cancelToken: axiosSource?.token,
      headers: getGenericAppHeaders(),
      responseType: 'blob',
    });
    return URL.createObjectURL(response.data);
  } catch (error) {
    if (!axios.isCancel(error)) {
      return null;
    }
  }
};

export const getPasswordVerifierHeaders = () => {
  const passwordVerifierHeaders = {
    // Authorization: getAuthorizationValue(),
    session_token: getNormalSSOToken(),
  };
  log(`passwordVerifierHeaders.., ${JSON.stringify(passwordVerifierHeaders)}`);
  return passwordVerifierHeaders;
};

export const isMiniApp = () => getMiniAppSource();

export const isV2KycUser = () => localStorage.getItem('kycFlowType') === 'V2';

export const getAutoPayApiAppHeaders = () => ({
  'client-Fe-Code': 'equity-stock-mt-subs',
});

export const getVpaAutoPayApiAppHeaders = () => ({
  'client-Fe-Code': 'mf', // TODO: Made this change to skip initate subscription api call before validate vpa
});

export const useBbcApiHeaders = (source, customFields = {}) => {
  const { store } = emptyObj;
  const { apiHeader } = store || emptyObj;
  if (source && source !== 'bbc') {
    return {};
  }
  const userAgent = JSON.parse(apiHeader?.['x-user-agent'] || '{}');
  const bbcUserAgent = {
    ...userAgent,
    source: 'BBC',
    ...customFields,
  };
  const bbcApiHeaders = {
    ...apiHeader,
    'x-user-agent': JSON.stringify(bbcUserAgent),
  };

  return { bbcApiHeaders };
};
