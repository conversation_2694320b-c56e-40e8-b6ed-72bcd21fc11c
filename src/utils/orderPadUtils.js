const STATUS = {
  ERROR: 'error',
  SUCCESS: 'success',
};

export const GENERIC_ERROR_MESSAGE = 'Something Went Wrong';

export const OMS_ERROR_CODE = {
  INVALID_PRODUCT_TYPE: 'E0002',
  EDIS_AUTH_REQUIRED: 'PM-0029',
  INSUFFICIENT_QTY_HOLDINGS: 'RS-0022',
  GENERIC_ERROR: 'RS-0022',
  INSUFFICIENT_FUNDS: 'E0001',
  STATUS_CHANGED: '3312',
  ORDER_ON_HOLD_ERROR: 'PM-0001',
  INSUFFICIENT_FUNDS_ORDER_HOLD: 'PM-OH-0004',
};

export function handleFundsLite(data) {
  let result = {
    insufficientFunds: false,
  };
  if (
    data?.response?.data?.error_code ===
    OMS_ERROR_CODE.INSUFFICIENT_FUNDS_ORDER_HOLD
  ) {
    result = {
      insufficientFunds: true,
      message: data?.response?.data?.message,
    };
  }
  return result;
}

export const INVALID_POPUP = {
  INVALID_ORDER_HEADER: 'Invalid Order',
  ERROR_HEADER: 'Error',
  CTA: 'OK, Got it',
};

export const LOCAL_STORAGE_KEYS = {
  ORDER_CONFIRMATION_DATA: 'ORDER_CONFIRMATION_DATA',
  ACTIVE_ORDER_PAD: 'ACTIVE_ORDER_PAD',
};

export function orderStatusChanges(data) {
  const { status, message } = data;
  let result = {
    insufficientFunds: false,
  };
  if (
    status === STATUS.ERROR &&
    data?.data?.[0]?.oms_error_code === OMS_ERROR_CODE.STATUS_CHANGED
  ) {
    result = {
      statusChanged: true,
      message,
    };
  }
  return result;
}
