const THEMES = {
  DARK: 'dark',
  LIGHT: 'light',
};

const COOKIES = {
  SSO_TOKEN: 'x-sso-token',
  USER_AGENT: 'x-user-agent',
  DEVICE_ID: 'dev-id',
  FLOW_TYPE: 'login-flow',
};

const CUSTOM_HEADERS = {
  REQUEST_ID: 'x-request-id',
  CLIENT_PLATFORM: 'x-client-platform',
  H5_USER_AGENT: 'x-h5-user-agent',
};

const APP_LINKS = {
  ANDROID: 'https://play.google.com/store/apps/details?id=com.paytmmoney',
  IOS: 'https://apple.co/2R6b9hA',
};

const VERTICAL_NAMES = {
  STOCKS: {
    id: 'stocks',
    name: 'equity',
  },
  MF: {
    id: 'mf',
    name: 'mf',
  },
};

const regexKey = {
  EMAIL: 'email',
  OTP_MOBILE: 'tel_otp',
  VALIDATE_OTP: 'validate_otp',
};

const OTP_LENGTH = 6;
const VALIDATE_NUMBER = /^[0-9]+$/;

const ERROR_MESSAGES = {
  VERIFY_LIMIT_CROSS: {
    err1: 'You have entered incorrect OTP multiple times.',
    err2: 'Please resend OTP to continue.',
  },
  RESEN_ATTEMPTS: {
    err1: 'You have exceeded the maximum resend attempts.',
    err2: 'Please try again after 24 hours or Login with Password',
    err3: 'Please try again after 24 hours.',
  },
};
export {
  THEMES,
  COOKIES,
  CUSTOM_HEADERS,
  VERTICAL_NAMES,
  APP_LINKS,
  regexKey,
  OTP_LENGTH,
  VALIDATE_NUMBER,
  ERROR_MESSAGES,
};
