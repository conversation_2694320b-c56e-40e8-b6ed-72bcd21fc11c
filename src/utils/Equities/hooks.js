/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable no-unsafe-optional-chaining */
import { useMemo, useState, useEffect } from 'react';
import { empty } from 'rxjs';
import { useAppStore } from '../../contexts/AppContextProvider';
import { INSTRUMENTS, SEGMENT_TYPES, EXCHANGE_CODE } from './enum';
import { getCombinedFeed } from './index';
import {
  RESPONSE_TYPES,
  REQUEST_TYPES,
} from '../../services/equities/dataConfig';

function useObservable(observableFn, input) {
  const [value, setValue] = useState(null);
  const memoizedObservable = useMemo(observableFn, input);

  useEffect(() => {
    const subscription = memoizedObservable?.subscribe({
      next: (v) => setValue(v),
    });
    return () => subscription?.unsubscribe();
  }, [memoizedObservable]);
  return value;
}

export const getLtpChangeFromTradeClose = (trade, close) => {
  if (close?.previousPrice !== null && close?.previousPrice !== undefined) {
    const pClose = close?.previousPrice;
    const change = trade?.lastTradePrice - close?.previousPrice;
    const percentageChange = (change * 100) / close?.previousPrice;
    const ltp = trade?.lastTradePrice;
    return {
      pClose,
      ltp,
      change: ltp ? change : 0,
      percentageChange: ltp ? percentageChange : 0,
    };
  }
  return {
    pClose: undefined,
    ltp: trade?.lastTradePrice,
    change: undefined,
    percentageChange: 0,
  };
};

function useFeeds(
  reqType,
  requiredStreams,
  { exchange, securityId, segment = SEGMENT_TYPES.CASH },
) {
  const { dataFeed } = useAppStore();
  const observableList = useMemo(
    () =>
      dataFeed?.getStream(
        reqType,
        exchange
          ? EXCHANGE_CODE[segment]?.[exchange]
            ? [EXCHANGE_CODE[segment][exchange], securityId]
            : [EXCHANGE_CODE[exchange], securityId]
          : [securityId],
        requiredStreams,
      ),
    [dataFeed, exchange, reqType, requiredStreams, securityId, segment],
  );
  return observableList || [];
}

function useResultFromFeed(
  reqType,
  requiredStreamsRaw,
  { exchange, securityId, segment = SEGMENT_TYPES.CASH },
) {
  const requiredStreams = Array.isArray(requiredStreamsRaw)
    ? requiredStreamsRaw
    : [requiredStreamsRaw];
  const stockFeed = useFeeds(reqType, requiredStreams, {
    exchange,
    securityId,
    segment,
  });

  return (
    useObservable(() => {
      if (!stockFeed.length) return empty();
      if (requiredStreams.length > 1) return getCombinedFeed(stockFeed);
      return stockFeed[0].feed;
    }, [stockFeed]) || (requiredStreams.length > 1 ? [] : null)
  );
}

const requiredInstructionFeedResponse = [RESPONSE_TYPES.INDEX];

function IndexPrice({ securityId }) {
  const stockFeed = useResultFromFeed(
    REQUEST_TYPES.INDEX,
    requiredInstructionFeedResponse,
    {
      securityId,
    },
  );
  if (stockFeed && typeof stockFeed === 'object') {
    let percentageChange = 0;
    let change;
    if (stockFeed?.pClose !== null && stockFeed?.pClose !== undefined) {
      change = stockFeed?.lastTradePrice - stockFeed?.pClose;
      percentageChange = (change * 100) / stockFeed?.pClose;
    }

    return {
      ltp: stockFeed?.lastTradePrice,
      percentageChange,
      change,
      pClose: stockFeed?.pClose,
    };
  }
  return {};
}

const requiredFeedResponse = [RESPONSE_TYPES.TRADE, RESPONSE_TYPES.P_CLOSE];

function useStockFeed({ exchange, securityId, segment = SEGMENT_TYPES.CASH }) {
  const result = useResultFromFeed(REQUEST_TYPES.STOCK, requiredFeedResponse, {
    exchange,
    securityId,
    segment,
  });

  if (Array.isArray(result)) {
    const [tradeData, closeData] = result;
    const { percentageChange, change, ltp, pClose } =
      getLtpChangeFromTradeClose(tradeData, closeData);

    return {
      pClose,
      ltp,
      change,
      percentageChange,
    };
  }
  return {};
}

function useStockAndInstrumentFeed({
  instrumentType,
  exchange,
  securityId,
  segment,
}) {
  const { ltp, percentageChange, change, pClose } =
    instrumentType === INSTRUMENTS.INDEX
      ? IndexPrice({ securityId })
      : useStockFeed({
          exchange,
          securityId,
          segment,
        });
  return {
    ltp,
    percentageChange,
    change,
    pClose,
  };
}
export { useStockAndInstrumentFeed, useStockFeed, IndexPrice };
