const EXCHANGE = {
  NSE: 'NSE',
  BSE: 'BSE',
};

const SEGMENT_TYPES = {
  CASH: 'E',
  DERIVATIVES: 'D',
};

const TRANSACTION_TYPES = {
  BUY: 'B',
  SELL: 'S',
};

const transactionTypeLabels = {
  [TRANSACTION_TYPES.BUY]: 'Buy',
  [TRANSACTION_TYPES.SELL]: 'Sell',
};

const INSTRUMENTS_SEARCH = {
  ALL: 'ALL',
  STOCK: 'ES',
  INDEX: 'I',
  ETF: 'ETF',
  OPT: 'OPT',
  FUT: 'FUT',
  MF: 'MF',
};

const INSTRUMENTS = {
  STOCK: 'ES',
  INDEX: 'I',
  ETF: 'ETF',
  FUTIDX: 'FUTIDX',
  FUTSTK: 'FUTSTK',
  OPTIDX: 'OPTIDX',
  OPTSTK: 'OPTSTK',
  REIT: 'REIT',
  INVITU: 'InvITU',
  DBT: 'DBT',
  GB: 'GB',
  CB: 'CB',
  DEB: 'DEB',
};

const INSTRUMENTS_LABEL_MAPPING = {
  [INSTRUMENTS.STOCK]: 'Stock',
  [INSTRUMENTS.INDEX]: 'Index',
  [INSTRUMENTS.ETF]: 'ETF',
  [INSTRUMENTS.FUTSTK]: 'FUT',
  [INSTRUMENTS.FUTIDX]: 'FUT',
  [INSTRUMENTS.OPTSTK]: 'OPT',
  [INSTRUMENTS.OPTIDX]: 'OPT',
  [INSTRUMENTS.INVITU]: 'Stock',
  [INSTRUMENTS.REIT]: 'Stock',
  MF: 'MF',
  NPS: 'NPS',
};

const EXCHANGE_CODE = {
  [EXCHANGE.NSE]: 1,
  [EXCHANGE.BSE]: 4,
  [SEGMENT_TYPES.CASH]: {
    [EXCHANGE.NSE]: 1,
    [EXCHANGE.BSE]: 4,
  },
  [SEGMENT_TYPES.DERIVATIVES]: {
    [EXCHANGE.NSE]: 2,
    [EXCHANGE.BSE]: 8,
  },
};

const PRODUCT_TYPES = {
  DELIVERY: 'C',
  MARGIN: 'M',
  INTRADAY: 'I',
  BRACKET_ORDER: 'B',
  COVER_ORDER: 'V',
  MTF: 'F',
};

const productTypeLabels = {
  [PRODUCT_TYPES.DELIVERY]: 'Delivery',
  [PRODUCT_TYPES.MARGIN]: 'Overnight',
  [PRODUCT_TYPES.INTRADAY]: 'Intraday',
  [PRODUCT_TYPES.BRACKET_ORDER]: 'Bracket',
  [PRODUCT_TYPES.COVER_ORDER]: 'Cover',
  [PRODUCT_TYPES.MTF]: 'Pay Later (MTF)',
};

const ORDER_TYPES = {
  MKT: 'MKT',
  LMT: 'LMT',
  SLM: 'SLM',
  SL: 'SL',
};

// const EXCHANGE_CODE_MAPPER = {
//   [EXCHANGE_CODE[EXCHANGE.NSE]]: EXCHANGE.NSE,
//   [EXCHANGE_CODE[EXCHANGE.BSE]]: EXCHANGE.BSE,
// };

const SEGMENT_TYPE = {
  [INSTRUMENTS.STOCK]: 'E',
  [INSTRUMENTS.INDEX]: 'I',
};

const segmentLabels = {
  [SEGMENT_TYPES.CASH]: 'Cash',
  [SEGMENT_TYPES.DERIVATIVES]: 'F&O',
};

const ORDER_STATUS = {
  TRANSIT: 'Transit',
  PENDING: 'Pending',
  TRIGGERED: 'Triggered',
  PART_TRADED: 'Part-Traded',
  MODIFIED: 'Modified',
  CANCELLED: 'Cancelled',
  SUCCESSFUL: 'Successful',
  TRADED: 'Traded',
  REJECTED: 'Rejected',
  FROZEN: 'Frozen',
  EXPIRED: 'Expired',
  AMO_PENDING: 'Pending AMO',
  O_PENDING: 'O-Pending',
  AMO_MODIFIED: 'Modified AMO',
  O_MODIFIED: 'O-Modified',
  AMO_CANCELLED: 'Cancelled AMO',
  O_CANCELLED: 'O-Cancelled',
  FAILED: 'Failed',
};

const orderStatusLabels = {
  [ORDER_STATUS.TRADED]: [ORDER_STATUS.SUCCESSFUL],
  [ORDER_STATUS.REJECTED]: [ORDER_STATUS.REJECTED],
  [ORDER_STATUS.CANCELLED]: [ORDER_STATUS.CANCELLED],
  [ORDER_STATUS.O_CANCELLED]: [ORDER_STATUS.AMO_CANCELLED],
};

const channelLabels = {
  PaytmMoney: 'PaytmMoney',
  Paytm: 'Paytm',
};

const STATUS_BUCKET = {
  PENDING: [
    ORDER_STATUS.TRANSIT,
    ORDER_STATUS.PENDING,
    ORDER_STATUS.TRIGGERED,
    ORDER_STATUS.PART_TRADED,
    ORDER_STATUS.MODIFIED,
    ORDER_STATUS.AMO_PENDING,
    ORDER_STATUS.O_PENDING,
    ORDER_STATUS.AMO_MODIFIED,
    ORDER_STATUS.O_MODIFIED,
  ],
  SUCCESSFUL: [ORDER_STATUS.SUCCESSFUL, ORDER_STATUS.TRADED],
  FAILED: [
    ORDER_STATUS.REJECTED,
    ORDER_STATUS.FROZEN,
    ORDER_STATUS.EXPIRED,
    ORDER_STATUS.FAILED,
  ],
  CANCELLED: [
    ORDER_STATUS.CANCELLED,
    ORDER_STATUS.AMO_CANCELLED,
    ORDER_STATUS.O_CANCELLED,
  ],
};

const ORDER_STATUS_BUCKETS = {
  PENDING: {
    key: 'Pending',
    values: STATUS_BUCKET.PENDING,
  },
  SUCCESSFUL: {
    key: 'Successful',
    values: STATUS_BUCKET.SUCCESSFUL,
  },
  FAILED: {
    key: 'Failed',
    values: STATUS_BUCKET.FAILED,
  },
  CANCELLED: {
    key: 'Cancelled',
    values: STATUS_BUCKET.CANCELLED,
  },
};

const orderTypeLabels = {
  MKT: 'Market',
  LMT: 'Limit',
  SLM: 'Stoploss Market',
  SL: 'Stoploss Limit',
};

const validityTypeLabels = {
  DAY: 'DAY',
};

export const MTF_IR_STATUS = {
  PENDING: 'PENDING',
  REVOKED: 'REVOKED',
  ACTIVE: 'ACTIVE',
  NOT_OPTED: 'NOT_OPTED',
  SUBSCRIPTION_PENDING: 'SUBSCRIPTION_PENDING', // This status for pending case
  UNDER_REGISTRATION: 'UNDER_REGISTRATION',
};

const BUFFER_MULTIPLIER_PLACE_ORDER = 1.0313;
const TICK_SIZE = 0.05;

const MARKET_STATUS_ERROR_CODES = ['3336', '3337', 'O3052'];

const PRODUCT_TYPE = {
  EQ: 'EQ',
};

export {
  EXCHANGE,
  SEGMENT_TYPES,
  EXCHANGE_CODE,
  // EXCHANGE_CODE_MAPPER,
  INSTRUMENTS,
  SEGMENT_TYPE,
  PRODUCT_TYPES,
  ORDER_TYPES,
  TRANSACTION_TYPES,
  BUFFER_MULTIPLIER_PLACE_ORDER,
  ORDER_STATUS,
  ORDER_STATUS_BUCKETS,
  productTypeLabels,
  orderTypeLabels,
  validityTypeLabels,
  MARKET_STATUS_ERROR_CODES,
  transactionTypeLabels,
  TICK_SIZE,
  segmentLabels,
  orderStatusLabels,
  channelLabels,
  PRODUCT_TYPE,
  INSTRUMENTS_SEARCH,
  INSTRUMENTS_LABEL_MAPPING,
};
