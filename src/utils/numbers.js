export function getOrdinalSuffix(num){
    if(!num || typeof num !=='number') return
    const lastDigit = num%10
    const lastTwoDigit = num%100

    // if ends with 11, 12, or 13 return default th
    // else st for 1, nd for 2 and rd for 3
     if(lastDigit===1 && lastTwoDigit!==11){
        return 'st'
     }
     if(lastDigit===2 & lastTwoDigit!==12){
        return 'nd'
     }
     if(lastDigit===3 && lastTwoDigit!==13){
        return 'rd'
     }
     return 'th' // for remaining
}