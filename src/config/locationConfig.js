import accessDenied from '../assets/LocationAccess/accessDenied.png';
import locationAccess from '../assets/LocationAccess/locationAccess.png';

const LOCATION_ACCESS_DATA = {
  HEADING: 'Location Access',
};

const SCREEN_TYPE = {
  CHECKING_LOCATION_PERMISSION: 'CHECKING_LOCATION_PERMISSION',
  LOCATION_ACCESS_LANDING: 'Location Access Landing',
  ACCESS_DENIED: 'Access Denies',
  IN_PROGRESS: 'In Progress',
  ALLOWED: 'Allowed',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  SKIP: 'SKIP',
};

const LOCATION_ACCESS_LANDING = {
  IMAGE_SRC: locationAccess,
  IMAGE_NAME: '',
  SUB_HEADING: 'Allow location access',
  MESSAGE:
    'As per SEBI regulations, we need to confirm your location before proceeding further. Please allow location access to continue.',
  BUTTON_TEXT: 'Allow',
};

const ACCESS_DENIED = {
  IMAGE_SRC: accessDenied,
  IMAGE_NAME: '',
  SUB_HEADING: 'Access Denied!',
  MESSAGE:
    'Your current location seems to be outside India. As per SEBI regulations, you need to be in India to complete your account opening process. Please try again later.',
  BUTTON_TEXT: 'Explore Investments',
};

const IN_PROGRESS = {
  MESSAGE: 'Fetching location details…',
  BUTTON_TEXT: 'Allow',
};

const PERMISSION_DENIED = {
  IMAGE_SRC: accessDenied,
  IMAGE_NAME: '',
  SUB_HEADING: 'Access Denied!',
  MESSAGE:
    'We need location access to continue with this process. Please grant location permission.',
  BUTTON_TEXT: 'Okay',
};

export {
  SCREEN_TYPE,
  LOCATION_ACCESS_DATA,
  LOCATION_ACCESS_LANDING,
  ACCESS_DENIED,
  IN_PROGRESS,
  PERMISSION_DENIED,
};
