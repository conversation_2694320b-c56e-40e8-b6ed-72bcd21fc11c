export const holidaysMap = {
  20200106: true,
  20200221: true,
  20200310: true,
  20200402: true,
  20200406: true,
  20200410: true,
  20200414: true,
  20200501: true,
  20200525: true,
  20201002: true,
  20201116: true,
  20201130: true,
  20190304: true,
  20190321: true,
  20190417: true,
  20190429: true,
  20190501: true,
  20190605: true,
  20190812: true,
  20190815: true,
  20190902: true,
  20191002: true,
  20191008: true,
  20191021: true,
  20191028: true,
  20191112: true,
  20191225: true,
  20210126: true,
  20210311: true,
  20210329: true,
  20210402: true,
  20210414: true,
  20210421: true,
  20210425: true,
  20210501: true,
  20210513: true,
  20210721: true,
  20210815: true,
  20210819: true,
  20210910: true,
  20211002: true,
  20211015: true,
  20211105: true,
  20211119: true,
  20211225: true,
  20220126: true,
  20220301: true,
  20220318: true,
  20220414: true,
  20220415: true,
  20220503: true,
  20220809: true,
  20220815: true,
  20220831: true,
  20221005: true,
  20221026: true,
  20221108: true,
  20230126: true,
  20230307: true,
  20230330: true,
  20230404: true,
  20230407: true,
  20230414: true,
  20230501: true,
  20230629: true,
  20230815: true,
  20230919: true,
  20231002: true,
  20231024: true,
  20231114: true,
  20231127: true,
  20231225: true,
  20240122: true,
  20240126: true,
  20240308: true,
  20240325: true,
  20240329: true,
  20240411: true,
  20240417: true,
  20240501: true,
  20240520: true,
  20240617: true,
  20240717: true,
  20240815: true,
  20241002: true,
  20241101: true,
  20241115: true,
  20241120: true,
  20241225: true,
  20250226: true,
  20250314: true,
  20250331: true,
  20250410: true,
  20250414: true,
  20250418: true,
  20250501: true,
  20250815: true,
  20250827: true,
  20251002: true,
  20251021: true,
  20251022: true,
  20251105: true,
  20251225: true,
};

export const muhuratMap = {
  20201114: '1815-1915',
  20211104: '1815-1915',
  20210224: '1545-1700',
  20221024: '1815-1915',
  // Diwali 2023
  20231112: '1815-1915',
  20240120: '0915-1530' /* PM3-16172: Jan 20th 2024 changes */,
  20240302: '0915-1000,1130-1230' /* PM3-18915: March 2nd 2024 changes */,
  20240518: '0915-1000,1130-1230' /* PM3-22845: May 18th 2024 changes */,
  // Diwali 2024
  20241101: '1800-1900',
  20250201: '0915-1530',
  ...(JSON.parse(localStorage.getItem('mockTradingViewLightSession')) || {}),
};

export const chartsFallbackConfig = {
  holidaysMap,
  muhuratMap,
};
