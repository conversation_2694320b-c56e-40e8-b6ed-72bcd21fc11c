import { isDarkMode } from '@utils/commonUtil';
import noFavDataBnrImg from '../assets/images/favError.png';

const BOOKMARK_ICON = isDarkMode()
  ? require('@src/assets/icons/bookmarkWhite.png')
  : require('@src/assets/icons/bookmarkBlack.png');

const ADD_TO_WATCHLIST = isDarkMode()
  ? require('@src/assets/icons/watchlist_blue_1.png')
  : require('@src/assets/icons/watchlist_blue.png');

export { BOOKMARK_ICON, ADD_TO_WATCHLIST };

export const ADD_WATCHLIST = '+ Add Watchlist';

export const FAV_DATA = {
  FAV_TITLE: 'My Watchlist',
  SELECT_WATCHLIST: 'Select Watchlist',
  MARKET_TITLE: 'Market Movers',
  SUBTITLE: "Current price and today's % change",
  EMPTY_FAV_TITLE: 'Your watchlist is empty',
  EMPTY_FAV_TEXT: 'Search and add stocks to this watchlist',
  ADD_TO_FAV: 'Add Stocks',
  SHARE_PRICE: `/share`,
  ERROR_MSG: 'Something went wrong!',
  NO_WISHLIST: 'NO_WISHLIST',
  NO_WISHLIST_MSG: 'NO_WISHLIST in fave page creating wishlist',
  CREATE_WATCHLIST_NAME: 'mini-favourite',
  REMOVED_FROM_FAV: (companyName, watchlistName) =>
    `${companyName} removed from your ${watchlistName}`,
  EMPTY_BNR_IMG: noFavDataBnrImg,
  ETFS: 'ETFs',
  EDIT_WATCHLIST: 'Edit Watchlist',
  REORDER_SECURITIES_SUCCESS: 'Securities reordered successfully',
};
