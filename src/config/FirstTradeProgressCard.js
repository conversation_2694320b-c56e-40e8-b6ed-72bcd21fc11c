export const FUND_ADDED = {
  HASADDED: 'HASADDED',
};

export const HAS_INVESTED = {
  NOTINVESTED: 'NOTINVESTED',
  HASINVESTED: 'HASINVESTED',
  INPROGRESS: 'INPROCESS',
};

export const HAS_TRADED = {
  NOTTRADED: 'NOTTRADED',
  HASTRADED: 'HASTRADED',
};

export const IR_STATUS_ENUM = {
  DEFAULT: 'DEFAULT',
  PENDING: 'PENDING',
  LOCKED: 'LOCKED',
  VERIFIED: 'VERIFIED',
  ACTIVE: 'ACTIVE',
  SUBMITTED: 'SUBMITTED',
  REJECTED: 'REJECTED',
  UNDER_REGISTRATION: 'UNDER_REGISTRATION',
  DOCUMENTS_SUBMITTED: 'DOCUMENTS_SUBMITTED',
  ESIGN_UNDER_VERIFICATION: 'ESIGN_UNDER_VERIFICATION',
  INVERIFICATION: 'INVERIFICATION',
  REVOKED: 'REVOKED',
  NOT_OPTED: 'NOT_OPTED',
  AADHAR_NOT_SEEDED_PAN_REVOKED: 'AADHAR_NOT_SEEDED_PAN_REVOKED',
  DORMANT_REVOKED: 'DORMANT_REVOKED',
  DORMANCY_IN_PROGRESS: 'DORMANCY_IN_PROGRESS',
  REKYC_IN_PROGRESS: 'REKYC_IN_PROGRESS',
  DOCUMENTS_PENDING: 'DOCUMENTS_PENDING',
  KRA_VALIDATION_REVOKED: 'KRA_VALIDATION_REVOKED',
  ACCOUNT_FREEZE: 'ACCOUNT_FREEZE',
  ADD_FUNDS_READY: 'ADD_FUNDS_READY',
  IR_REVOKED: 'IR_REVOKED',
};

export const CARD_IDS = {
  FIRST_TRADE_STOCK_PRICE: 'FIRST_TRADE_STOCK_PRICE',
  TITLE_PREIR: 'TITLE_PREIR',
  SUB_TITLE_PREIR: 'SUB_TITLE_PREIR',
  USER_READINESS_API: 'USER_READINESS_API',
  ACCOUNT_DETAILS_CARD: 'ACCOUNT_DETAILS_CARD',
  FUND_ADDITION_CARD: 'FUND_ADDITION_CARD',
  STOCK_PURCHASE_CARD: 'STOCK_PURCHASE_CARD',
  MF_PURCHASE_CARD: 'MF_PURCHASE_CARD',
  PERCENT_COMPLETE: 'PERCENT_COMPLETE',
};

export const PERCENTAGES_MAP = {
  AC_DETAILS: 'A/c details',
  ADD_FUNDS: 'Add fund',
  FIRST_STOCK: 'First Stock',
  FIRST_MF: 'First MF',
};

export const LABELS = {
  RIGHT_AFTER_ONBOARDING: 'right_after_onboarding',
  HOME_SCREEN: 'home_screen',
  OTHER: 'other',
};

export const EVENTS = {
  VERTICAL_NAME: 'homescreen_4.0',
  SCREEN_NAME: '/homescreen',
  CATEGORY: 'homescreen',
  EVENT: {
    OPEN_SCREEN: 'openScreen',
    CUSTOM_EVENT: 'custom_event',
  },
  EVENT_TYPE: 'view',
  ACTIONS: {
    NEW_IR_WIDGET: 'newIR_widget',
    ADD_FUND_CTA_CLICK: 'addfund_ctaclick',
    INVEST_NOW_STOCKS_CTA_CLICK: 'investnowstocks_ctaclick',
    INVEST_NOW_STOCKS_ORDER_STATUS_CLICK: 'investnowstocks_orderstatusclick',
    INVEST_NOW_MF_CTA_CLICK: 'investnowmf_ctaclick',
    INVEST_NOW_MF_ORDER_STATUS_CLICK: 'investnowmf_orderstatusclick',
    VIEW_ACCOUNT_STATUS_CLICK: 'viewaccountstatus_click',
  },
};
