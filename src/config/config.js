import {
  SEGMENT_TYPES,
  EXCHANGE,
  PRODUCT_TYPES,
  ORDER_TYPES,
} from '../utils/Equities/enum';
import {URLs} from "./envConfig" 
const fallbackConfig = {
  [SEGMENT_TYPES.CASH]: {
    [EXCHANGE.NSE]: {
      exchange: EXCHANGE.NSE,
      segment: 'E',
      status: 1,
      message: '',
      amo: false,
      [PRODUCT_TYPES.DELIVERY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.INTRADAY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.BRACKET_ORDER]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.COVER_ORDER]: [ORDER_TYPES.LMT],
    },
    [EXCHANGE.BSE]: {
      exchange: EXCHANGE.BSE,
      segment: 'E',
      status: 1,
      message: '',
      amo: false,
      [PRODUCT_TYPES.DELIVERY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.INTRADAY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.BRACKET_ORDER]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.COVER_ORDER]: [ORDER_TYPES.LMT],
    },
  },
  [SEGMENT_TYPES.DERIVATIVES]: {
    [EXCHANGE.NSE]: {
      exchange: EXCHANGE.NSE,
      segment: 'D',
      status: 1,
      message: '',
      [PRODUCT_TYPES.MARGIN]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.INTRADAY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.BRACKET_ORDER]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.COVER_ORDER]: [ORDER_TYPES.MKT, ORDER_TYPES.LMT],
    },
    [EXCHANGE.BSE]: {
      exchange: EXCHANGE.BSE,
      segment: 'D',
      status: 1,
      message: '',
      [PRODUCT_TYPES.MARGIN]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.INTRADAY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.BRACKET_ORDER]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.COVER_ORDER]: [ORDER_TYPES.MKT, ORDER_TYPES.LMT],
    },
  },
};

export const REGEX = {
  VALIDATE_UPI_ID: /[a-zA-Z0-9_+.-]@[a-zA-Z0-9]/,
  VALIDATE_DP_ID: /(^[I,i][N,n][0-9]{6}$)/,
  VALIDATE_CLIENT_ID: /^\d{8}$/,
  VALIDATE_CDSL_ACCOUNT: /^\d{16}$/,
  VALIDATE_PAN: /^[a-zA-Z]{5}[0-9]{4}[a-zA-Z]{1}$/,
  VALIDATE_POSTAL_CODE: /^[1-9]{1}[0-9]{2}[0-9]{3}$/,
  VALIDATE_MOBILE_NUMBER: /^[6-9]\d{9}$/,
  VALIDATE_EMAIL: /^([a-zA-Z0-9_.-])+@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/,
  VALIDATE_TEXT_FOR_NAME: /^[a-zA-Z][a-zA-Z. ]{1,30}$/,
  VALIDATE_DD_MM_YYYY_FORMAT: /^(0[1-9]|[12][0-9]|3[01])[/](0[1-9]|1[012])[/]\d{4}$/, // DD/MM/YYYY
  VALIDATE_PASSCODE: /^\d{4}$/,
  VALIDATE_OTP: /^\d{6}$/,
  VALIDATE_ADDRESS: /^[a-zA-Z0-9\s,.'\-#()+/_]*$/,
  ADD_COMMA_AMOUNT: /\B(?=(?:(\d\d)+(\d)(?!\d))+(?!\d))/g,
  VALIDATE_NUMBER: /^[0-9]+$/,
  BANK_ACC_REPLACEMENT: /[^0-9]/gi,
  VALIDATE_BANK_IFSC: /[^a-z0-9]/gi,
};

export { fallbackConfig };
