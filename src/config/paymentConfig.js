export const PAYMENT_CONSTANTS = {
  TITLE: 'Add Money',
  DESCRIPTION:
    'Any leftover money after purchase, will be kept in your Investment Wallet. This can be used for investments or transferred to your bank account.',
  UPI_ALREADY_EXIST: 'UPI ID is already saved with us',
  ACC_NO: 'A/c No.',
  INPUT_UPI: 'Enter your UPI ID',
  ADD_MONEY: 'Proceed to Add Money',
  UPI_ID_REQUIRED: 'UPI ID required',
  UPI_ID_INVALID: 'UPI ID invalid',
  UPI_ID_MODE_NOT_SUPPORTED: 'Support payments up to ₹1,00,000 only',
  LOW_BALANCE: 'The Available Balance is low',
  ACCOUNT_INACTIVE: 'This account is inactive',
  PPBL: 'Paytm Payments Bank Ltd',
  CHECK_BALANCE: 'Check Balance',
  AVAILABLE_BALANCE: 'Available Balance:',
  INSUFFICIENT_BALANCE: 'insufficient balance',
  APPLY: 'Apply',
  ADD: 'add',
  BUY: 'buy',
  TOTAL: 'Total',
  PLEASE_NOTE: 'Please Note: ',
  WARNING: 'This bank is currently unavailable for UPI transactions',
  VIEW_LESS: 'View less',
  VIEW_MORE: 'View more',
  PAYTM_UPI_ID_WARNING:
    "Please avoid Paytm UPI IDs - @ptsbi, @ptyes, @ptaxis, @pthdfc. We'll improve your experience soon. Use other UPI IDs in the meantime.",
};

export const PAYMENT_UPI_INTENT = {
  SELECT_UPI_APP: 'Select your UPI App',
  SELECT_UPI_APP_MULTIBANK:
    'Select UPI app of choice or enter your UPI ID below',
  UPI_APP_DESCRIPTION: bank =>
    `Please ensure you are paying from UPI ID linked with ${bank}`,
  UPI_APP_MULTIBANK_DESCRIPTION:
    'Complete transaction by accepting request on UPI app',
  OR: 'OR',
  MORE: 'Pay by any UPI App',
  ALL_UPI_APPS: 'Pay by any UPI App',
  UPI: 'upi',
  ALL_UPI_LIST: 'all_upi_list',
  UPI_LIMIT: 3,
  PAYTM_PROCESS_NAME: 'net.one97.paytm',
  PAYTM_APP_IDENTIFIER: 'paytmmp://',
  PAYTM_UPI: 'Paytm UPI',
  MULTIBANK_PAYMENT_DESCRIPTION:
    'We recommend using one of these accounts to avoid payment failure',
  NOTE: 'Note: ',
  UPI_INTENT_MESSAGE: bankDetails => `Please pay from ${bankDetails}`,
  MULTIBANK_INTENT_MESSAGE:
    'Please pay only from one of the added bank accounts',
  UPI_INTENT: 'UPI_INTENT',
  UPI_COLLECT: 'UPI_COLLECT',
  NET_BANKING: 'Net Banking',
};

export const PAYMENT_MODES = {
  NET_BANKING: 1,
  UPI: 5,
  PPBL: 10,
};

export const UPI_REGEX = /^[\w.-]+@[\w.-]+$/;
export const UPI_REGEX_PAYTM = /^[\w.-]+@pt[\w.-]+$/;

export const PAYMENT_OPTIONS = {
  REDIRECT: {
    WEB_VIEW_TYPE: 'PaymentWebviewRedirect',
    WEB_VIEW_TITLE: 'Complete Payment',
  },
  UPI_TPV_FORM: {
    WEB_VIEW_TYPE: 'PaymentSDK',
    WEB_VIEW_TITLE: 'Complete Payment',
  },
  INITIALIZE_SDK: {
    WEB_VIEW_TYPE: 'PaymentSDK',
    WEB_VIEW_TITLE: 'Complete Payment',
  },
  UPI_MANDATE: {
    WEB_VIEW_TYPE: 'PaymentWebviewRedirect',
    WEB_VIEW_TITLE: 'Setup Autopay',
  },
};

export const MAKE_PAYMENT_RESPONSE_TYPES = {
  REDIRECT: 'REDIRECT',
  UPI_TPV_FORM: 'UPI_TPV_FORM',
  INITIALIZE_SDK: 'INITIALIZE_SDK',
  UPI_DEEP_LINK: 'UPI_DEEP_LINK',
  UPI_MANDATE: 'UPI_MANDATE',
};

export const PAYMENT_STATUS = {
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  PENDING: 'PENDING',
};

// TODO modify upi intent version after testing
export const UPI_INTENT_APP_VERSION = '10.11.0';
export const UPI_INTENT_APP_PAYTM_VERSION = '10.44.3';

// export const PAYMENT_STATUS_CONSTANT = {
//   SUCCESS: {
//     IMG_NAME: ICONS_NAME.TRANSACTION_SUCCESS_WALLET,
//     TITLE: 'added successfully',
//     DESCRIPTION: 'Money is added to Cash Balance. Proceed to buy stocks.',
//     BUTTON: 'Proceed',
//   },
//   FAILED: {
//     IMG_NAME: ICONS_NAME.TRANSACTION_FAILED,
//     TITLE: 'Oops! Money addition failed',
//     DESCRIPTION:
//       'Incorrect UPI pin entered by you. Any amount debited will be reversed in 4-5 working days.',
//     BUTTON: 'Try Again',
//   },
//   PENDING: {
//     IMG_NAME: ICONS_NAME.TRANSACTION_PENDING,
//     TITLE: 'Money addition is in process',
//     DESCRIPTION:
//       'Generally the status is updated in one working day. Any amount debited will be reversed in 4-5 working days.',
//     BUTTON: 'Go to Home',
//   },
// };

export const ADD_MONEY_COPY_TITLE = 'Add Money Transaction Id';

export const PAYMENT_SOURCE = {
  PLACE_ORDER: 'placeOrder',
  FUNDS: 'funds',
  BOND: 'bond',
  BBC: 'bbc',
};

export const BBC_NAVIGATION_ROUTES = {
  CONFIRM_SIP: '/bbc/confirm-sip',
  SIP_SETUP: '/bbc/sip-setup',
  MANAGE_AUTOPAY: '/bbc/manage-autopay',
};

export const PRIORITIZED_PROCESSES_SET = [
  'net.one97.paytm',
  'com.google.android.apps.nbu.paisa.user',
  'com.phonepe.app',
];
export const PRIORITIZED_PROCESSES_SET_IOS = [
  'paytmmp://',
  'tez://',
  'phonepe://',
];

export const intentFlowMessage = {
  enableSleekCard: false,
  drawerSleekCard: true,
  sleekCardDetails: {
    title: 'Did you know?',
    icon: 'https://static.paytmmoney.com/icons/infocard.png',
    message:
      'We settle funds on the same day to your bank account by 9PM, in ca...',
    cta_link: '',
    cta_text: 'Know More',
    cta_type: 'EXTERNAL',
  },
  drawerCardDetails: {
    headerText: 'Pay via registered bank accounts only',
    subHeaderText:
      'Please select the same Bank on your UPI App as the Bank selected on this page',
    callToActionText: 'Okay',
  },
};

export const PAYMENT_DATA = {
  PAYMENT_MODES: {
    NET_BANKING: 1,
    UPI: 5,
    PPBL: 10,
    BANK_TRANSFER: 11,
  },
};

export const QUICK_PAYMENT_DATA = {
  FUND_ADD_CTA: (amount) => `Add ₹${amount} to Funds`,
  UPI_COLLECT: 'UPI_COLLECT',
  UPI_INTENT: 'UPI_INTENT',
  UPI_FALLBACK: 'UPI_FALLBACK',
};

export const PAYMENTS_CONFIG = {
  partialTxn: {
    title: 'Partial Transaction',
    subTitle:
      'A transaction can be partially approved for any of the reasons below',
    list: [
      {
        id: 1,
        value:
          'You have traded after placing a withdrawal request in the same session',
      },
      {
        id: 2,
        value:
          'Your ledger was deducted for the due charges/subscription, etc.',
      },
    ],
  },
  multiBankTxnInfo:
    "Your payment bank might differ from actual payment bank. We have displayed your default bank here. Rest assured, we will only debit your bank account, if it's authorised by you",
  addNewBankDeeplink:
    'paytmmoney:///mini-app?aId=ec7d3582692b487a90247ee78d850f5f&pageName=view-bank',
  payout: {
    title: 'Withdraw Funds',
    description: 'Available Funds: ',
    selectBankAccount: 'Select your Bank Account',
    addNewBankAccount: 'Add new bank account',
    transferrableBalance: 'Transferrable Balance',
    withdrawableFunds: 'Withdrawable Funds',
    unsettledFunds: 'Unsettled Funds',
    cashBalanceDesc: 'Why is this balance less than Cash Balance?',
    learn: 'Learn',
    unsettledAmount: 'Unsettled Funds',
    unsettledAmountDesc:
      'Unsettled funds refer to money that is not yet available for withdrawal or use because the related transactions are still in process',
    withdrawableFundsDesc:
      'Withdrawable funds are the amount of money in your brokerage account that you can take out or transfer to your bank account',
    amountPlaceholder: 'Enter Amount',
    minAmountDesc: 'Minimum amount allowed is ',
    maxAmountDesc: 'Maximum amount allowed is ',
    bankDesc: 'Money will be transferred to this account',
    note: 'Note:',
    changeBank: 'Change Bank',
    bankDesc2: 'Funds will be transferred to this account',
    withdrawalNote:
      'Withdrawal requests placed before 5:30 PM on business days will be processed within 24 hours.',
    accountNumberTitle: 'A/C No.',
    submit: 'Submit',
    maxWithdrawalLimit: 'Max Withdrawal Limit',
    amountToWithdraw: 'Amount to Withdraw',
    rupeeSymbol: '₹',
    ctaWithdraw: 'Withdraw Funds',
    withdrawalInProgressHeader: 'Withdrawal in Progress',
    withdrawalInProgressDesc:
      'Withdrawals requested before 5:30 PM on business days will be processed within 24 hrs',
    inProgress: 'In Progress',
    timeline: {
      header: 'Expected withdrawal Timelines',
      table: {
        header: {
          key: 'Eligibility',
          value: 'Timeline',
        },
        row: [
          {
            key: 'No Pending Orders / Open Positions; Cut-off - 4:30PM',
            value: '11PM today',
          },
          {
            key: 'Other requests; Cut-off - 5:30PM',
            value: '3AM on next working day',
          },
          {
            key: 'All requests after 5:30PM; Cut-off - 12:00AM',
            value: '3PM on next working day',
          },
        ],
      },
      notes: [
        'Same day withdrawals are only applicable on working days and Clients must not have any orders (unfilled/filled/cancelled) or positions (open/closed)',
        'Cut-off for same day withdrawals - 4:30PM',
      ],
      hideCta: 'Do not display again',
      cta: 'Withdraw',
      addNewBankAccount: 'Add new bank account',
      selectBankAccount: 'Select your Bank Account',
      version: 1,
    },
    cancelWithdrawal: {
      desc: 'Are you sure you want to cancel the withdrawal request',
      confirmCta: 'Yes, cancel it',
      errorHeader: 'Unable to cancel the withdrawal',
      rejectCta: "Don't want to cancel",
      errorPrimaryCta: 'Report issue',
      errorSecondaryCta: 'Go to homepage',
      errorMessage:
        'We are facing some technical issue at the moment. Please try after sometime',
      success: 'Withdrawal request has been cancelled.',
      cancelWithdrawDark:
        'https://static.paytmmoney.com/images/payments/cancelWithdrawDark.svg',
      cancelWithdrawLight:
        'https://static.paytmmoney.com/images/payments/cancelWithdrawLight.svg',
      withdrawFailedLight:
        'https://static.paytmmoney.com/images/payments/withdrawFailedLight.svg',
    },
  },
  payin: {
    title: 'Add Funds',
    addNewUpiId: 'Add New UPI ID',
    addNewBankAcc: 'Add New Bank Account',
    selectBankAccount2: 'Select your bank account for UPI payments',
    change: 'Change',
    selectBankAccount: 'Select your Bank Account',
    viewAll: 'View All',
    upiCollectTitle:
      'Add UPI ID linked to bank account registered with Paytm Money only',
    vpaNote: 'We will securely store your UPI ID for future reference',
    verify: 'Verify',
    fee: 'Fee: ',
    vpaSuggestions: ['@okaxis', '@okicici', '@axl', '@ptyes'],
    neftDescriptionFee: 'Fee: ₹10+GST for each transaction',
    vpaVerifiedMsg: 'UPI ID verified successfully',
    bankTransfer: {
      header: {
        title: 'Bank Transfer',
        desc: 'Transfer funds from your bank account to Paytm Money via NEFT, RTGS or IMPS',
      },
      cta: [
        {
          title: 'Higher Txn Limits',
          icon: 'TXN_HISTORY',
        },
        {
          title: 'Access to Small Banks',
          icon: 'FINANCE',
        },
        {
          title: 'Reliable Processing',
          icon: 'FINANCE',
        },
      ],
      notes: {
        header: 'Funds reflection time varies by bank and transfer method',
        table: {
          key: ['NEFT / RTGS: ', 'IMPS:'],
          values: [
            'Up to 2 hours, varies by bank',
            '5-10 minutes during market hours',
          ],
        },
        note: 'Fee: ₹10+GST is applicable for each transaction',
      },
      desc: {
        title: "By continuing, I accept Paytm Money's",
        cta1: 'Disclaimer ',
        text: 'and',
        cta2: 'T&C',
      },
      primaryCta: 'Enable Bank Transfer',
      success: 'Bank Transfer has been enabled.',
      error: {
        message: 'We are facing some technical error. Please try again',
        cta: 'Retry',
        title2: 'VAN creation is disabled ',
        message2:
          'RBI rules halt Paytm Bank Transfer signups. Solution underway, service to resume',
        cta2: 'Ok, got it',
        bankTransferDrawer: {
          title: 'Bank transfer is down',
          desc: 'We are facing some technical issue at this moment.  We will notify once available',
          cta: 'Notify me',
        },
      },
      enableBankSteps: {
        desc: {
          title: 'How it works',
          desc: 'Transfer funds seamlessly from your bank app',
        },
        steps: [
          {
            title: 'Log in to your Internet Banking/UPI app',
            type: 'UPI',
          },
          {
            title: 'Add and save the beneficiary details below',
            type: 'BENEFICIARY',
          },
          {
            title:
              'Transfer funds to the saved beneficiary via NEFT, RTGS, or IMPS',
            type: 'NONE',
          },
          {
            title: 'Funds will be credited to your trading account',
            type: 'NONE',
          },
        ],
        beneficiaryName: 'Beneficiary Name',
        accNumber: 'Account Number',
        ifsc: 'IFSC',
        accType: 'Account Type',
        registerBank: 'Pay using your registered banks only',
      },
    },
    offer: {
      title: '',
      cta: {
        title: 'View Offers',
        dl_type: 'embed',
        dl_link: '',
      },
    },
    cancelAddMoney: {
      header: "Don't miss out!",
      description:
        'Add funds now to unlock opportunities in stock investments and make the most of your financial goals.',
      primaryCta: 'Continue to Add Funds',
      secondaryCta: 'Exit anyway',
      exitIntentLight:
        'https://static.paytmmoney.com/data/v1/icons/exit_add_funds.svg',
      exitIntentDark:
        'https://static.paytmmoney.com/data/v1/icons/exit_add_funds_dark.svg',
      pgImage:
        'https://static.paytmmoney.com/images/payments/pgCancelAddMoney.png',
      exitIntentEnabled: true,
      version: '9.30.1120',
    },
    paymentOptionsDown: {
      title: 'No payment method available',
      redirectTitle:
        'Your request has been received. We will inform you when the payment method is available',
      desc: "Bank transfer service is currently experiencing downtime. We're working to resolve this as quickly as possible. Please try again shortly, and thank you for your patience!",
      cta1: 'Ok',
      cta2: 'Go to homepage',
      timer: 3000,
      type: 'event',
      link: 'notify_post_outage',
    },
    errorMessage: {
      noPaymentMethodSelected: 'Please select Payment method',
    },
  },
};
