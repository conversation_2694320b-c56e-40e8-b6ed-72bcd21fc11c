/* eslint-disable no-use-before-define */
import { ORDER_TYPES, PRODUCT_TYPES } from '../utils/Equities/enum';
import { getSource } from '../services/coreUtils';
import { EQUITIES, PRICES_URL } from '../config/urlConfig';
import {
  getGenericAppHeaders,
  makeApiPostCall,
  makeApiGetCall,
} from '../utils/apiUtil';

const defaultHeadersPlaceOrder = {
  ...getGenericAppHeaders(),
  channel: 'Paytm',
};
export const placeOrder = ({
  transactionType,
  exchange,
  securityId,
  quantity,
  segment,
  amo,
  isLimitOrder,
  price,
  product = PRODUCT_TYPES.DELIVERY,
  isSl,
  triggerPrice,
  locationHeader,
  orderHold = false,
  ...rest
}) => {
  const body = {
    source: getSource(),
    txn_type: transactionType,
    exchange,
    segment,
    product,
    security_id: securityId,
    quantity,
    validity: 'DAY',
    order_type: getOrderType(isLimitOrder, isSl),
    price: isLimitOrder ? parseFloat(price) : 0,
    trigger_price: isSl ? triggerPrice : null,
    off_mkt_flag: amo,
  };
  if (locationHeader) {
    defaultHeadersPlaceOrder['location-data'] = locationHeader;
  }
  return makeApiPostCall({
    url: orderHold
      ? EQUITIES.ORDERS_HOLD.PLACE_REGULAR
      : EQUITIES.ORDERS.PLACE.POST_REGULAR,
    body: {
      ...body,
      ...rest,
    },
    headers: defaultHeadersPlaceOrder,
  });
};

export const placeCoverOrder = ({
  transactionType,
  exchange,
  securityId,
  quantity,
  segment,
  amo,
  isLimitOrder,
  price,
  product = PRODUCT_TYPES.DELIVERY,
  locationHeader,
  stopLossTriggerPrice,
  ...rest
}) => {
  const body = {
    source: getSource(),
    txn_type: transactionType,
    exchange,
    segment,
    product,
    security_id: securityId,
    quantity,
    validity: 'DAY',
    order_type: isLimitOrder ? ORDER_TYPES.LMT : ORDER_TYPES.MKT,
    price: isLimitOrder ? parseFloat(price) : 0,
    trigger_price: stopLossTriggerPrice,
  };

  if (locationHeader) {
    defaultHeadersPlaceOrder['location-data'] = locationHeader;
  }

  return makeApiPostCall({
    url: EQUITIES.ORDERS.PLACE.POST_COVER,
    body: {
      ...body,
      ...rest,
    },
    headers: defaultHeadersPlaceOrder,
  });
};

export const placeBracketOrder = ({
  transactionType,
  exchange,
  securityId,
  quantity,
  segment,
  amo,
  isLimitOrder,
  price,
  product = PRODUCT_TYPES.DELIVERY,
  isSl,
  triggerPrice,
  locationHeader,
  stopLossPrice,
  targetPrice,
  ...rest
}) => {
  const body = {
    source: getSource(),
    txn_type: transactionType,
    exchange,
    segment,
    product,
    security_id: securityId,
    quantity,
    validity: 'DAY',
    order_type: getOrderType(isLimitOrder, isSl),
    price: isLimitOrder ? parseFloat(price) : 0,
    trigger_price: isSl ? triggerPrice : null,
    stoploss_value: stopLossPrice,
    profit_value: targetPrice,
  };
  if (locationHeader) {
    defaultHeadersPlaceOrder['location-data'] = locationHeader;
  }
  return makeApiPostCall({
    url: EQUITIES.ORDERS.PLACE.POST_BRACKET,
    body: {
      ...body,
      ...rest,
    },
    headers: defaultHeadersPlaceOrder,
  });
};

function getOrderType(isLimit, isSL) {
  if (isLimit && isSL) {
    return ORDER_TYPES.SL;
  }

  if (!isLimit && isSL) {
    return ORDER_TYPES.SLM;
  }

  if (isLimit && !isSL) {
    return ORDER_TYPES.LMT;
  }

  if (!isLimit && !isSL) {
    return ORDER_TYPES.MKT;
  }

  return ORDER_TYPES.MKT;
}

export const getMargin = ({
  securityId,
  exchange,
  segment,
  productType,
  txn_type,
  isLimitOrder,
  price,
  quantity,
  axiosSource,
}) => {
  const headers = getGenericAppHeaders();
  return makeApiGetCall({
    url: PRICES_URL.GET_MARGIN,
    headers,
    queryParams: {
      source: getSource(),
      segment,
      exchange,
      security_id: securityId,
      txn_type,
      quantity,
      product: productType,
      trigger_price: 0,
      price: isLimitOrder ? price : 0,
    },
    axiosSource,
  });
};

export function postChargesInfo(postData) {
  /*
   * postData -> exchange, price, product_type, qty, transaction_type, brokerage_profile_code, instrument_type,
   */
  return makeApiPostCall({
    url: EQUITIES.CHARGES_INFO.POST,
    body: {
      ...postData,
    },
    headers: getGenericAppHeaders(),
  });
}