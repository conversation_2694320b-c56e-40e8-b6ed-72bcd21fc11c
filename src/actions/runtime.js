import axios from 'axios';
import { GENERIC_API_URL } from '../config/urlConfig';
import { getGenericAppHeaders } from '../utils/apiUtil';
import { generateUniqSerial } from '../utils/commonUtil';

export async function sendErrorToBackend({
  data = 'Something went wrong',
  appName = 'pml-widgets',
  level = 'error',
  key = 'api_error',
  page = window?.location?.href || '',
}) {
  const response = await axios.post(
    GENERIC_API_URL.APP_LOG,
    {
      app: appName,
      logs: [
        {
          level,
          key,
          timestamp: new Date()?.toISOString(),
          data: data?.stack || data,
          page,
        },
      ],
    },
    {
      headers: {
        ...getGenericAppHeaders(),
        'x-request-id': generateUniqSerial(16),
        auth2: 'VM1jiFAqAO48l3qZgSZH',
      },
    },
  );
  return response;
}
