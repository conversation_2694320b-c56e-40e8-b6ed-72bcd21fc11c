import { FAV_API_URLS } from '../config/urlConfig';
import {
  getGenericAppHeaders,
  makeApiGetCall,
  makeApiPostCall,
} from '../utils/apiUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';

export const createWatchlist = async (miniapp_watchlist, axiosSource) => {
  const url = FAV_API_URLS.CREATE_WATCHLIST();
  const headers = getGenericAppHeaders();
  const body = {
    name: miniapp_watchlist,
  };
  try {
    const response = await makeApiPostCall({
      url,
      headers,
      axiosSource,
      body,
    });
    return response.data;
  } catch (error) {
    AxiosErrorHandler(error, false, false, false, true);
    return error?.response?.data;
  }
};

export const getAllWatchlist = async (axiosSource) => {
  const url = FAV_API_URLS.GET_ALL_WATCHLIST();
  const headers = getGenericAppHeaders();
  try {
    const { data } = await makeApiGetCall({
      url,
      headers,
      axiosSource,
    });
    return data.data;
  } catch (error) {
    if (error.response?.status === 404) {
      const createWishlistApiData = await createWatchlist('My Watchlist');
      const currentDate = new Date();
      return {
        watchlist_count: 1,
        watchlists: [
          {
            created_at: currentDate.toISOString(),
            id: createWishlistApiData.data.watchlist.id,
            name: 'My Watchlist',
            security_count: 0,
          },
        ],
      };
    }
    AxiosErrorHandler(error, false, false, false, true);
    return {
      watchlist_count: 0,
      watchlists: [],
    };
  }
};
