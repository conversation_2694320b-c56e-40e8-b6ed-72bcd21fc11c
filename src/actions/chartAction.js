import { makeApiPostCall, getGenericAppHeaders } from '../utils/apiUtil';
import { PRICE_CHART_API } from '../config/urlConfig';
import { errorLog } from '../utils/commonUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';

export const getPriceChart = async (body, axiosSource) => {
  const url = PRICE_CHART_API;
  const headers = getGenericAppHeaders();
  try {
    const response = await makeApiPostCall({
      url,
      headers,
      axiosSource,
      body,
    });
    return response.data;
  } catch (error) {
    errorLog('error', error);
    AxiosErrorHandler(error, false, false, false, true);
    return error;
  }
};
