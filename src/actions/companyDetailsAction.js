import { makeApiGetCall, getGenericAppHeaders } from '../utils/apiUtil';
import { COMPANY_DETAILS_API } from '../config/urlConfig';
import { errorLog } from '../utils/commonUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';

export const getSingleCompanyDetails = async (company_id, axiosSource) => {
  const url = COMPANY_DETAILS_API.GET_COMPANY_DETAILS(company_id);
  const headers = getGenericAppHeaders();
  try {
    const response = await makeApiGetCall({
      url,
      headers,
      axiosSource,
    });
    return response.data;
  } catch (error) {
    errorLog('error', error);
    AxiosErrorHandler(error, false, false, true);
    return error;
  }
};
