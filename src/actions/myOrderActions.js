import { ORDERS_API_URLS, GENERIC_API_URL } from '../config/urlConfig';
import {
  getGenericAppHeaders,
  makeApiGetCall,
  makeApiPutCall,
} from '../utils/apiUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';

export const getOrderBookDetails = async () => {
  const headers = {
    ...getGenericAppHeaders(),
    channel: 'Paytm',
  };
  const url = ORDERS_API_URLS.ORDER_BOOK_DETAILS;
  // setLoaderView(true);
  try {
    const request = await makeApiGetCall({
      headers,
      url,
    });
    //   setLoaderView(false);
    request.data.status = request?.status;
    return request.data;
  } catch (error) {
    //   setLoaderView(false);
    AxiosErrorHandler(error, false, false, false, true);
    return error.response;
  }
};

export const cancelPayment = async (transactionId, axiosSource) => {
  const url = GENERIC_API_URL.CANCEL_PAYMENT(transactionId);
  const headers = getGenericAppHeaders();
  try {
    const response = await makeApiPutCall({
      url,
      headers,
      axiosSource,
    });
    return response.data;
  } catch (error) {
    AxiosErrorHandler(error, false, false, false, true);
    return {};
  }
};
