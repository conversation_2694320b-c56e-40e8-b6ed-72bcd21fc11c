import { noOpFallback } from '../dataConfig';
import { request } from '../requests';
import { methods } from '../process';
import { apiCollections, caches } from '../subscriptions/dataConfig';

// eslint-disable-next-line no-undef
const showLogs = __BUILD__ !== 'prod';
const log = showLogs ? console.log.bind(console) : () => {};

function str2Uint8Array(str) {
  const { length } = str;
  const bufView = new Uint8Array(length);
  for (let i = 0; i < length; i += 1) {
    bufView[i] = str.charCodeAt(i);
  }
  return bufView;
}

export default class SocketConnection {
  constructor(
    { url, options, decode, apis, cachedApisKey },
    { onOpen, onClose, onMessage, onError },
  ) {
    this.url = url;
    this.options = options;
    this.connectionAttempts = 0;
    this.active = false;
    this.connected = false;
    this.reconnecting = false;
    this.closing = false;
    this.subscriptions = {};
    this.request = request;
    this.decode = methods[decode];
    this.apis = apiCollections[apis] || {};
    this.cachedApisKey = caches[cachedApisKey] || {};
    this.hasApis = Object.keys(this.apis).length > 0;
    this.onOpenCallback = noOpFallback(onOpen);
    this.onCloseCallback = noOpFallback(onClose);
    this.onMessageCallback = noOpFallback(onMessage);
    this.onErrorCallback = noOpFallback(onError);
    this.pause = false;

    this.onOpen = this.onOpen.bind(this);
    this.onClose = this.onClose.bind(this);
    this.onMessage = this.onMessage.bind(this);
    this.onError = this.onError.bind(this);
    this.sendRequest = this.sendRequest.bind(this);
    this.initializeSocket = this.initializeSocket.bind(this);
    this.resendRequests = this.resendRequests.bind(this);
    this.reInitializeSocket = this.reInitializeSocket.bind(this);

    this.initializeSocket();
  }

  initializeSocket() {
    if (!this.connected) {
      log('Initializing', JSON.stringify({ url: this.url }));
      this.socket = new WebSocket(this.url);
      this.connectionAttempts += 1;
      this.active = true;

      this.socket.onopen = this.onOpen;
      this.socket.onclose = this.onClose;
      this.socket.onmessage = this.onMessage;
      this.socket.onerror = this.onError;
    }
  }

  pauseConnection() {
    try {
      this.pause = true;
      this.active = false;
      this.socket.close();
      this.connected = false;
      this.options.reconnect = false;
      return true;
    } catch (error) {
      return false;
    }
  }

  closeSocket() {
    try {
      this.active = false;
      this.closing = true;
      this.socket.close();
      this.connected = false;
      this.connectionAttempts = 0;
      return true;
    } catch (error) {
      console.error({ url: this.url, error });
      return false;
    }
  }

  reInitializeSocket() {
    this.pause = false;
    this.options.reconnect = true;
    // this.initializeSocket();
    this.reconnectSocket();
  }

  reconnectSocket() {
    try {
      this.socket.close();
      this.connectionAttempts = 0;
      return true;
    } catch (error) {
      console.error({ url: this.url, error });
      return false;
    }
  }

  internalSendRequest(type, params, reconnectFeeds) {
    if (this.connected === true) {
      log(
        'Send request',
        JSON.stringify({
          type,
          params,
          url: this.url,
          reconnectFeeds,
        }),
      );
      const data = this.request[type](params);
      this.socket.send(data);
    }
  }

  sendRequest(type, params) {
    // log({ type, params: JSON.stringify(params) });
    if (!this.active && !this.pause) {
      this.reconnecting = true;
      this.initializeSocket();
    }
    if (type && !this.pause) {
      const apiCache = this.apis[type];
      if (apiCache) {
        apiCache(params);
      }
      this.internalSendRequest(type, params, false);
    }
  }

  resendRequests() {
    log('Resending active requests');
    Object.keys(this.cachedApisKey).forEach(api => {
      const [type, params] = api.split('_');
      this.internalSendRequest(type, JSON.parse(params), true);
    });
  }

  onOpen(data) {
    log('Socket open', JSON.stringify({ url: this.url, data }));
    this.connected = true;
    this.onOpenCallback(JSON.stringify(data), this.connectionAttempts > 1);
    if (this.reconnecting) {
      this.resendRequests();
    }
    this.reconnecting = false;
  }

  onClose(data) {
    this.connected = false;
    let idleConnection = false;
    if (this.closing) {
      this.active = false;
    }
    if (
      this.active &&
      this.hasApis &&
      !Object.keys(this.cachedApisKey).length
    ) {
      log(
        'Idle disconnection',
        JSON.stringify({
          options: this.options,
          connectionAttempts: this.connectionAttempts,
          url: this.url,
          data,
        }),
      );
      this.active = false;
      idleConnection = true;
    }
    if (this.active && this.options.reconnect) {
      log(
        'Reconnecting',
        JSON.stringify({
          options: this.options,
          connectionAttempts: this.connectionAttempts,
          url: this.url,
          data,
        }),
      );
      this.reconnecting = true;
      this.initializeSocket();
    } else if (!idleConnection) {
      this.closing = false;
      log(
        'Disconnected',
        JSON.stringify({
          options: this.options,
          connectionAttempts: this.connectionAttempts,
          url: this.url,
          data,
        }),
      );
      this.onCloseCallback(data);
    }
  }

  onError(error) {
    console.error({ url: this.url, error });
    this.onErrorCallback(error);
  }

  onMessage(message) {
    try {
      if (typeof message.data === 'string') {
        const dataArray = this.decode(str2Uint8Array(message.data));
        dataArray.forEach(data => {
          this.onMessageCallback(data);
        });
        // } else if (message && typeof message.toJSON === 'function') {
        //   const data = this.decode(message.toJSON().data || []);
        //   this.onMessageCallback(data);
      } else {
        let fr = new FileReader();
        fr.onload = e => {
          const dataArray = this.decode(new Uint8Array(e.target.result));
          // log(JSON.stringify(data));
          dataArray.forEach(data => {
            this.onMessageCallback(data);
          });
          fr = null;
        };
        fr.readAsArrayBuffer(message.data);
      }
    } catch (error) {
      console.error({ error, url: this.url });
      this.onErrorCallback(error, this);
    }
  }
}
