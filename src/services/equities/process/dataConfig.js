import { TYPES, calculateLength } from '../dataConfig/typeConfig';

function encodeString(dataView, inputStr, { position, length }) {
  try {
    const str = inputStr.toString();
    const bufferStringLength = str.length;
    if (typeof length !== 'number' || length === 0) {
      throw new Error(`Missing or invalid buffer length specified: ${length}`);
    }
    if (length < bufferStringLength) {
      throw new Error(
        `Max string length supported by current array buffer is ${length}. "${str}" is too long`,
      );
    }
    const strEndPosition = position + bufferStringLength;
    const endPosition = position + length;
    for (let i = 0; i < str.length; i += 1) {
      dataView.setUint8(position + i, str.charCodeAt(i), true);
    }
    for (let i = strEndPosition; i < endPosition; i += 1) {
      dataView.setUint8(i);
    }
    // eslint-disable-next-line no-empty
  } catch (err) {}
}

function decodeString(dataView, { position, length }) {
  if (typeof length !== 'number' || length === 0) {
    throw new Error(`Missing or invalid buffer length specified: ${length}`);
  }
  return String.fromCharCode
    .apply(
      null,
      new Uint8Array(dataView.buffer.slice(position, position + length)),
    )
    .replace(/[\u0000]/g, ''); // eslint-disable-line no-control-regex
}

function encodeValue(dataView, { type, position, length, arrayType, value }) {
  switch (type) {
    case TYPES.STRING:
      return encodeString(dataView, value, { position, length });
    case TYPES.ARRAY:
      return encodeArray(dataView, value, { arrayType, position, length }); // eslint-disable-line no-use-before-define
    default:
      return dataView[`set${type}`](position, value, true);
  }
}

export function decodeValue(dataView, { type, position, length, arrayType }) {
  switch (type) {
    case TYPES.STRING:
      return decodeString(dataView, { position, length });
    case TYPES.ARRAY:
      return decodeArray(dataView, { arrayType, position, length }); // eslint-disable-line no-use-before-define
    default:
      return dataView[`get${type}`](position, true);
  }
}

export function encodeConfig(dataView, struct, inputParams, offset = 0) {
  const parameters = [...inputParams];
  struct.forEach(({ position, ...config }) => {
    const value = parameters.shift();
    encodeValue(dataView, { position: position + offset, ...config, value });
  });

  return dataView;
}

// To be tested
function encodeArray(
  dataView,
  { position, length, arrayType, value, arrayContentLength },
) {
  const finalLength = Math.min(length, value.length);
  for (let i = 0; i < finalLength; i += 1) {
    encodeConfig(
      dataView,
      arrayType,
      value[i],
      position + i * arrayContentLength,
    );
  }
}

export function decodeArray(dataView, { position, length, arrayType }) {
  const output = [];
  const contentLength = calculateLength(arrayType);
  for (let i = 0; i < length; i += 1) {
    // eslint-disable-next-line no-use-before-define
    const { payload } = decodeSegment(
      dataView,
      arrayType,
      position + i * contentLength,
    );
    output.push(payload);
  }
  return output;
}

export function preprocessFixedValues(dataView, inputStruct) {
  const struct = [];
  inputStruct.forEach(config => {
    if (config.value !== undefined) {
      encodeValue(dataView, config);
    } else {
      struct.push(config);
    }
  });

  return struct;
}

export function addRequestConfig(struct, code, length) {
  if (!code || !length) {
    return struct;
  }
  const [requestType, requestPayload, ...others] = struct;
  return [
    { ...requestType, value: code },
    { ...requestPayload, value: length },
    ...others,
  ];
}

export function decodeSegment(segment, struct, offset = 0) {
  const payload =
    struct &&
    struct.map(({ position, ...config }) =>
      decodeValue(segment, {
        ...config,
        position: position + offset,
      }),
    );
  return { payload };
}

export function formatPayload(struct, data) {
  const formattedData = {};
  struct.forEach(({ arrayType, key }, index) => {
    let finalKey;
    let finalValue;
    if (key) {
      if (arrayType) {
        finalKey = key;
        finalValue = data[index].map(item => formatPayload(arrayType, item));
      } else if (typeof key === 'string') {
        finalKey = key;
        finalValue = data[index];
      } else if (typeof key === 'function') {
        const { key: internalKey, value } = key(data, struct);
        finalKey = internalKey;
        finalValue = value;
      }

      formattedData[finalKey] = finalValue;
    }
  });

  return formattedData;
}

const processedStructs = {};

export function addProcessedStruct(code, { struct }) {
  processedStructs[code] = struct;
}

export function getProcessedStruct(code) {
  return processedStructs[code] || [];
}
