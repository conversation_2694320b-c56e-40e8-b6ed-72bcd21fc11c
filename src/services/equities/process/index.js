import { RESPONSES, CODE_REVERSE_MAP } from '../dataConfig';
import {
  addRequestConfig,
  encodeConfig,
  preprocessFixedValues,
  decodeSegment,
  formatPayload,
  addProcessedStruct,
  getProcessedStruct,
} from './dataConfig';

function processDataHeader(header) {
  const [, , , messageLength, responseCode] = header;
  return { messageLength, responseCode };
}

function processOrderHeader(header) {
  const [responseCode, messageLength] = header;
  return { messageLength, responseCode };
}

function decodeStream(headerStruct, processMessageHeader) {
  return array => {
    const buffer = Uint8Array.from(array);
    const stream = new DataView(buffer.buffer);
    const response = [];
    let currentPosition = 0;
    const streamLength = stream.byteLength;

    while (currentPosition < streamLength) {
      const { payload: header } = decodeSegment(
        stream,
        headerStruct,
        currentPosition,
      );
      const { messageLength, responseCode } = processMessageHeader(header);
      if (RESPONSES[responseCode]) {
        if (streamLength < currentPosition + messageLength) {
          console.error('Message length mismatch', {
            array,
            streamLength,
            currentPosition,
            messageLength,
            responseCode,
            response,
          });
        } else {
          const struct = RESPONSES[responseCode];
          const { payload } = decodeSegment(stream, struct, currentPosition);
          const formattedData = formatPayload(
            [...headerStruct, ...RESPONSES[responseCode]],
            [...header, ...payload],
          );
          response.push({
            [CODE_REVERSE_MAP[responseCode]]: formattedData,
          });
        }
      } else {
        console.error(`Unrecognised message code ${responseCode}`);
        console.error(`Invalid message length ${messageLength}`);
        if (!messageLength) break;
      }
      currentPosition += messageLength;
    }

    return response;
  };
}

const methods = {
  decodeDataStream: decodeStream(RESPONSES.DATA_HEADER, processDataHeader),
  decodeOrderStream: decodeStream(RESPONSES.ORDER_HEADER, processOrderHeader),
};

const keys = {};
Object.keys(methods).forEach(key => {
  keys[key] = key;
});

export {
  addRequestConfig,
  encodeConfig,
  preprocessFixedValues,
  addProcessedStruct,
  getProcessedStruct,
  methods,
  keys,
};
