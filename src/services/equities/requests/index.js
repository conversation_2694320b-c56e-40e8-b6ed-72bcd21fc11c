import { REQUEST_CODES, RESPONSE_TYPES } from '../dataConfig';
import { generateTypes } from '../dataConfig/typeConfig';
import {
  constructRequest,
  generateRequest,
  stockFilterFn,
  indexFilterFn,
  feedFilter,
} from './dataConfig';

export const request = {
  // startFeed: constructRequest(REQUEST_CODES.START_FEED),
  // stopFeed: constructRequest(REQUEST_CODES.STOP_FEED),
  authenticate: constructRequest(REQUEST_CODES.AUTH),
  addStock: constructRequest(REQUEST_CODES.ADD_STOCK),
  delStock: constructRequest(REQUEST_CODES.DEL_STOCK),
  addDepth: constructRequest(REQUEST_CODES.ADD_DEPTH),
  delDepth: constructRequest(REQUEST_CODES.DEL_DEPTH),
  addIndex: constructRequest(REQUEST_CODES.ADD_INDEX),
  delIndex: constructRequest(REQUEST_CODES.DEL_INDEX),
  heartbeat: constructRequest(REQUEST_CODES.HEARTBEAT),
};
export const stockFields = [
  RESPONSE_TYPES.TRADE,
  RESPONSE_TYPES.TOP_BID_ASK,
  RESPONSE_TYPES.OHLC,
  RESPONSE_TYPES.CIRCUIT_LIMIT,
  RESPONSE_TYPES.DEPTH,
  RESPONSE_TYPES.P_CLOSE,
  RESPONSE_TYPES.HIGH_LOW,
];
export const depthFields = [RESPONSE_TYPES.DEPTH];
export const indexFields = [RESPONSE_TYPES.INDEX, RESPONSE_TYPES.HIGH_LOW];
export const marketStatusFields = [RESPONSE_TYPES.MARKET_STATUS];
export const orderFields = [RESPONSE_TYPES.ORDER];

export const requestGenerator = {
  stock: generateRequest(
    stockFields,
    'addStock',
    'delStock',
    stockFilterFn,
    true,
  ),
  depth: generateRequest(
    depthFields,
    'addDepth',
    'delDepth',
    stockFilterFn,
    true,
  ),
  index: generateRequest(
    indexFields,
    'addIndex',
    'delIndex',
    indexFilterFn,
    true,
  ),
  marketStatus: generateRequest(marketStatusFields, null, null),
  order: generateRequest(orderFields, null, null),
};

export const REQUEST_TYPES = generateTypes(requestGenerator);

export { feedFilter };
