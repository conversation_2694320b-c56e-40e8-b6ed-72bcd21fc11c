import { REQUESTS } from '../dataConfig';
import { createStruct } from '../dataConfig/typeConfig';
import {
  addRequestConfig,
  encodeConfig,
  preprocessFixedValues,
  addProcessedStruct,
  getProcessedStruct,
} from '../process';

export function constructRequest({
  code,
  header = REQUESTS.HEADER,
  struct = [],
  position = 83,
  length = position,
}) {
  const dataView = new DataView(new ArrayBuffer(length));
  if (struct.length) {
    struct[0].position = struct[0].position || position; // eslint-disable-line no-param-reassign
  }
  addProcessedStruct(
    code,
    createStruct([...addRequestConfig(header, code, length), ...struct]),
  );

  return inputParams =>
    encodeConfig(
      dataView,
      preprocessFixedValues(dataView, getProcessedStruct(code)),
      inputParams,
    );
}

export function stockFilterFn(payload) {
  return packet => {
    if (!packet || typeof packet !== 'object') {
      return false;
    }
    return packet.securityId === payload[1] && packet.exchangeId === payload[0];
  };
}

export function indexFilterFn(payload) {
  return packet => {
    if (!packet || typeof packet !== 'object') {
      return false;
    }
    return packet.securityId === payload[0];
  };
}

export function passThroughFilterFn() {
  return () => true;
}

export function feedFilter(feeds, allowedFeeds) {
  return allowedFeeds.map(feedType =>
    feeds.find(({ type }) => type === feedType),
  );
}

export function generateRequest(
  allowedFeeds,
  subscribe,
  unsubscribe,
  filterFn = passThroughFilterFn,
  storeInitialValue = false,
) {
  return (feeds, payload) => [
    [],
    feedFilter(feeds, allowedFeeds),
    {
      subscribe,
      unsubscribe,
      filterFn: filterFn(payload),
    },
    storeInitialValue,
    payload || [],
  ];
}
