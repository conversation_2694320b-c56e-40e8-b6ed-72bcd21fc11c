import { filter } from 'rxjs/operators';

import { shareObservable } from './dataConfig';
import { noOpFallback } from '../dataConfig';
import { feedFilter } from '../requests';

export default class Subscription {
  constructor(
    streams,
    { subscribe, unsubscribe, filterFn },
    storeInitialValue,
    payload,
    onComplete,
  ) {
    this.subscribe = subscribe;
    this.unsubscribe = unsubscribe;
    this.payload = payload;
    this.onComplete = noOpFallback(onComplete);
    this.streams = streams.map(({ type, feed }) => ({
      type,
      feed: shareObservable(
        feed().pipe(filter(filterFn)), // Filter for requested payload
        { onUnsubscribe: this.removeSubscriber.bind(this), storeInitialValue },
      ),
    }));
    this.subscriberCount = 0;
  }

  addSubscriber(requestedStreams = []) {
    const feeds = feedFilter(this.streams, requestedStreams); // Filter for requested streams
    this.subscriberCount += feeds.filter(feed => feed).length;

    return feeds.map(feed =>
      feed ? { type: feed.type, feed: feed.feed() } : feed,
    );
  }

  removeSubscriber() {
    if (this.subscriberCount > 0) {
      this.subscriberCount -= 1;
    }
    if (this.subscriberCount === 0) {
      this.destroySubscriber();
    }
  }

  destroySubscriber() {
    this.subscriberCount = 0;
    this.onComplete();
  }
}
