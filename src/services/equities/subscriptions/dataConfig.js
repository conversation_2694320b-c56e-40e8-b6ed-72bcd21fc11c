import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { RESPONSE_TYPES } from '../dataConfig';

export function shareObservable(
  inputObservable,
  {
    onUnsubscribe = null,
    onNoSubscribers = null,
    storeInitialValue = false,
  } = {},
) {
  let observers = [];
  let initialValue;
  const localObservable = inputObservable.subscribe(data => {
    if (storeInitialValue) {
      initialValue = data;
    }
    observers.forEach(({ observer }) => {
      if (observer && observer.next) {
        observer.next(data);
      }
    });
  });
  let refs = 0;
  return () => {
    refs += 1;
    const currentRef = refs;
    return new Observable(observer => {
      observers.push({ observer, ref: currentRef });
      if (initialValue) {
        observer.next(initialValue);
      }
    }).pipe(
      finalize(() => {
        observers = observers.filter(({ ref }) => ref !== currentRef);
        if (onUnsubscribe) {
          onUnsubscribe(observers.length);
        }
        if (onNoSubscribers && observers.length === 0) {
          localObservable.unsubscribe();
          onNoSubscribers();
        }
      }),
    );
  };
}

export function generateFeeds() {
  const keys = Object.values(RESPONSE_TYPES);
  const feedMap = {};
  const feedArray = keys.map(type => {
    const feed = {
      type,
    };
    feed.feed = shareObservable(
      new Observable(observer => {
        feed.observer = observer;
        feedMap[type] = observer;
      }),
    );
    return feed;
  });

  return {
    feedMap,
    feedArray,
  };
}

const dataCachedApis = {};
const caches = { dataCachedApis };
const cacheKeys = {};

Object.keys(caches).forEach(key => {
  cacheKeys[key] = key;
});

function addCache(type) {
  return payload => {
    const key = `${type}_${JSON.stringify(payload)}`;
    dataCachedApis[key] = true;
    // eslint-disable-next-line no-console
    // log({ dataCachedApis });
    return key;
  };
}

function delCache(type) {
  return payload => {
    const key = `${type}_${JSON.stringify(payload)}`;
    delete dataCachedApis[key];
    // eslint-disable-next-line no-console
    // log({ dataCachedApis });
    return key;
  };
}

const dataApis = {
  addStock: addCache('addStock'),
  delStock: delCache('addStock'),
  addDepth: addCache('addDepth'),
  delDepth: delCache('addDepth'),
  addIndex: addCache('addIndex'),
  delIndex: delCache('addIndex'),
};

const apiCollections = { dataApis };

const apiKeys = {};

Object.keys(apiCollections).forEach(key => {
  apiKeys[key] = key;
});

export { caches, cacheKeys, apiCollections, apiKeys };
