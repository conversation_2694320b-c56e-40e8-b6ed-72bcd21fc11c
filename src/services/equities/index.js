import { Subject } from 'rxjs';
import { RESPONSE_TYPES } from './dataConfig';
import { apiKeys, cacheKeys } from './subscriptions/dataConfig';
import SubscriptionManager, {
  eventTypes,
} from './subscriptions/SubscriptionManager';
import { keys } from './process';
import {
  REQUEST_TYPES,
  stockFields,
  depthFields,
  indexFields,
  orderFields,
} from './requests';
import { BROADCAST_URL } from '../../config/envConfig';

const defaultOptions = { reconnect: true, maxReconnectCount: 10 };

export const lastPacketSubject = new Subject();

export const feeds = {
  data: new SubscriptionManager(
    BROADCAST_URL.DATA_BROADCAST,
    keys.decodeDataStream,
    defaultOptions,
    apiKeys.dataApis,
    cacheKeys.dataCachedApis,
    lastPacketSubject,
  ),
  order: new SubscriptionManager(
    BROADCAST_URL.ORDER_BROADCAST,
    keys.decodeOrderStream,
    defaultOptions,
  ),
};

export {
  RESPONSE_TYPES,
  REQUEST_TYPES,
  eventTypes,
  stockFields,
  depthFields,
  indexFields,
  orderFields,
};
