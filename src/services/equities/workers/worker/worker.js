/* eslint-disable no-param-reassign */
import SocketConnection from '../../stream';
import { marketStatusFields, stockFields, indexFields } from '../../requests';

const defaultCacheKey = 'default';

const INDEX_EXCHANGE_ID = 0;

function addValueToCacheKey(caches, cacheKey, key, value) {
  if (caches[cacheKey]) {
    caches[cacheKey][key] = value;
  } else {
    caches[cacheKey] = { [key]: value };
  }
}

function extractCacheKeys(payload) {
  const caches = {};
  Object.entries(payload).forEach(([key, value]) => {
    let cacheKey = key || defaultCacheKey;
    if (stockFields.includes(key) || indexFields.includes(key)) {
      if (stockFields.includes(key)) {
        const { exchangeId, securityId } = value;
        cacheKey = `[${exchangeId},${securityId}]`;
        if (exchangeId !== INDEX_EXCHANGE_ID)
          addValueToCacheKey(caches, cacheKey, key, value);
      }
      if (indexFields.includes(key)) {
        const { securityId, exchangeId } = value;
        cacheKey = `[${securityId}]`;
        if (exchangeId === INDEX_EXCHANGE_ID)
          addValueToCacheKey(caches, cacheKey, key, value);
      }
    } else {
      addValueToCacheKey(caches, cacheKey, key, value);
    }
  });

  return caches;
}

function generateCacheKey(payload, type) {
  if (payload && payload.length) {
    return `${JSON.stringify(payload)}`;
  }

  return type || defaultCacheKey;
}

export default class WorkerWrapper {
  constructor(currentTarget, data) {
    this.url = data.url;
    this.cache = {
      [marketStatusFields[0]]: {
        data: null, // or use {}
        subscriptions: {},
      },
    };
    this.connectedClient = currentTarget;

    this.onMessage = this.sendMessage.bind(this);

    this.onOpen = payload => {
      if (this.authenticated) {
        const { type, payload: pl } = this.authenticated;
        this.socket.sendRequest(type, pl);
      } else {
        this.connectedClient.postMessage({
          method: 'onOpen',
          url: this.url,
          payload,
        });
      }
    };

    this.onClose = payload => {
      this.connectedClient.postMessage({
        method: 'onClose',
        url: this.url,
        payload: JSON.stringify(payload),
      });
    };

    this.onError = payload => {
      this.connectedClient.postMessage({
        method: 'onError',
        url: this.url,
        payload: JSON.stringify(payload),
      });
    };

    this.socket = new SocketConnection(data, this);
  }

  sendMessage(incomingPayload) {
    const { cache } = this;
    const caches = extractCacheKeys(incomingPayload);
    Object.entries(caches).forEach(([cacheKey, payload]) => {
      if (cache[cacheKey]) {
        const { data } = cache[cacheKey];
        cache[cacheKey].data = { ...data, ...payload };
        this.connectedClient.postMessage({
          method: 'onMessage',
          url: this.url,
          payload,
        });
      }
      this.connectedClient.postMessage({
        method: 'onMessage',
        url: this.url,
      });
    });
    this.connectedClient.postMessage({
      method: 'onMessage',
      url: this.url,
      incomingPayload,
    });
  }

  addSubscription(type, payload) {
    const { cache, socket } = this;
    const cacheKey = generateCacheKey(payload, type);

    if (!cache[cacheKey]) {
      cache[cacheKey] = {
        data: null,
        subscriptions: {},
      };
    }
    if (!cache[cacheKey].subscriptions[type]) {
      socket.sendRequest(type, payload);
      cache[cacheKey].subscriptions[type] = true;
    } else {
      this.connectedClient.postMessage({
        method: 'onMessage',
        url: this.url,
        payload: cache[cacheKey].data,
      });
    }
  }

  removeSubscription(type, payload) {
    const { cache, socket } = this;
    const cacheKey = generateCacheKey(payload, type);
    const subType = type.replace('del', 'add');

    if (cacheKey !== marketStatusFields[0]) {
      socket.sendRequest(type, payload);
      delete cache[cacheKey].subscriptions[subType];
    }
    if (cacheKey !== marketStatusFields[0]) {
      if (Object.keys(cache[cacheKey].subscriptions).length === 0) {
        // no one subscribing to this data cache
        delete cache[cacheKey];
      }
    }
  }

  addClient(currentTarget) {
    this.connectedClient = currentTarget;
    this.connectedClient.postMessage({
      method: 'onOpen',
      url: this.url,
      payload: 'Connected',
    });
  }

  closeConnection() {
    Object.entries(this.cache).forEach(([inpPayload, { subscriptions }]) => {
      let payload;
      if (inpPayload === marketStatusFields[0]) {
        [payload] = marketStatusFields;
      } else {
        payload = JSON.parse(inpPayload);
      }
      Object.keys(subscriptions).forEach(type => {
        this.removeSubscription(type, payload);
      });
    });
    delete this.connectedClient;
  }

  reconnect() {
    this.socket.reconnectSocket();
  }

  reInitializeSocket() {
    this.socket.reInitializeSocket();
  }

  pauseConnection() {
    this.socket.pauseConnection();
  }
}
