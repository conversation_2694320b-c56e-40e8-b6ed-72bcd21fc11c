import WorkerWrapper from './worker';

let webSocket;

// eslint-disable-next-line no-undef

onmessage = messageEvent => {
  const { target } = messageEvent;
  const { method, payload: data } = messageEvent.data;
  if (method === 'initializeSocket') {
    if (!webSocket) {
      // Create connection
      webSocket = new WorkerWrapper(target, data);
      webSocket.authenticated = false;
    } else {
      // Add client
      webSocket.addClient(target);
    }
  } else if (webSocket.socket[method] && data) {
    const [type, payload] = data;
    switch (type) {
      case 'addStock':
      case 'addIndex':
      case 'addDepth': {
        webSocket.addSubscription(type, payload);
        break;
      }
      case 'delStock':
      case 'delIndex':
      case 'delDepth':
        webSocket.removeSubscription(type, payload);
        break;
      case 'authenticate':
        if (!webSocket.authenticated) {
          webSocket.socket[method](type, payload);
          webSocket.authenticated = { type, payload };
        }
        break;
      default:
        webSocket.socket[method](type, payload);
    }
  } else if (webSocket[method]) {
    webSocket[method](data || null);
  }
};
