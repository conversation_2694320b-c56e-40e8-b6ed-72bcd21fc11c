/* eslint-disable no-console */
import SocketConnection from '../stream';
import CustomWorker from './worker/index.worker';

const WORKER_TYPE = {
  WEB_WORKER: 'WEB_WORKER',
  NONE: 'NONE',
};
let workerType = null;
let worker = null;
let messageListeners = [];

function addCallback(callback) {
  messageListeners.push(callback);
}

function removeCallback(callback) {
  messageListeners = messageListeners.filter((cb) => callback !== cb);
}

function triggerCallbacks(data) {
  messageListeners.forEach((cb) => cb(data));
}

export default class SocketWrapper {
  constructor(payload, callbacks) {
    if (!workerType) {
      if (window.Worker) {
        workerType = WORKER_TYPE.WEB_WORKER;
        try {
          worker = new CustomWorker();
          worker.onmessage = triggerCallbacks;
          window.addEventListener('beforeunload', () => {
            worker.postMessage({ method: 'closeConnection', url: this.url });
            try {
              worker.close();
            } catch (e) {
              throw new Error(e);
            }
          });
        } catch (error) {
          console.error('Worker creation failed:', error);
          workerType = WORKER_TYPE.NONE;
          this.fallbackToNonWorker(payload, callbacks);
        }
      } else {
        workerType = WORKER_TYPE.NONE;
      }
    }
    switch (workerType) {
      case WORKER_TYPE.WEB_WORKER:
        this.url = payload.url;
        this.handleMessage = this.handleMessage.bind(this);
        addCallback(this.handleMessage);
        this.onOpen = callbacks.onOpen;
        this.onClose = callbacks.onClose;
        this.onMessage = callbacks.onMessage;
        this.onError = callbacks.onError;
        this.payload = payload;
        worker.postMessage({
          method: 'initializeSocket',
          url: this.url,
          payload: this.payload,
        });
        break;
      default:
        // Fallback to not using workers
        // Use dynamic import instead of static import
        this.fallbackToNonWorker(payload, callbacks);
    }
    workerType = WORKER_TYPE.NONE;
  }

  fallbackToNonWorker(payload, callbacks) {
    this.socket = new SocketConnection(payload, callbacks);
    this.closeSocket = this.socket.closeSocket;
    this.sendRequest = this.socket.sendRequest;
  }

  sendRequest(...payload) {
    worker.postMessage({
      method: 'sendRequest',
      url: this.url,
      payload,
    });
  }

  handleMessage(event) {
    const { method, url, payload } = event.data;
    if (url && this.url !== url) {
      return;
    }
    switch (method) {
      case 'onOpen':
        this.onOpen(payload.data);
        break;
      case 'sendRequest':
        break;
      default:
        if (typeof this[method] === 'function') {
          this[method](payload);
        }
    }
  }

  closeSocket() {
    worker.postMessage({ method: 'closeSocket' });
    removeCallback(this.handleMessage);
  }

  pauseConnection() {
    worker.postMessage({ method: 'pauseConnection', url: this.url });
  }

  reconnectSocket() {
    worker.postMessage({ method: 'reconnect', url: this.url });
  }

  reInitializeSocket() {
    worker.postMessage({ method: 'reInitializeSocket', url: this.url });
  }
}
