import { TYPES } from './typeConfig';

export const REQUESTS = {
  // HEADER
  HEADER: [
    { type: TYPES.BYTE }, // Request code as per request
    { type: TYPES.USHORT }, // Total length of packet to be sent
  ],

  // AUTH
  OLD_AUTH: [
    { position: 3, type: TYPES.STRING, length: 30 }, // Client id with which user is logging in application
    { position: 83, type: TYPES.STRING, length: 100 }, // Authorization header value, position 83
    { type: TYPES.STRING, length: 50 }, // x-pmngx-key header value, position 183
    { type: TYPES.STRING, length: 50 }, // x-sso-token header value, position 233
    //  { type: TYPES.STRING, length: 20, value: "NP" }, // Watchlist name, position 283
    { position: 303, type: TYPES.STRING, length: 20 }, // mobile platform(android/ios), position 303
    { type: TYPES.STRING, length: 20 }, // app_version, position 323
    { type: TYPES.STRING, length: 30 }, // device id, position 343
    { type: TYPES.STRING, length: 50 }, // mobile model name, position 373
    { type: TYPES.BYTE }, // mobile OS version, position 423
    { type: TYPES.STRING, length: 20 }, // user id, position 424, end position 444
  ],

  // AUTH
  AUTH: [
    { position: 3, type: TYPES.STRING, length: 30 }, // Client id with which user is logging in application
    { position: 83, type: TYPES.STRING, length: 100 }, // Authorization header value, position 83
    // { type: TYPES.STRING, length: 50 }, // 183 - RESERVED
    { position: 233, type: TYPES.STRING, length: 50 }, // x-pmngx-key header value, position 233
    { type: TYPES.STRING, length: 500 }, // x-sso-token header value, position 283
    // { type: TYPES.STRING, length: 50 }, // RESERVED 783
    // { type: TYPES.STRING, length: 20 }, // Watchlist 833
    { position: 853, type: TYPES.STRING, length: 20 }, // app_version, position 853 mobile platform(android/ios)
    { type: TYPES.STRING, length: 20 }, // device id, position 873 (APPLICATION VERSION)
    { type: TYPES.STRING, length: 30 }, // mobile model name, position 893 (DEVICE_ID)
    { type: TYPES.STRING, length: 50 }, // mobile OS version, position 923 (MOBILE MODEL NAME)
    { type: TYPES.BYTE }, // OS VERSION 953
    { type: TYPES.STRING, length: 20 }, // user id, position 954, end position 974 // 994
  ],
  // 994

  // StreamFeedAddStock && StreamFeedMbpAddStock
  ADD_DEL_STOCK: [
    { type: TYPES.BYTE }, // Exch Segid for which request in sent
    { type: TYPES.INT, value: -1 }, // Default value -1 kept for future use
    { type: TYPES.BYTE, value: 1 }, // Default value 1
    // { type: TYPES.STRING, length: 20, value: "NP" }, // Watchlist name
    { position: 109, type: TYPES.STRING, length: 20 }, // List containing scrip id to subscribe/unsubscribe. Note – Send only 1 record in list
  ],

  // StreamFeedIndexAddStock
  ADD_DEL_INDEX: [
    { type: TYPES.BYTE, value: 0 }, // Exch Segid used in other request. Use 0  here
    { type: TYPES.INT, value: -1 }, // Default value -1 kept for future use
    { type: TYPES.BYTE, value: 1 }, // Default value 1
    { position: 109, type: TYPES.STRING, length: 20 }, // List containing exchange id to subscribe/unsubscribe. Note – Send only 1 record in list
  ],
};

export const REQUEST_CODES = {
  // Request codes
  // START_FEED: { code: 10 }, // StreamFeedConnect
  // STOP_FEED: { code: 11 }, // StreamFeedDisconnect
  AUTH: { code: 40, length: 994, struct: REQUESTS.AUTH }, // StreamAuthFeedConnect
  ADD_STOCK: { code: 12, length: 129, struct: REQUESTS.ADD_DEL_STOCK }, // StreamFeedAddStock
  DEL_STOCK: { code: 13, length: 129, struct: REQUESTS.ADD_DEL_STOCK }, // StreamFeedDelStock
  ADD_DEPTH: { code: 23, length: 129, struct: REQUESTS.ADD_DEL_STOCK }, // StreamFeedMbpAddStock
  DEL_DEPTH: { code: 25, length: 129, struct: REQUESTS.ADD_DEL_STOCK }, // StreamFeedMbpDelStock
  ADD_INDEX: { code: 24, length: 129, struct: REQUESTS.ADD_DEL_INDEX }, // StreamFeedIndexAddStock
  DEL_INDEX: { code: 28, length: 129, struct: REQUESTS.ADD_DEL_INDEX }, // StreamFeedIndexDelStock
  HEARTBEAT: { code: 14 }, // Heartbeat
};

export const REQUEST_TYPES = {
  STOCK: 'stock',
  DEPTH: 'depth',
  INDEX: 'index',
  ORDER: 'order',
  MARKET_STATUS: 'marketStatus',
};
