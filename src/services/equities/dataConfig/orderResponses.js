import { TYPES, createStruct } from './typeConfig';

export const orderHeader = createStruct([
  { type: TYPES.SHORT }, // request code === 23
  { type: TYPES.SHORT }, // message length === 912
]);

const { length: position } = orderHeader;

export const order = createStruct([
  {
    // Exchange Id
    position,
    type: TYPES.STRING,
    length: 6,
    key: 'exchange',
  },
  { type: TYPES.STRING, length: 1, key: 'segment' }, // Segment
  { type: TYPES.STRING, length: 1, key: 'source' }, // Source ####
  { type: TYPES.STRING, length: 12, key: 'security_id' }, // Security Id ||PACK 2?
  { type: TYPES.STRING, length: 30, key: 'client_id' }, // Client Id
  { type: TYPES.STRING, length: 30, key: 'exch_order_no' }, // Exchange order number
  { type: TYPES.STRING, length: 30, key: 'order_no' }, // Order number
  { type: TYPES.STRING, length: 1, key: 'product' }, // Product
  { type: TYPES.STRING, length: 1, key: 'txn_type' }, // Transaction type
  { type: TYPES.STRING, length: 10, key: 'order_type' }, // Order type
  {
    // Validity
    type: TYPES.STRING,
    length: 5,
    pack: 1,
    key: 'validity',
  },
  { type: TYPES.INT, key: 'discloseQuantity' }, // Disclose Quantity ####
  { type: TYPES.INT, key: 'discloseQuantityRemaining' }, // Disclose Quantity Remaining  #####
  { type: TYPES.INT, key: 'remaining_quantity' }, // Quantity Remaining
  { type: TYPES.INT, key: 'quantity' }, // Quantity
  { type: TYPES.INT, key: 'traded_qty' }, // Total traded quantity
  { type: TYPES.DOUBLE, key: 'price' }, // Price
  { type: TYPES.DOUBLE, key: 'trigger_price' }, // Trigger price
  { type: TYPES.SHORT, pack: 2, key: 'serial_no' }, // Serial number
  { type: TYPES.DOUBLE, key: 'tradePrice' }, // Trade price #####
  { type: TYPES.DOUBLE, key: 'avg_traded_price' }, // Average trade price
  { type: TYPES.DOUBLE, key: 'algo_ord_no' }, // Algo order number
  { type: TYPES.SHORT, key: 'strategyId' }, // Strategy Id ####
  { type: TYPES.STRING, length: 1, key: 'off_mkt_flag' }, // Off Mkt flag
  { type: TYPES.STRING, length: 20, key: 'order_date_time' }, // Order entry datetime
  { type: TYPES.STRING, length: 20, key: 'exch_order_time' }, // Exchange order datetime
  { type: TYPES.STRING, length: 20, key: 'last_updated_time' }, // Last updated time
  { type: TYPES.STRING, length: 24, key: 'remarks' }, // Remarks  #####
  { type: TYPES.STRING, length: 4, key: 'mkt_type' }, // Market type
  {
    // Reason description
    type: TYPES.STRING,
    length: 300,
    pack: 1,
    key: 'reason_description',
  },
  { type: TYPES.SHORT, key: 'leg_no' }, // Leg number
  {
    // Market protection flag ####
    type: TYPES.STRING,
    length: 1,
    pack: 1,
    key: 'marketProtectionFlag',
  },
  { type: TYPES.DOUBLE, key: 'marketProtectionValue' }, // Market protection value  ####
  { type: TYPES.STRING, length: 1, key: 'participantType' }, // Participant type 1? ####
  { type: TYPES.STRING, length: 30, key: 'settlor' }, // Settlor ####
  { type: TYPES.STRING, length: 1, key: 'gtcFlag' }, // GTC Flag  ####
  { type: TYPES.STRING, length: 1, key: 'encashFlag' }, // Encash Flag  ####
  {
    // PAN  #####
    type: TYPES.STRING,
    length: 12,
    pack: 3,
    key: 'pan',
  },
  { type: TYPES.INT, key: 'group_id' }, // Group Id
  { type: TYPES.STRING, length: 10, key: 'instrument' }, // Instrument Name ####
  { type: TYPES.STRING, length: 15, key: 'symbol' }, // Symbol 1?  ####
  { type: TYPES.STRING, length: 20, key: 'productName' }, // Product name  ####
  { type: TYPES.STRING, length: 15, key: 'status' }, // Order status 1?
  { type: TYPES.INT, key: 'lot_size' }, // Lot size ###
  { type: TYPES.DOUBLE, key: 'slTrailing' }, // SL trailing ###
  { type: TYPES.DOUBLE, key: 'sl_abstick_value' }, // SL tick value
  { type: TYPES.DOUBLE, key: 'pr_abstick_value' }, // Profit price tick value
  { type: TYPES.DOUBLE, key: 'strike_price' }, // Strike price ####
  { type: TYPES.STRING, length: 20, key: 'expiry_date' }, // Expiry date ###
  { type: TYPES.STRING, length: 5, key: 'opt_type' }, // Option type 1? ####
  { type: TYPES.STRING, length: 40, key: 'display_name' }, // Custom symbol/display name
  { type: TYPES.STRING, length: 15, key: 'isin' }, // ISIN code 1?
  { type: TYPES.STRING, length: 3, key: 'series' }, // Series 1? ###
  { type: TYPES.STRING, length: 20, key: 'gtdDate' }, // GTD date ###
  {
    type: TYPES.STRING,
    length: 50,
    key: 'intrumentType',
    pack: 3,
  }, // Instrument type ####
  { type: TYPES.DOUBLE, key: 'ref_ltp' }, // Reference LTP
  { type: TYPES.DOUBLE, key: 'tick_size' }, // Tick Size
]);
