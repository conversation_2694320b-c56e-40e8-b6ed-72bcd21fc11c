import {
  dataHeader,
  trade,
  depth,
  ohlc,
  spread,
  index,
  topBidAsk,
  marketStatus,
  pClose,
  circuitLimit,
  highLow,
  /** Only available in TCP socket mode */
  // priceTicker,
  // journalMessage,
} from './dataResponses';
import { orderHeader, order } from './orderResponses';

function createReverseMap(codes) {
  const output = {};
  Object.entries(codes).forEach(([key, value]) => {
    output[value] = key;
  });

  return output;
}

export const RESPONSE_TYPES = {
  TRADE: 'TRADE',
  DEPTH: 'DEPTH',
  OHLC: 'OHLC',
  SPREAD: 'SPREAD',
  INDEX: 'INDEX',
  TOP_BID_ASK: 'TOP_BID_ASK',
  MARKET_STATUS: 'MARKET_STATUS',
  P_CLOSE: 'P_CLOSE',
  CIRCUIT_LIMIT: 'CIRCUIT_LIMIT',
  HIGH_LOW: 'HIGH_LOW',
  ORDER: 'ORDER',

  /** Only available in TCP socket mode */
  // PRICE_TICKER: 'PRICE_TICKER',
  // JOURNAL_MSG: 'JOURNAL_MSG',
};

export const RESPONSE_CODES = {
  // Response codes
  [RESPONSE_TYPES.ORDER]: 23, // ORDER
  [RESPONSE_TYPES.TRADE]: 1, // TRADE
  [RESPONSE_TYPES.DEPTH]: 2, // MBP
  [RESPONSE_TYPES.OHLC]: 3, // OHLC
  [RESPONSE_TYPES.SPREAD]: 4, // SPREAD
  [RESPONSE_TYPES.INDEX]: 5, // INDEX
  [RESPONSE_TYPES.TOP_BID_ASK]: 6, // TopBidAsk
  [RESPONSE_TYPES.MARKET_STATUS]: 29, // MARKET_STATUS
  [RESPONSE_TYPES.P_CLOSE]: 32, // PCLOSE
  [RESPONSE_TYPES.CIRCUIT_LIMIT]: 33, // CKT_LMT
  [RESPONSE_TYPES.HIGH_LOW]: 36, // F52WKHIGH_LOW

  /** Only available in TCP socket mode */
  // [RESPONSE_TYPES.PRICE_TICKER]: 30, // PRICE_TICKER
  // [RESPONSE_TYPES.JOURNAL_MSG]: 31, // JNRL_MSG
};

export const CODE_REVERSE_MAP = createReverseMap(RESPONSE_CODES);

export const RESPONSES = {
  ORDER_HEADER: orderHeader.struct,
  [RESPONSE_CODES.ORDER]: order.struct,

  DATA_HEADER: dataHeader.struct,
  [RESPONSE_CODES.TRADE]: trade.struct,
  [RESPONSE_CODES.DEPTH]: depth.struct,
  [RESPONSE_CODES.OHLC]: ohlc.struct,
  [RESPONSE_CODES.SPREAD]: spread.struct,
  [RESPONSE_CODES.INDEX]: index.struct,
  [RESPONSE_CODES.TOP_BID_ASK]: topBidAsk.struct,
  [RESPONSE_CODES.MARKET_STATUS]: marketStatus.struct,
  [RESPONSE_CODES.P_CLOSE]: pClose.struct,
  [RESPONSE_CODES.CIRCUIT_LIMIT]: circuitLimit.struct,
  [RESPONSE_CODES.HIGH_LOW]: highLow.struct,

  /** Only available in TCP socket mode */
  // [RESPONSE_CODES.PRICE_TICKER]: priceTicker.struct,
  // [RESPONSE_CODES.JOURNAL_MSG]: journalMessage.struct,
};
