export const TYPES = {
  BYTE: 'Uint8',
  SHORT: 'Int16',
  USHORT: 'Uint16',
  INT: 'Int32',
  UINT: 'Uint32',
  FLOAT: 'Float32',
  DOUBLE: 'Float64',
  STRING: 'string',
  ARRAY: 'array',
};

export const TYPE_SIZE_MAP = {
  [TYPES.BYTE]: 1,
  [TYPES.SHORT]: 2,
  [TYPES.USHORT]: 2,
  [TYPES.INT]: 4,
  [TYPES.UINT]: 4,
  [TYPES.FLOAT]: 4,
  [TYPES.DOUBLE]: 8,
  [TYPES.STRING]: 1,
};

export function calculateLength(config) {
  let length = 0;
  config.forEach(item => {
    length += TYPE_SIZE_MAP[item.type] || item.length;
  });
  return length;
}

export function createStruct(config) {
  const struct = [];
  let nextPosition = 0;
  config.forEach(
    ({ position, pack = 0, type, length, arrayType, ...payload }) => {
      const currentPosition = position || nextPosition;
      switch (type) {
        case TYPES.STRING:
          struct.push({
            position: currentPosition,
            type,
            length,
            ...payload,
          });
          nextPosition = currentPosition + length;
          break;
        case TYPES.ARRAY:
          {
            const arrayStruct = createStruct(arrayType, currentPosition);
            struct.push({
              position: currentPosition,
              type,
              length,
              arrayType: arrayStruct.struct,
              arrayContentLength: arrayStruct.length,
              ...payload,
            });
            nextPosition = currentPosition + length * arrayStruct.length;
          }
          break;
        default:
          struct.push({
            position: currentPosition,
            type,
            ...payload,
          });
          nextPosition = currentPosition + TYPE_SIZE_MAP[type];
      }
      nextPosition += pack;
    },
  );

  return { struct, length: nextPosition };
}

export function generateTypes(source) {
  const keys = Object.keys(source);
  const types = {};
  keys.forEach(key => {
    types[key.replace(/[A-Z]/g, letter => `_${letter}`).toUpperCase()] = key;
  });

  return types;
}
