import { TYPES, createStruct } from './typeConfig';

export const dataHeader = createStruct([
  { type: TYPES.BYTE, key: 'exchangeId' }, // Exch Seg code for which broadcast is received
  { type: TYPES.UINT, key: 'securityId' }, // ScripId for which broadcast is received
  { type: TYPES.UINT }, // 2nd ScripId. Will be present in case of Speard broadcast
  { type: TYPES.BYTE }, // Length of packet to be received
  { type: TYPES.BYTE }, // Msg code to identify what type of packet has arrived
]);

const { length: position } = dataHeader;

export const trade = createStruct([
  { position, type: TYPES.FLOAT, key: 'lastTradePrice' }, // Last trade of stock
  { type: TYPES.USHORT, key: 'lastTradeQuantity' }, // Last trade quantity of stock
  { type: TYPES.UINT, key: 'tradeVolume' }, // Total volume traded
  { type: TYPES.FLOAT, key: 'averageTradePrice' }, // Average trade price of stock
  { type: TYPES.INT, key: 'openInterest' }, // Open interest of contract will be 0 for PlaceOrder
  { type: TYPES.INT, key: 'lastTradeTime' }, // Last trade time
  { type: TYPES.INT, key: 'lastUpdatedTime' }, // Last update time
]);
export const depth = createStruct([
  {
    position,
    type: TYPES.ARRAY,
    length: 5,
    key: 'depth',
    arrayType: [
      { type: TYPES.UINT, key: 'bidQuantity' }, // Bid quantity
      { type: TYPES.UINT, key: 'askQuantity' }, // Ask quantity
      { type: TYPES.USHORT, key: 'buyQuantity' }, // Number of buy orders
      { type: TYPES.USHORT, key: 'sellQuantity' }, // Number of sell orders
      { type: TYPES.FLOAT, key: 'bidPrice' }, // Bid price
      { type: TYPES.FLOAT, key: 'askPrice' }, // Ask price
    ],
  },
]);
export const ohlc = createStruct([
  { position, type: TYPES.FLOAT, key: 'Open' }, // Opening price of stock
  { type: TYPES.FLOAT, key: 'pClose' }, // Closing price of stock
  { type: TYPES.FLOAT, key: 'High' }, // High price of stock for current day
  { type: TYPES.FLOAT, key: 'Low' }, // Low price of stock for current day
]);
export const spread = createStruct([
  { position, type: TYPES.FLOAT, key: 'lastTradePriceDiff' }, // Last trade price difference of spread pair
  { type: TYPES.UINT, key: 'volume' }, // Volume
  { type: TYPES.USHORT, key: 'lastTradeQuantity' }, // Last trade quantity
  { type: TYPES.INT, key: 'lastUpdatedTime' }, // Last upated time
  { type: TYPES.INT, key: 'lastTradeTime' }, // Last trade time
]);
export const index = createStruct([
  { position, type: TYPES.FLOAT, key: 'lastTradePrice' }, // Last trade price of index
  { type: TYPES.FLOAT, key: 'Open' }, // Opening price of index
  { type: TYPES.FLOAT, key: 'pClose' }, // Closing price of index
  { type: TYPES.FLOAT, key: 'High' }, // High price of index
  { type: TYPES.FLOAT, key: 'Low' }, // Low price of index
]);
export const topBidAsk = createStruct([
  { position, type: TYPES.UINT, key: 'totalSellQuantity' }, // Total sell quantity
  { type: TYPES.UINT, key: 'totalBuyQuantity' }, // Total buy quantity
  { type: TYPES.UINT, key: 'topBidQuantity' }, // Top bid quantity
  { type: TYPES.UINT, key: 'topAskQuantity' }, // Top ask quantity
  { type: TYPES.FLOAT, key: 'topBidPrice' }, // Top bid price
  { type: TYPES.FLOAT, key: 'topAskPrice' }, // Top ask price
]);
export const marketStatus = createStruct([
  {
    position,
    type: TYPES.STRING,
    length: 3,
    key: 'marketType',
  }, // Mkt_Type
  { type: TYPES.INT, key: 'marketStatus' }, // Mkt_Status
]);
export const pClose = createStruct([
  { position, type: TYPES.FLOAT, key: 'previousPrice' }, // Previous Price of Stock
  { type: TYPES.FLOAT, key: 'previousOpenInterest' }, // Previous Open Interest of Stock
]);
export const circuitLimit = createStruct([
  { position, type: TYPES.FLOAT, key: 'ucl' }, // Upper circuit limit
  { type: TYPES.FLOAT, key: 'lcl' }, // Lower circuit limit
]);
export const highLow = createStruct([
  { position, type: TYPES.FLOAT, key: 'yearHigh' }, // 52 week high of Stock
  { type: TYPES.FLOAT, key: 'yearLow' }, // 52 week low of Stock
]);

/** Only available in TCP socket mode */
// export const priceTicker = createStruct([
//   { position, type: TYPES.FLOAT, key: 'lastTradePrice' }, // Last trade price
// ]);
// export const journalMessage = createStruct([
//   { position, type: TYPES.USHORT, key: 'branchNumber' }, // BranchNumber
//   { type: TYPES.STRING, length: 5, key: 'brokerNumber' }, // BrokerNumber
//   { type: TYPES.STRING, length: 3, key: 'actionCode' }, // ActionCode
//   { type: TYPES.USHORT }, // MsgLength
//   { type: TYPES.STRING, length: 150, key: 'message' }, // Msg
//   { type: TYPES.INT, key: 'time' }, // Time
// ]);
