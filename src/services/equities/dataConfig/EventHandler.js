export default class EventHandler {
  constructor(types) {
    this.handlers = {};
    this.eventTypes = types;
  }

  triggerEventHandlers(type, data) {
    if (this.eventTypes[type] && this.handlers[type]) {
      this.handlers[type].forEach(handler => {
        handler({
          type,
          ...data,
        });
      });
    }
  }

  addEventListener(type, handler) {
    if (this.eventTypes[type]) {
      if (!this.handlers[type]) {
        this.handlers[type] = [];
      }
      this.handlers[type].push(handler);
    }
  }

  removeEventListener(type, handler) {
    if (this.eventTypes[type] && this.handlers[type]) {
      this.handlers[type] = this.handlers[type].filter(hn => hn !== handler);

      if (!this.handlers[type].length) {
        this.handlers[type] = null;
      }
    }
  }
}
