import { TYPES, TYPE_SIZE_MAP } from './typeConfig';
import { REQUEST_CODES, REQUESTS, REQUEST_TYPES } from './requestConfig';
import {
  RESPONSE_TYPES,
  RESPONSE_CODES,
  RESPONSES,
  CODE_REVERSE_MAP,
} from './responseConfig';

function noOp() {
  console.warn('noOp function called');
}

function noOpReturn(data) {
  console.warn('noOpReturn function called');
  return data;
}

function noOpFallback(fn, fallbackFn = noOp) {
  return typeof fn === 'function' ? fn : fallbackFn;
}

export {
  TYPES,
  TYPE_SIZE_MAP,
  REQUEST_CODES,
  REQUESTS,
  RESPONSE_TYPES,
  REQUEST_TYPES,
  RESPONSE_CODES,
  RESPONSES,
  CODE_REVERSE_MAP,
  noOp,
  noOpReturn,
  noOpFallback,
};
