function isDevBuild() {
  return document.body.dataset.developmentBuild === 'true';
}

function getCookieDomain() {
  const isDev = isDevBuild();
  return isDev ? 'localhost' : '.paytmmoney.com';
}

function setItem(
  name,
  value,
  { expires = null, domain = null, currentDomain = false } = {},
) {
  let cookieExpiry = null;
  if (typeof expires === 'number' || expires instanceof Date) {
    cookieExpiry = new Date(expires).toUTCString();
  }
  let cookieStr = `${name}=${value}; path=/`;
  if (!currentDomain) {
    const cookieDomain = domain || getCookieDomain();
    cookieStr += `; domain=${cookieDomain}`;
  }
  if (cookieExpiry) {
    cookieStr += `; expires=${cookieExpiry}`;
  }
  cookieStr += '; Secure';
  document.cookie = cookieStr;
}

function getItem(name) {
  const b = document.cookie.match(`(^|[^;]+)\\s*${name}\\s*=\\s*([^;]+)`);
  return b ? decodeURIComponent(b.pop()) : '';
}

function deleteItem(name) {
  const expires = new Date();
  setItem(name, '', { expires }); // clear cookie on .paytmmoney.com
  setItem(name, '', { expires, currentDomain: true }); // clear cookie on current domain
}

export default {
  getItem,
  setItem,
  deleteItem,
};
