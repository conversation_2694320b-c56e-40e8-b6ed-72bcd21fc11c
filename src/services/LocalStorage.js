function set(key, value, expiry = 24 * 60 * 60) {
  if (!(window && window.localStorage)) return;
  const finalObj = {
    value,
    createdAt: new Date().getTime(),
    expiry: expiry * 1000,
  };

  window.localStorage.setItem(key, JSON.stringify(finalObj));
}

function get(key) {
  if (!(window && window.localStorage)) return null;
  const stringifiedObj = window.localStorage.getItem(key);
  if (!stringifiedObj) return null;
  const finalObj = JSON.parse(stringifiedObj);
  if (finalObj.createdAt + finalObj.expiry > new Date().getTime()) {
    return finalObj.value;
  }
  return null;
}

function deleteItem(key) {
  if (!(window && window.localStorage)) return null;
  return window.localStorage.removeItem(key);
}

export default {
  set,
  get,
  deleteItem,
};
